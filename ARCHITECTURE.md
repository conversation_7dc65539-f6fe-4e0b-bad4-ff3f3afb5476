# GTM Mixer MCP Server Architecture

## Overview

This project implements a hierarchical agent architecture using FastMCP (Model Context Protocol) with pydantic-ai agents. The system is designed for intuitive organization where specialized agents handle domain-specific tasks and a master agent orchestrates the delegation.

## Architecture Components

### 1. Singleton MCP Server (`shared/mcp_server.py`)
- Exports `main_mcp` as a singleton FastMCP instance
- Can be imported across all agent modules
- Centralizes tool registration using decorators

### 2. Master Agent (`agents/master_agent/agent.py`)
- **Role**: Orchestrator that delegates tasks to specialized agents
- **Tools**: Has delegation tools to communicate with other agents
- **MCP Exposure**: Exposed as `run_master_agent` tool via MCP
- **Intelligence**: Analyzes requests and routes to appropriate specialized agents

### 3. Specialized Agents

#### Workspace Agent (`agents/workspace_agent/agent.py`)
- **Domain**: Workspace management, project organization, team collaboration
- **Knowledge**: Workspace structures, project templates, team roles, permissions
- **Tools**: 
  - `run_workspace_agent`: General workspace queries
  - `create_workspace`: Create new workspaces
  - `analyze_workspace_structure`: Optimize workspace structures
  - `suggest_project_organization`: Project organization recommendations
- **Internal Tools**: Template management, validation utilities

#### Poet Agent (`agents/poet_agent/agent.py`)
- **Domain**: Creative writing, poetry, literary tasks
- **Knowledge**: Poetry styles, creative writing techniques, literary analysis
- **Tools**:
  - `run_poet_agent`: General creative writing requests
  - `create_poem`: Create poems with specific themes and styles
- **Internal Tools**: Poetry generation utilities

## How It Works

### 1. Tool Registration Pattern
```python
from shared.mcp_server import main_mcp

@main_mcp.tool
async def my_tool(param: str) -> str:
    """Tool description for MCP clients."""
    # Tool implementation
    return result
```

### 2. Agent Delegation Pattern
```python
# Master agent has delegation tools
@master_agent.tool
async def delegate_to_workspace_agent(ctx, prompt: str) -> str:
    """Delegate workspace tasks to workspace agent."""
    result = await workspace_agent.run(prompt)
    return result.output
```

### 3. Request Flow
1. **Client Request** → MCP Server → `run_master_agent`
2. **Master Agent** analyzes request and identifies domain
3. **Delegation** → Master agent calls appropriate delegation tool
4. **Specialized Agent** processes the request with domain expertise
5. **Response** → Result flows back through master agent to client

## Benefits

### 1. **Intuitive Organization**
- Each agent has clear domain boundaries
- Tools are co-located with relevant agents
- Easy to understand which agent handles what

### 2. **Scalable Architecture**
- Add new specialized agents by creating new modules
- Master agent automatically gains access to new capabilities
- No need to modify main.py for new agents

### 3. **Separation of Concerns**
- **MCP Layer**: Tool exposure and protocol handling
- **Agent Layer**: Domain logic and AI reasoning
- **Delegation Layer**: Request routing and coordination

### 4. **Flexible Delegation**
- Master agent can combine responses from multiple agents
- Specialized agents can call other specialized agents if needed
- Context and conversation history maintained

## Adding New Agents

1. **Create Agent Module**: `agents/new_agent/agent.py`
2. **Define Agent**: Create pydantic-ai Agent with domain instructions
3. **Add MCP Tools**: Use `@main_mcp.tool` decorator for external tools
4. **Add Internal Tools**: Use `@agent.tool` decorator for agent-specific tools
5. **Update Master Agent**: Add delegation tools for the new agent
6. **Import Module**: Add import to `main.py`

## Example Usage

### Direct Tool Call
```python
# Client calls workspace tool directly
result = await create_workspace(
    name="My Project", 
    description="A new project workspace",
    template="agile_software"
)
```

### Master Agent Delegation
```python
# Client asks master agent to handle workspace request
result = await run_master_agent(
    "I need to create a new agile software development workspace for my team of 5 developers working on a 12-week project"
)
# Master agent analyzes request, delegates to workspace agent, returns comprehensive response
```

## File Structure
```
├── main.py                          # Entry point, imports all agents
├── shared/
│   └── mcp_server.py               # Singleton MCP server
├── agents/
│   ├── master_agent/
│   │   └── agent.py                # Master orchestrator agent
│   ├── workspace_agent/
│   │   └── agent.py                # Workspace management specialist
│   └── poet_agent/
│       └── agent.py                # Creative writing specialist
└── ARCHITECTURE.md                 # This file
```

This architecture provides a clean, scalable foundation for building complex multi-agent systems with clear delegation patterns and intuitive organization.
