# GTM Mixer MCP Server Architecture

## Overview

This project implements a hierarchical agent architecture using FastMCP (Model Context Protocol) with pydantic-ai agents. The system is designed for intuitive organization where specialized agents handle domain-specific tasks and a master agent orchestrates the delegation.

## Architecture Components

### 1. Singleton MCP Server (`shared/mcp_server.py`)

- Exports `main_mcp` as a singleton FastMCP instance
- Can be imported across all agent modules
- Centralizes tool registration using decorators

### 2. Master Agent (`agents/master_agent/agent.py`)

- **Role**: Orchestrator that delegates tasks to specialized agents
- **Tools**: Has delegation tools to communicate with other agents
- **MCP Exposure**: Exposed as `run_master_agent` tool via MCP
- **Intelligence**: Analyzes requests and routes to appropriate specialized agents

### 3. Specialized Agents

#### Workspace Agent (`agents/workspace_agent/agent.py`)

- **Domain**: Workspace management, project organization, team collaboration
- **Knowledge**: Workspace structures, project templates, team roles, permissions
- **Tools**:
  - `run_workspace_agent`: General workspace queries
  - `create_workspace`: Create new workspaces
  - `analyze_workspace_structure`: Optimize workspace structures
  - `suggest_project_organization`: Project organization recommendations
- **Internal Tools**: Template management, validation utilities

#### Poet Agent (`agents/poet_agent/agent.py`)

- **Domain**: Creative writing, poetry, literary tasks
- **Knowledge**: Poetry styles, creative writing techniques, literary analysis
- **Tools**:
  - `run_poet_agent`: General creative writing requests
  - `create_poem`: Create poems with specific themes and styles
- **Internal Tools**: Poetry generation utilities

## How It Works

### 1. Tool Registration Pattern

```python
from shared.mcp_server import main_mcp

@main_mcp.tool
async def my_tool(param: str) -> str:
    """Tool description for MCP clients."""
    # Tool implementation
    return result
```

### 2. Dynamic Tool Discovery Pattern

```python
# Master agent automatically discovers all MCP tools
@master_agent.tool
async def call_specialized_tool(ctx, tool_name: str, **kwargs) -> str:
    """Call any available specialized tool by name."""
    tools_dict = await main_mcp.get_tools()
    if tool_name not in tools_dict:
        return f"Tool '{tool_name}' not found"

    result = await main_mcp._tool_manager.call_tool(tool_name, kwargs)
    return str(result)

@master_agent.tool
async def list_available_tools(ctx) -> str:
    """List all available specialized tools."""
    tools_dict = await main_mcp.get_tools()
    # Returns formatted list of all available tools
```

### 3. Request Flow

1. **Client Request** → MCP Server → `run_master_agent`
2. **Master Agent** analyzes request and identifies required tools
3. **Dynamic Discovery** → Master agent calls `list_available_tools` to see options
4. **Tool Execution** → Master agent calls `call_specialized_tool` with appropriate tool name and parameters
5. **Specialized Agent** processes the request with domain expertise
6. **Response** → Result flows back through master agent to client

## Benefits

### 1. **Intuitive Organization**

- Each agent has clear domain boundaries
- Tools are co-located with relevant agents
- Easy to understand which agent handles what

### 2. **Scalable Architecture**

- Add new specialized agents by creating new modules
- Master agent **automatically discovers** all available tools
- No need to modify master agent when adding new tools
- No manual delegation wrapper functions needed

### 3. **Separation of Concerns**

- **MCP Layer**: Tool exposure and protocol handling
- **Agent Layer**: Domain logic and AI reasoning
- **Dynamic Discovery Layer**: Automatic tool discovery and routing

### 4. **Zero-Maintenance Delegation**

- Master agent automatically sees all registered MCP tools
- No need to create wrapper functions for each new tool
- Scales to hundreds of tools without code changes
- Perfect for bundling as a tool for other agents

## Adding New Agents

1. **Create Agent Module**: `agents/new_agent/agent.py`
2. **Define Agent**: Create pydantic-ai Agent with domain instructions
3. **Add MCP Tools**: Use `@main_mcp.tool` decorator for external tools
4. **Add Internal Tools**: Use `@agent.tool` decorator for agent-specific tools
5. **Update Master Agent**: Add delegation tools for the new agent
6. **Import Module**: Add import to `main.py`

## Example Usage

### Direct Tool Call

```python
# Client calls workspace tool directly
result = await create_workspace(
    name="My Project",
    description="A new project workspace",
    template="agile_software"
)
```

### Master Agent Delegation

```python
# Client asks master agent to handle workspace request
result = await run_master_agent(
    "I need to create a new agile software development workspace for my team of 5 developers working on a 12-week project"
)
# Master agent analyzes request, delegates to workspace agent, returns comprehensive response
```

## File Structure

```
├── main.py                          # Entry point, imports all agents
├── shared/
│   └── mcp_server.py               # Singleton MCP server
├── agents/
│   ├── master_agent/
│   │   └── agent.py                # Master orchestrator agent
│   ├── workspace_agent/
│   │   └── agent.py                # Workspace management specialist
│   └── poet_agent/
│       └── agent.py                # Creative writing specialist
└── ARCHITECTURE.md                 # This file
```

This architecture provides a clean, scalable foundation for building complex multi-agent systems with clear delegation patterns and intuitive organization.
