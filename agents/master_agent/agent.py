from pydantic_ai import Agent

from shared.instructions import SHARED_INSTRUCTIONS
from shared.secrets import DEFAULT_MODEL
from shared.mcp_server import main_mcp

MASTER_AGENT_INSTRUCTIONS = """
{shared_instructions}

You are the Master Agent. Your job is to delegate tasks to other agents and coordinate their responses.

Available specialized agents and their tools:

1. WORKSPACE AGENT - For workspace and project management:
   - run_workspace_agent: General workspace queries and advice
   - create_workspace: Create new workspaces with specific configurations
   - analyze_workspace_structure: Analyze and optimize workspace structures
   - suggest_project_organization: Get project organization recommendations

2. POET AGENT - For creative writing and poetry:
   - run_poet_agent: General creative writing requests
   - create_poem: Create poems with specific themes and styles

DELEGATION STRATEGY:
- Analyze the user's request to identify the domain (workspace, creative, etc.)
- Use the appropriate specialized agent's tools
- If the request spans multiple domains, coordinate between agents
- Always provide context and clear instructions when delegating
- Synthesize responses from multiple agents when needed

WORKSPACE-RELATED KEYWORDS: workspace, project, organization, team, management, structure, planning, collaboration, permissions, resources
CREATIVE-RELATED KEYWORDS: poem, poetry, creative, writing, story, literary, artistic, imagination
""".format(
    shared_instructions=SHARED_INSTRUCTIONS
)

# Import the specialized agents for delegation
from agents.workspace_agent.agent import workspace_agent
from agents.poet_agent.agent import poet_agent

master_agent = Agent(
    DEFAULT_MODEL, system_prompt=MASTER_AGENT_INSTRUCTIONS, name="MasterAgent"
)


# Add delegation tools to the master agent
@master_agent.tool
async def delegate_to_workspace_agent(ctx, prompt: str) -> str:
    """Delegate workspace-related tasks to the workspace agent."""
    result = await workspace_agent.run(prompt)
    return result.output


@master_agent.tool
async def delegate_to_poet_agent(ctx, prompt: str) -> str:
    """Delegate creative writing and poetry tasks to the poet agent."""
    result = await poet_agent.run(prompt)
    return result.output


@master_agent.tool
async def create_workspace_via_delegation(
    ctx, name: str, description: str = "", template: str = "default"
) -> str:
    """Create a workspace by delegating to the workspace agent."""
    prompt = f"""
    Create a new workspace with the following specifications:
    - Name: {name}
    - Description: {description}
    - Template: {template}

    Provide a detailed plan for setting up this workspace.
    """
    result = await workspace_agent.run(prompt)
    return result.output


@master_agent.tool
async def create_poem_via_delegation(ctx, theme: str, style: str = "free verse") -> str:
    """Create a poem by delegating to the poet agent."""
    prompt = f"Write a {style} poem about {theme}"
    result = await poet_agent.run(prompt)
    return result.output


@main_mcp.tool
async def run_master_agent(prompt: str) -> str:
    """
    Run the master orchestrator agent that delegates tasks to specialized agents.

    Args:
        prompt: The user's request or question

    Returns:
        The response from the master agent after delegation
    """
    result = await master_agent.run(prompt)
    return result.output
