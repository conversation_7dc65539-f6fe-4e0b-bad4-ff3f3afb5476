from pydantic_ai import Agent

from shared.instructions import SHARED_INSTRUCTIONS
from shared.secrets import DEFAULT_MODEL
from shared.mcp_server import main_mcp


async def get_dynamic_master_instructions():
    """Generate dynamic master agent instructions based on available tools."""
    base_instructions = """
{shared_instructions}

You are the Master Agent. Your job is to delegate tasks to other agents and coordinate their responses.

CORE CAPABILITIES:
You have two powerful tools for delegation:
1. list_available_tools() - See all available specialized tools
2. call_specialized_tool(tool_name, **kwargs) - Call any tool by name

DELEGATION STRATEGY:
1. When you receive a request, first call list_available_tools() to see what's available
2. Analyze the user's request to identify the most appropriate tool(s)
3. Use call_specialized_tool() to execute the appropriate tool with the right parameters
4. If the request spans multiple domains, coordinate between multiple tools
5. Always provide context and synthesize responses when needed

AVAILABLE SPECIALIZED TOOLS:
""".format(
        shared_instructions=SHARED_INSTRUCTIONS
    )

    try:
        # Get current tools dynamically
        tools_dict = await main_mcp.get_tools()
        tools_info = []

        for tool_name, tool in tools_dict.items():
            if tool_name not in ["run_master_agent"]:  # Exclude self
                description = getattr(tool, "description", "No description available")
                tools_info.append(f"- {tool_name}: {description}")

        if tools_info:
            base_instructions += "\n".join(tools_info)
        else:
            base_instructions += "No specialized tools currently available."

        base_instructions += """

IMPORTANT: Always use your delegation tools rather than trying to handle specialized tasks yourself.
You are an orchestrator, not a specialist in any particular domain.
"""

    except Exception as e:
        # Fallback if tool discovery fails
        base_instructions += f"Tool discovery unavailable: {e}"

    return base_instructions


# Initialize with basic instructions (will be updated dynamically)
MASTER_AGENT_INSTRUCTIONS = """
{shared_instructions}

You are the Master Agent. Your job is to delegate tasks to other agents.
Use your delegation tools to discover and call specialized tools.
""".format(
    shared_instructions=SHARED_INSTRUCTIONS
)

from shared.mcp_server import main_mcp


master_agent = Agent(
    DEFAULT_MODEL, system_prompt=MASTER_AGENT_INSTRUCTIONS, name="MasterAgent"
)


@master_agent.tool
async def update_master_instructions(ctx) -> str:
    """Update the master agent's instructions with current available tools."""
    try:
        new_instructions = await get_dynamic_master_instructions()
        # Note: In practice, you'd need to recreate the agent or update its system prompt
        # This is a demonstration of the concept
        return f"Instructions updated with current tools. New instruction length: {len(new_instructions)} characters"
    except Exception as e:
        return f"Error updating instructions: {str(e)}"


@master_agent.tool
async def call_specialized_tool(ctx, tool_name: str, **kwargs) -> str:
    """
    Call any available specialized tool by name with the provided arguments.

    Args:
        tool_name: The name of the tool to call
        **kwargs: Arguments to pass to the tool

    Returns:
        The result from the specialized tool
    """
    try:
        tools_dict = await main_mcp.get_tools()
        if tool_name not in tools_dict:
            available_tools = list(tools_dict.keys())
            return f"Tool '{tool_name}' not found. Available tools: {available_tools}"

        # Use the tool manager to call the tool
        result = await main_mcp._tool_manager.call_tool(tool_name, kwargs)
        return str(result)
    except Exception as e:
        return f"Error calling tool '{tool_name}': {str(e)}"


@master_agent.tool
async def list_available_tools(ctx) -> str:
    """List all available specialized tools that can be called."""
    try:
        tools_dict = await main_mcp.get_tools()
        tools_list = []

        for tool_name, tool in tools_dict.items():
            description = getattr(tool, "description", "No description available")
            tools_list.append(f"- {tool_name}: {description}")

        return "Available specialized tools:\n" + "\n".join(tools_list)
    except Exception as e:
        return f"Error listing tools: {str(e)}"


@main_mcp.tool
async def run_master_agent(prompt: str) -> str:
    """
    Run the master orchestrator agent that delegates tasks to specialized agents.

    Args:
        prompt: The user's request or question

    Returns:
        The response from the master agent after delegation
    """
    result = await master_agent.run(prompt)
    return result.output
