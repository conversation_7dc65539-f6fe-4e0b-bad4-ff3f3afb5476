from pydantic_ai import Agent

from shared.instructions import SHARED_INSTRUCTIONS
from shared.secrets import DEFAULT_MODEL
from shared.mcp_server import main_mcp

MASTER_AGENT_INSTRUCTIONS = """
{shared_instructions}

You are the Master Agent. Your job is to delegate tasks to other agents and coordinate their responses.

Available specialized agents and their tools:

1. WORKSPACE AGENT - For workspace and project management:
   - run_workspace_agent: General workspace queries and advice
   - create_workspace: Create new workspaces with specific configurations
   - analyze_workspace_structure: Analyze and optimize workspace structures
   - suggest_project_organization: Get project organization recommendations

2. POET AGENT - For creative writing and poetry:
   - run_poet_agent: General creative writing requests
   - create_poem: Create poems with specific themes and styles

DELEGATION STRATEGY:
- Analyze the user's request to identify the domain (workspace, creative, etc.)
- Use the appropriate specialized agent's tools
- If the request spans multiple domains, coordinate between agents
- Always provide context and clear instructions when delegating
- Synthesize responses from multiple agents when needed

WORKSPACE-RELATED KEYWORDS: workspace, project, organization, team, management, structure, planning, collaboration, permissions, resources
CREATIVE-RELATED KEYWORDS: poem, poetry, creative, writing, story, literary, artistic, imagination
""".format(
    shared_instructions=SHARED_INSTRUCTIONS
)

from shared.mcp_server import main_mcp


master_agent = Agent(
    DEFAULT_MODEL, system_prompt=MASTER_AGENT_INSTRUCTIONS, name="MasterAgent"
)


@master_agent.tool
async def call_specialized_tool(ctx, tool_name: str, **kwargs) -> str:
    """
    Call any available specialized tool by name with the provided arguments.

    Args:
        tool_name: The name of the tool to call
        **kwargs: Arguments to pass to the tool

    Returns:
        The result from the specialized tool
    """
    try:
        tools_dict = await main_mcp.get_tools()
        if tool_name not in tools_dict:
            available_tools = list(tools_dict.keys())
            return f"Tool '{tool_name}' not found. Available tools: {available_tools}"

        # Use the tool manager to call the tool
        result = await main_mcp._tool_manager.call_tool(tool_name, kwargs)
        return str(result)
    except Exception as e:
        return f"Error calling tool '{tool_name}': {str(e)}"


@master_agent.tool
async def list_available_tools(ctx) -> str:
    """List all available specialized tools that can be called."""
    try:
        tools_dict = await main_mcp.get_tools()
        tools_list = []

        for tool_name, tool in tools_dict.items():
            description = getattr(tool, "description", "No description available")
            tools_list.append(f"- {tool_name}: {description}")

        return "Available specialized tools:\n" + "\n".join(tools_list)
    except Exception as e:
        return f"Error listing tools: {str(e)}"


@main_mcp.tool
async def run_master_agent(prompt: str) -> str:
    """
    Run the master orchestrator agent that delegates tasks to specialized agents.

    Args:
        prompt: The user's request or question

    Returns:
        The response from the master agent after delegation
    """
    result = await master_agent.run(prompt)
    return result.output
