from pydantic_ai import Agent

from shared.instructions import SHARED_INSTRUCTIONS
from agents.poet_agent.agent import poet_agent
from shared.secrets import DEFAULT_MODEL

MASTER_AGENT_INSTRUCTIONS = """
{shared_instructions}

You are the Master Agent. Your job is to delegate tasks to other agents.
""".format(
    shared_instructions=SHARED_INSTRUCTIONS
)

master_agent = Agent(DEFAULT_MODEL, system_prompt=MASTER_AGENT_INSTRUCTIONS)
