"""
Workspace Agent - Specialized agent for workspace management and organization.
This agent knows everything about workspaces, projects, and organizational structures.
"""

from pydantic_ai import Agent
from typing import Dict, List, Any

from shared.secrets import DEFAULT_MODEL
from shared.mcp_server import main_mcp

WORKSPACE_AGENT_INSTRUCTIONS = """
You are the Workspace Agent. You are an expert in workspace management and organization.

Your expertise includes:
- Creating and managing workspaces
- Project organization and structure
- Team collaboration and permissions
- Workspace optimization and best practices
- Resource allocation and management
- Workflow design and implementation

You have access to tools for:
- Creating, updating, and deleting workspaces
- Managing projects within workspaces
- Handling permissions and access control
- Organizing resources and assets
- Generating workspace reports and analytics

When responding to workspace-related queries, provide detailed, actionable advice
and use the appropriate tools to perform the requested operations.
"""

workspace_agent = Agent(
    DEFAULT_MODEL,
    system_prompt=WORKSPACE_AGENT_INSTRUCTIONS,
    name="WorkspaceAgent"
)


# Workspace Agent Tools (exposed via MCP)
@main_mcp.tool
async def run_workspace_agent(prompt: str) -> str:
    """
    Run the workspace agent for workspace management tasks.
    
    Args:
        prompt: The workspace-related request or question
        
    Returns:
        The response from the workspace agent
    """
    result = await workspace_agent.run(prompt)
    return result.output


@main_mcp.tool
async def create_workspace(name: str, description: str = "", template: str = "default") -> Dict[str, Any]:
    """
    Create a new workspace with the specified configuration.
    
    Args:
        name: The name of the workspace
        description: Optional description of the workspace
        template: The template to use (default, agile, creative, etc.)
        
    Returns:
        Workspace creation result with details
    """
    prompt = f"""
    Create a new workspace with the following specifications:
    - Name: {name}
    - Description: {description}
    - Template: {template}
    
    Provide a detailed plan for setting up this workspace including:
    - Recommended project structure
    - Initial configuration settings
    - Suggested team roles and permissions
    - Best practices for this type of workspace
    """
    
    result = await workspace_agent.run(prompt)
    return {
        "status": "success",
        "workspace_name": name,
        "description": description,
        "template": template,
        "setup_plan": result.output
    }


@main_mcp.tool
async def analyze_workspace_structure(workspace_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Analyze a workspace structure and provide optimization recommendations.
    
    Args:
        workspace_data: Dictionary containing workspace information
        
    Returns:
        Analysis results with recommendations
    """
    prompt = f"""
    Analyze the following workspace structure and provide optimization recommendations:
    
    Workspace Data: {workspace_data}
    
    Please provide:
    1. Current structure analysis
    2. Identified inefficiencies or issues
    3. Optimization recommendations
    4. Implementation steps
    5. Expected benefits
    """
    
    result = await workspace_agent.run(prompt)
    return {
        "status": "analyzed",
        "workspace_data": workspace_data,
        "analysis": result.output
    }


@main_mcp.tool
async def suggest_project_organization(project_type: str, team_size: int, duration_weeks: int) -> Dict[str, Any]:
    """
    Get project organization suggestions based on project parameters.
    
    Args:
        project_type: Type of project (software, marketing, research, etc.)
        team_size: Number of team members
        duration_weeks: Expected project duration in weeks
        
    Returns:
        Project organization recommendations
    """
    prompt = f"""
    Suggest an optimal project organization for:
    - Project Type: {project_type}
    - Team Size: {team_size} members
    - Duration: {duration_weeks} weeks
    
    Include recommendations for:
    1. Project structure and phases
    2. Team roles and responsibilities
    3. Communication workflows
    4. Milestone planning
    5. Resource allocation
    6. Risk management strategies
    """
    
    result = await workspace_agent.run(prompt)
    return {
        "project_type": project_type,
        "team_size": team_size,
        "duration_weeks": duration_weeks,
        "recommendations": result.output
    }


# Internal tools for the workspace agent (not exposed via MCP)
@workspace_agent.tool
def get_workspace_templates(ctx) -> List[str]:
    """Get available workspace templates."""
    return [
        "default",
        "agile_software",
        "creative_agency", 
        "research_lab",
        "marketing_team",
        "startup",
        "enterprise"
    ]


@workspace_agent.tool
def validate_workspace_name(ctx, name: str) -> Dict[str, Any]:
    """Validate workspace name according to naming conventions."""
    issues = []
    
    if len(name) < 3:
        issues.append("Name too short (minimum 3 characters)")
    if len(name) > 50:
        issues.append("Name too long (maximum 50 characters)")
    if not name.replace("_", "").replace("-", "").isalnum():
        issues.append("Name contains invalid characters (only letters, numbers, hyphens, and underscores allowed)")
    
    return {
        "valid": len(issues) == 0,
        "issues": issues,
        "suggestions": [
            "Use descriptive names",
            "Avoid special characters",
            "Use underscores or hyphens for spaces"
        ] if issues else []
    }
