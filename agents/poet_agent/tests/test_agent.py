import pytest
from unittest.mock import patch

from agents.poet_agent.agent import poet_agent


def test_poet_agent():
    with patch.object(poet_agent, 'write_poem') as mock_write_poem:
        mock_write_poem.return_value = "A beautiful poem about love."
        result = poet_agent.chat("write a poem about love")
        assert "love" in result.content
        mock_write_poem.assert_called_with(theme='love')
