from pydantic_ai import Agent

from shared.secrets import DEFAULT_MODEL
from shared.mcp_server import main_mcp

POET_AGENT_INSTRUCTIONS = """
You are a poet. You write beautiful poems, creative content, and literary works.
You specialize in:
- Writing poems on various themes
- Creative writing
- Literary analysis
- Storytelling
- Wordplay and linguistic creativity
"""

poet_agent = Agent(
    DEFAULT_MODEL, system_prompt=POET_AGENT_INSTRUCTIONS, name="PoetAgent"
)


@poet_agent.tool
def write_poem(ctx, theme: str) -> str:
    """Writes a poem about a given theme."""
    return f"A beautiful poem about {theme}."


@main_mcp.tool
async def run_poet_agent(prompt: str) -> str:
    """
    Run the poet agent for creative writing and poetry tasks.

    Args:
        prompt: The creative writing request or poetry theme

    Returns:
        The creative response from the poet agent
    """
    result = await poet_agent.run(prompt)
    return result.output


@main_mcp.tool
async def create_poem(theme: str, style: str = "free verse") -> str:
    """
    Create a poem on a specific theme and style.

    Args:
        theme: The theme or subject for the poem
        style: The style of poem (free verse, sonnet, haiku, etc.)

    Returns:
        A poem created by the poet agent
    """
    prompt = f"Write a {style} poem about {theme}"
    result = await poet_agent.run(prompt)
    return result.output
