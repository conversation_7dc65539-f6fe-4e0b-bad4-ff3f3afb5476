# GTM Mixer MCP Server Environment Configuration
# Copy this file to .env and fill in your values

# AI Model Configuration
# Uncomment and set the model you want to use
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# GOOGLE_API_KEY=your_google_api_key_here

# Default model to use (update in shared/secrets.py if needed)
# DEFAULT_MODEL=openai:gpt-4o
# DEFAULT_MODEL=anthropic:claude-3-sonnet-20240229
# DEFAULT_MODEL=google:gemini-1.5-pro

# MCP Server Configuration
MCP_SERVER_NAME=TagRabbit MCP Server
MCP_SERVER_HOST=127.0.0.1
MCP_SERVER_PORT=8000

# Logging Configuration
LOG_LEVEL=INFO
DEBUG=false

# Optional: Custom configurations for specific agents
# Add your agent-specific environment variables here
