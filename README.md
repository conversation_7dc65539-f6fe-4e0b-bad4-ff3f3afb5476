# GTM Mixer MCP Server

A scalable, hierarchical agent architecture using FastMCP (Model Context Protocol) with pydantic-ai agents. Features dynamic tool discovery and zero-maintenance delegation.

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install pydantic-ai fastmcp python-dotenv
   ```

2. **Run the Server**
   ```bash
   python main.py
   ```

3. **Test the Architecture**
   ```bash
   python test_architecture.py
   ```

## 📁 Project Structure

```
├── main.py                          # Entry point with auto-discovery
├── shared/
│   └── mcp_server.py               # Singleton MCP server
├── agents/
│   ├── master_agent/
│   │   └── agent.py                # Master orchestrator with dynamic delegation
│   ├── workspace_agent/
│   │   └── agent.py                # Workspace management specialist
│   └── poet_agent/
│       └── agent.py                # Creative writing specialist
├── ARCHITECTURE.md                 # Detailed architecture documentation
└── README.md                       # This file
```

## ⚠️ **CRITICAL REQUIREMENT: agent.py Files**

**Each agent folder MUST contain an `agent.py` file.** This is not optional.

### Why agent.py is Required

1. **Auto-Discovery**: The main.py auto-discovery system looks for `agents/{folder}/agent.py`
2. **Tool Registration**: The `@main_mcp.tool` decorators in agent.py register tools with the MCP server
3. **Import Convention**: The system expects `agents.{folder}.agent` module structure

### Creating New Agents

When adding a new agent, follow this exact structure:

```
agents/
└── your_new_agent/
    └── agent.py          # ← REQUIRED FILE NAME
```

**Example agent.py template:**

```python
from pydantic_ai import Agent
from shared.secrets import DEFAULT_MODEL
from shared.mcp_server import main_mcp

# Define your agent's instructions
YOUR_AGENT_INSTRUCTIONS = """
You are a specialist in [your domain].
Your expertise includes:
- [capability 1]
- [capability 2]
"""

# Create the agent
your_agent = Agent(
    DEFAULT_MODEL,
    system_prompt=YOUR_AGENT_INSTRUCTIONS,
    name="YourAgent"
)

# Register MCP tools using decorators
@main_mcp.tool
async def your_main_tool(param: str) -> str:
    """Description of what this tool does."""
    result = await your_agent.run(f"Handle this request: {param}")
    return result.output

@main_mcp.tool
async def your_specific_tool(specific_param: str, optional_param: str = "default") -> dict:
    """Another tool with specific functionality."""
    # Your tool logic here
    return {"result": "success", "data": specific_param}

# Add internal agent tools if needed
@your_agent.tool
def internal_helper(ctx, data: str) -> str:
    """Internal tool only available to this agent."""
    return f"Processed: {data}"
```

## 🏗️ Architecture Features

### 1. **Dynamic Tool Discovery**
- Master agent automatically discovers all available tools
- No manual wrapper functions needed
- Scales to hundreds of tools without code changes

### 2. **Zero-Maintenance Delegation**
- Add new agents → They're automatically available to master agent
- No need to modify master agent code
- Perfect for bundling as a tool for other agents

### 3. **Intuitive Organization**
- Each agent owns its domain and related tools
- Tools are co-located with relevant agents
- Clear separation of concerns

## 🔧 Master Agent Capabilities

The master agent has dynamic delegation tools:

- `list_available_tools()` - See all available specialized tools
- `call_specialized_tool(tool_name, **kwargs)` - Call any tool by name
- `update_master_instructions()` - Refresh instructions with current tools

## 📝 Usage Examples

### Direct Tool Access
```python
# Call workspace tool directly
result = await create_workspace(
    name="My Project", 
    description="A new project workspace",
    template="agile_software"
)
```

### Master Agent Orchestration
```python
# Let master agent handle complex requests
result = await run_master_agent(
    "I need to create a new agile software development workspace for my team of 5 developers working on a 12-week project, and then write a poem about successful teamwork"
)
# Master agent will:
# 1. List available tools
# 2. Call create_workspace with appropriate parameters
# 3. Call create_poem for the poetry request
# 4. Synthesize and return comprehensive response
```

## 🚨 Common Issues

### "Agent not found" Error
- **Cause**: Missing `agent.py` file in agent folder
- **Solution**: Ensure every agent folder has an `agent.py` file

### "Tool not registered" Error
- **Cause**: Agent module not imported
- **Solution**: Check that auto-discovery is working or add explicit import

### "Import failed" Error
- **Cause**: Syntax error in agent.py file
- **Solution**: Check agent.py for Python syntax errors

## 🔄 Adding New Agents

1. **Create Agent Folder**: `mkdir agents/new_agent`
2. **Create agent.py**: Follow the template above
3. **Define Tools**: Use `@main_mcp.tool` decorators
4. **Test**: Run `python test_architecture.py` to verify

The master agent will automatically discover and gain access to your new tools!

## 📚 Further Reading

- See `ARCHITECTURE.md` for detailed technical documentation
- See `test_architecture.py` for working examples
- Each agent.py file contains inline documentation
