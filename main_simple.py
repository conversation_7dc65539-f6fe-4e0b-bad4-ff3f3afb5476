import asyncio
from dotenv import load_dotenv

# Import agents to register their tools (just import, don't assign)
import agents.master_agent.agent
import agents.poet_agent.agent
import agents.workspace_agent.agent
# Add new agents here as you create them

from shared.mcp_server import main_mcp

load_dotenv()


async def setup():
    """Setup function to initialize any async components if needed."""
    pass


if __name__ == "__main__":
    asyncio.run(setup())
    main_mcp.run()
