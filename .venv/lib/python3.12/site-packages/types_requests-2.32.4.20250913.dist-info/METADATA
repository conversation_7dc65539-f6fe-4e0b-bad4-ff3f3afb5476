Metadata-Version: 2.4
Name: types-requests
Version: 2.32.4.20250913
Summary: Typing stubs for requests
License-Expression: Apache-2.0
Project-URL: Homepage, https://github.com/python/typeshed
Project-URL: GitHub, https://github.com/python/typeshed
Project-URL: Changes, https://github.com/typeshed-internal/stub_uploader/blob/main/data/changelogs/requests.md
Project-URL: Issue tracker, https://github.com/python/typeshed/issues
Project-URL: Chat, https://gitter.im/python/typing
Classifier: Programming Language :: Python :: 3
Classifier: Typing :: Stubs Only
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: urllib3>=2
Dynamic: license-file

## Typing stubs for requests

This is a [type stub package](https://typing.python.org/en/latest/tutorials/external_libraries.html)
for the [`requests`](https://github.com/psf/requests) package. It can be used by type checkers
to check code that uses `requests`. This version of
`types-requests` aims to provide accurate annotations for
`requests~=2.32.4`.

Note: `types-requests` has required `urllib3>=2` since v********. If you need to install `types-requests` into an environment that must also have `urllib3<2` installed into it, you will have to use `types-requests<********`.

This package is part of the [typeshed project](https://github.com/python/typeshed).
All fixes for types and metadata should be contributed there.
See [the README](https://github.com/python/typeshed/blob/main/README.md)
for more details. The source for this package can be found in the
[`stubs/requests`](https://github.com/python/typeshed/tree/main/stubs/requests)
directory.

This package was tested with the following type checkers:
* [mypy](https://github.com/python/mypy/) 1.17.1
* [pyright](https://github.com/microsoft/pyright) 1.1.405

It was generated from typeshed commit
[`59c36c8bf960b1b7609af5eee3b17ecde6c48864`](https://github.com/python/typeshed/commit/59c36c8bf960b1b7609af5eee3b17ecde6c48864).
