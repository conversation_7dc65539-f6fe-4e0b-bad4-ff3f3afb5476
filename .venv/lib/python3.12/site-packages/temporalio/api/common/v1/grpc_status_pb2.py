# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/common/v1/grpc_status.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import any_pb2 as google_dot_protobuf_dot_any__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n(temporal/api/common/v1/grpc_status.proto\x12\x16temporal.api.common.v1\x1a\x19google/protobuf/any.proto"R\n\nGrpcStatus\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\x07\x64\x65tails\x18\x03 \x03(\x0b\x32\x14.google.protobuf.Anyb\x06proto3'
)


_GRPCSTATUS = DESCRIPTOR.message_types_by_name["GrpcStatus"]
GrpcStatus = _reflection.GeneratedProtocolMessageType(
    "GrpcStatus",
    (_message.Message,),
    {
        "DESCRIPTOR": _GRPCSTATUS,
        "__module__": "temporal.api.common.v1.grpc_status_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.GrpcStatus)
    },
)
_sym_db.RegisterMessage(GrpcStatus)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    _GRPCSTATUS._serialized_start = 95
    _GRPCSTATUS._serialized_end = 177
# @@protoc_insertion_point(module_scope)
