"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys

import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.empty_pb2
import google.protobuf.internal.containers
import google.protobuf.message

import temporalio.api.enums.v1.common_pb2
import temporalio.api.enums.v1.event_type_pb2
import temporalio.api.enums.v1.reset_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class DataBlob(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENCODING_TYPE_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    encoding_type: temporalio.api.enums.v1.common_pb2.EncodingType.ValueType
    data: builtins.bytes
    def __init__(
        self,
        *,
        encoding_type: temporalio.api.enums.v1.common_pb2.EncodingType.ValueType = ...,
        data: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "data", b"data", "encoding_type", b"encoding_type"
        ],
    ) -> None: ...

global___DataBlob = DataBlob

class Payloads(google.protobuf.message.Message):
    """See `Payload`"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAYLOADS_FIELD_NUMBER: builtins.int
    @property
    def payloads(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___Payload
    ]: ...
    def __init__(
        self,
        *,
        payloads: collections.abc.Iterable[global___Payload] | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["payloads", b"payloads"]
    ) -> None: ...

global___Payloads = Payloads

class Payload(google.protobuf.message.Message):
    """Represents some binary (byte array) data (ex: activity input parameters or workflow result) with
    metadata which describes this binary data (format, encoding, encryption, etc). Serialization
    of the data may be user-defined.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class MetadataEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.bytes
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.bytes = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    METADATA_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    @property
    def metadata(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[
        builtins.str, builtins.bytes
    ]: ...
    data: builtins.bytes
    def __init__(
        self,
        *,
        metadata: collections.abc.Mapping[builtins.str, builtins.bytes] | None = ...,
        data: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["data", b"data", "metadata", b"metadata"],
    ) -> None: ...

global___Payload = Payload

class SearchAttributes(google.protobuf.message.Message):
    """A user-defined set of *indexed* fields that are used/exposed when listing/searching workflows.
    The payload is not serialized in a user-defined way.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class IndexedFieldsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___Payload: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___Payload | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["value", b"value"]
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    INDEXED_FIELDS_FIELD_NUMBER: builtins.int
    @property
    def indexed_fields(
        self,
    ) -> google.protobuf.internal.containers.MessageMap[
        builtins.str, global___Payload
    ]: ...
    def __init__(
        self,
        *,
        indexed_fields: collections.abc.Mapping[builtins.str, global___Payload]
        | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["indexed_fields", b"indexed_fields"]
    ) -> None: ...

global___SearchAttributes = SearchAttributes

class Memo(google.protobuf.message.Message):
    """A user-defined set of *unindexed* fields that are exposed when listing/searching workflows"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class FieldsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___Payload: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___Payload | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["value", b"value"]
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    FIELDS_FIELD_NUMBER: builtins.int
    @property
    def fields(
        self,
    ) -> google.protobuf.internal.containers.MessageMap[
        builtins.str, global___Payload
    ]: ...
    def __init__(
        self,
        *,
        fields: collections.abc.Mapping[builtins.str, global___Payload] | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["fields", b"fields"]
    ) -> None: ...

global___Memo = Memo

class Header(google.protobuf.message.Message):
    """Contains metadata that can be attached to a variety of requests, like starting a workflow, and
    can be propagated between, for example, workflows and activities.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class FieldsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___Payload: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___Payload | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["value", b"value"]
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    FIELDS_FIELD_NUMBER: builtins.int
    @property
    def fields(
        self,
    ) -> google.protobuf.internal.containers.MessageMap[
        builtins.str, global___Payload
    ]: ...
    def __init__(
        self,
        *,
        fields: collections.abc.Mapping[builtins.str, global___Payload] | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["fields", b"fields"]
    ) -> None: ...

global___Header = Header

class WorkflowExecution(google.protobuf.message.Message):
    """Identifies a specific workflow within a namespace. Practically speaking, because run_id is a
    uuid, a workflow execution is globally unique. Note that many commands allow specifying an empty
    run id as a way of saying "target the latest run of the workflow".
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WORKFLOW_ID_FIELD_NUMBER: builtins.int
    RUN_ID_FIELD_NUMBER: builtins.int
    workflow_id: builtins.str
    run_id: builtins.str
    def __init__(
        self,
        *,
        workflow_id: builtins.str = ...,
        run_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "run_id", b"run_id", "workflow_id", b"workflow_id"
        ],
    ) -> None: ...

global___WorkflowExecution = WorkflowExecution

class WorkflowType(google.protobuf.message.Message):
    """Represents the identifier used by a workflow author to define the workflow. Typically, the
    name of a function. This is sometimes referred to as the workflow's "name"
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["name", b"name"]
    ) -> None: ...

global___WorkflowType = WorkflowType

class ActivityType(google.protobuf.message.Message):
    """Represents the identifier used by a activity author to define the activity. Typically, the
    name of a function. This is sometimes referred to as the activity's "name"
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    name: builtins.str
    def __init__(
        self,
        *,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["name", b"name"]
    ) -> None: ...

global___ActivityType = ActivityType

class RetryPolicy(google.protobuf.message.Message):
    """How retries ought to be handled, usable by both workflows and activities"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INITIAL_INTERVAL_FIELD_NUMBER: builtins.int
    BACKOFF_COEFFICIENT_FIELD_NUMBER: builtins.int
    MAXIMUM_INTERVAL_FIELD_NUMBER: builtins.int
    MAXIMUM_ATTEMPTS_FIELD_NUMBER: builtins.int
    NON_RETRYABLE_ERROR_TYPES_FIELD_NUMBER: builtins.int
    @property
    def initial_interval(self) -> google.protobuf.duration_pb2.Duration:
        """Interval of the first retry. If retryBackoffCoefficient is 1.0 then it is used for all retries."""
    backoff_coefficient: builtins.float
    """Coefficient used to calculate the next retry interval.
    The next retry interval is previous interval multiplied by the coefficient.
    Must be 1 or larger.
    """
    @property
    def maximum_interval(self) -> google.protobuf.duration_pb2.Duration:
        """Maximum interval between retries. Exponential backoff leads to interval increase.
        This value is the cap of the increase. Default is 100x of the initial interval.
        """
    maximum_attempts: builtins.int
    """Maximum number of attempts. When exceeded the retries stop even if not expired yet.
    1 disables retries. 0 means unlimited (up to the timeouts)
    """
    @property
    def non_retryable_error_types(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """Non-Retryable errors types. Will stop retrying if the error type matches this list. Note that
        this is not a substring match, the error *type* (not message) must match exactly.
        """
    def __init__(
        self,
        *,
        initial_interval: google.protobuf.duration_pb2.Duration | None = ...,
        backoff_coefficient: builtins.float = ...,
        maximum_interval: google.protobuf.duration_pb2.Duration | None = ...,
        maximum_attempts: builtins.int = ...,
        non_retryable_error_types: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "initial_interval",
            b"initial_interval",
            "maximum_interval",
            b"maximum_interval",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "backoff_coefficient",
            b"backoff_coefficient",
            "initial_interval",
            b"initial_interval",
            "maximum_attempts",
            b"maximum_attempts",
            "maximum_interval",
            b"maximum_interval",
            "non_retryable_error_types",
            b"non_retryable_error_types",
        ],
    ) -> None: ...

global___RetryPolicy = RetryPolicy

class MeteringMetadata(google.protobuf.message.Message):
    """Metadata relevant for metering purposes"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NONFIRST_LOCAL_ACTIVITY_EXECUTION_ATTEMPTS_FIELD_NUMBER: builtins.int
    nonfirst_local_activity_execution_attempts: builtins.int
    """Count of local activities which have begun an execution attempt during this workflow task,
    and whose first attempt occurred in some previous task. This is used for metering
    purposes, and does not affect workflow state.

    (-- api-linter: core::0141::forbidden-types=disabled
        aip.dev/not-precedent: Negative values make no sense to represent. --)
    """
    def __init__(
        self,
        *,
        nonfirst_local_activity_execution_attempts: builtins.int = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "nonfirst_local_activity_execution_attempts",
            b"nonfirst_local_activity_execution_attempts",
        ],
    ) -> None: ...

global___MeteringMetadata = MeteringMetadata

class WorkerVersionStamp(google.protobuf.message.Message):
    """Deprecated. This message is replaced with `Deployment` and `VersioningBehavior`.
    Identifies the version(s) of a worker that processed a task
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BUILD_ID_FIELD_NUMBER: builtins.int
    USE_VERSIONING_FIELD_NUMBER: builtins.int
    build_id: builtins.str
    """An opaque whole-worker identifier. Replaces the deprecated `binary_checksum` field when this
    message is included in requests which previously used that.
    """
    use_versioning: builtins.bool
    """If set, the worker is opting in to worker versioning. Otherwise, this is used only as a
    marker for workflow reset points and the BuildIDs search attribute.
    """
    def __init__(
        self,
        *,
        build_id: builtins.str = ...,
        use_versioning: builtins.bool = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "build_id", b"build_id", "use_versioning", b"use_versioning"
        ],
    ) -> None: ...

global___WorkerVersionStamp = WorkerVersionStamp

class WorkerVersionCapabilities(google.protobuf.message.Message):
    """Identifies the version that a worker is compatible with when polling or identifying itself,
    and whether or not this worker is opting into the build-id based versioning feature. This is
    used by matching to determine which workers ought to receive what tasks.
    Deprecated. Use WorkerDeploymentOptions instead.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BUILD_ID_FIELD_NUMBER: builtins.int
    USE_VERSIONING_FIELD_NUMBER: builtins.int
    DEPLOYMENT_SERIES_NAME_FIELD_NUMBER: builtins.int
    build_id: builtins.str
    """An opaque whole-worker identifier"""
    use_versioning: builtins.bool
    """If set, the worker is opting in to worker versioning, and wishes to only receive appropriate
    tasks.
    """
    deployment_series_name: builtins.str
    """Must be sent if user has set a deployment series name (versioning-3)."""
    def __init__(
        self,
        *,
        build_id: builtins.str = ...,
        use_versioning: builtins.bool = ...,
        deployment_series_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "build_id",
            b"build_id",
            "deployment_series_name",
            b"deployment_series_name",
            "use_versioning",
            b"use_versioning",
        ],
    ) -> None: ...

global___WorkerVersionCapabilities = WorkerVersionCapabilities

class ResetOptions(google.protobuf.message.Message):
    """Describes where and how to reset a workflow, used for batch reset currently
    and may be used for single-workflow reset later.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FIRST_WORKFLOW_TASK_FIELD_NUMBER: builtins.int
    LAST_WORKFLOW_TASK_FIELD_NUMBER: builtins.int
    WORKFLOW_TASK_ID_FIELD_NUMBER: builtins.int
    BUILD_ID_FIELD_NUMBER: builtins.int
    RESET_REAPPLY_TYPE_FIELD_NUMBER: builtins.int
    CURRENT_RUN_ONLY_FIELD_NUMBER: builtins.int
    RESET_REAPPLY_EXCLUDE_TYPES_FIELD_NUMBER: builtins.int
    @property
    def first_workflow_task(self) -> google.protobuf.empty_pb2.Empty:
        """Resets to the first workflow task completed or started event."""
    @property
    def last_workflow_task(self) -> google.protobuf.empty_pb2.Empty:
        """Resets to the last workflow task completed or started event."""
    workflow_task_id: builtins.int
    """The id of a specific `WORKFLOW_TASK_COMPLETED`,`WORKFLOW_TASK_TIMED_OUT`, `WORKFLOW_TASK_FAILED`, or
    `WORKFLOW_TASK_STARTED` event to reset to.
    Note that this option doesn't make sense when used as part of a batch request.
    """
    build_id: builtins.str
    """Resets to the first workflow task processed by this build id.
    If the workflow was not processed by the build id, or the workflow task can't be
    determined, no reset will be performed.
    Note that by default, this reset is allowed to be to a prior run in a chain of
    continue-as-new.
    """
    reset_reapply_type: temporalio.api.enums.v1.reset_pb2.ResetReapplyType.ValueType
    """Deprecated. Use `options`.
    Default: RESET_REAPPLY_TYPE_SIGNAL
    """
    current_run_only: builtins.bool
    """If true, limit the reset to only within the current run. (Applies to build_id targets and
    possibly others in the future.)
    """
    @property
    def reset_reapply_exclude_types(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[
        temporalio.api.enums.v1.reset_pb2.ResetReapplyExcludeType.ValueType
    ]:
        """Event types not to be reapplied"""
    def __init__(
        self,
        *,
        first_workflow_task: google.protobuf.empty_pb2.Empty | None = ...,
        last_workflow_task: google.protobuf.empty_pb2.Empty | None = ...,
        workflow_task_id: builtins.int = ...,
        build_id: builtins.str = ...,
        reset_reapply_type: temporalio.api.enums.v1.reset_pb2.ResetReapplyType.ValueType = ...,
        current_run_only: builtins.bool = ...,
        reset_reapply_exclude_types: collections.abc.Iterable[
            temporalio.api.enums.v1.reset_pb2.ResetReapplyExcludeType.ValueType
        ]
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "build_id",
            b"build_id",
            "first_workflow_task",
            b"first_workflow_task",
            "last_workflow_task",
            b"last_workflow_task",
            "target",
            b"target",
            "workflow_task_id",
            b"workflow_task_id",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "build_id",
            b"build_id",
            "current_run_only",
            b"current_run_only",
            "first_workflow_task",
            b"first_workflow_task",
            "last_workflow_task",
            b"last_workflow_task",
            "reset_reapply_exclude_types",
            b"reset_reapply_exclude_types",
            "reset_reapply_type",
            b"reset_reapply_type",
            "target",
            b"target",
            "workflow_task_id",
            b"workflow_task_id",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["target", b"target"]
    ) -> (
        typing_extensions.Literal[
            "first_workflow_task", "last_workflow_task", "workflow_task_id", "build_id"
        ]
        | None
    ): ...

global___ResetOptions = ResetOptions

class Callback(google.protobuf.message.Message):
    """Callback to attach to various events in the system, e.g. workflow run completion."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class Nexus(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        class HeaderEntry(google.protobuf.message.Message):
            DESCRIPTOR: google.protobuf.descriptor.Descriptor

            KEY_FIELD_NUMBER: builtins.int
            VALUE_FIELD_NUMBER: builtins.int
            key: builtins.str
            value: builtins.str
            def __init__(
                self,
                *,
                key: builtins.str = ...,
                value: builtins.str = ...,
            ) -> None: ...
            def ClearField(
                self,
                field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
            ) -> None: ...

        URL_FIELD_NUMBER: builtins.int
        HEADER_FIELD_NUMBER: builtins.int
        url: builtins.str
        """Callback URL."""
        @property
        def header(
            self,
        ) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
            """Header to attach to callback request."""
        def __init__(
            self,
            *,
            url: builtins.str = ...,
            header: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["header", b"header", "url", b"url"],
        ) -> None: ...

    class Internal(google.protobuf.message.Message):
        """Callbacks to be delivered internally within the system.
        This variant is not settable in the API and will be rejected by the service with an INVALID_ARGUMENT error.
        The only reason that this is exposed is because callbacks are replicated across clusters via the
        WorkflowExecutionStarted event, which is defined in the public API.
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        DATA_FIELD_NUMBER: builtins.int
        data: builtins.bytes
        """Opaque internal data."""
        def __init__(
            self,
            *,
            data: builtins.bytes = ...,
        ) -> None: ...
        def ClearField(
            self, field_name: typing_extensions.Literal["data", b"data"]
        ) -> None: ...

    NEXUS_FIELD_NUMBER: builtins.int
    INTERNAL_FIELD_NUMBER: builtins.int
    LINKS_FIELD_NUMBER: builtins.int
    @property
    def nexus(self) -> global___Callback.Nexus: ...
    @property
    def internal(self) -> global___Callback.Internal: ...
    @property
    def links(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___Link
    ]:
        """Links associated with the callback. It can be used to link to underlying resources of the
        callback.
        """
    def __init__(
        self,
        *,
        nexus: global___Callback.Nexus | None = ...,
        internal: global___Callback.Internal | None = ...,
        links: collections.abc.Iterable[global___Link] | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "internal", b"internal", "nexus", b"nexus", "variant", b"variant"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "internal",
            b"internal",
            "links",
            b"links",
            "nexus",
            b"nexus",
            "variant",
            b"variant",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["variant", b"variant"]
    ) -> typing_extensions.Literal["nexus", "internal"] | None: ...

global___Callback = Callback

class Link(google.protobuf.message.Message):
    """Link can be associated with history events. It might contain information about an external entity
    related to the history event. For example, workflow A makes a Nexus call that starts workflow B:
    in this case, a history event in workflow A could contain a Link to the workflow started event in
    workflow B, and vice-versa.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class WorkflowEvent(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        class EventReference(google.protobuf.message.Message):
            """EventReference is a direct reference to a history event through the event ID."""

            DESCRIPTOR: google.protobuf.descriptor.Descriptor

            EVENT_ID_FIELD_NUMBER: builtins.int
            EVENT_TYPE_FIELD_NUMBER: builtins.int
            event_id: builtins.int
            event_type: temporalio.api.enums.v1.event_type_pb2.EventType.ValueType
            def __init__(
                self,
                *,
                event_id: builtins.int = ...,
                event_type: temporalio.api.enums.v1.event_type_pb2.EventType.ValueType = ...,
            ) -> None: ...
            def ClearField(
                self,
                field_name: typing_extensions.Literal[
                    "event_id", b"event_id", "event_type", b"event_type"
                ],
            ) -> None: ...

        class RequestIdReference(google.protobuf.message.Message):
            """RequestIdReference is a indirect reference to a history event through the request ID."""

            DESCRIPTOR: google.protobuf.descriptor.Descriptor

            REQUEST_ID_FIELD_NUMBER: builtins.int
            EVENT_TYPE_FIELD_NUMBER: builtins.int
            request_id: builtins.str
            event_type: temporalio.api.enums.v1.event_type_pb2.EventType.ValueType
            def __init__(
                self,
                *,
                request_id: builtins.str = ...,
                event_type: temporalio.api.enums.v1.event_type_pb2.EventType.ValueType = ...,
            ) -> None: ...
            def ClearField(
                self,
                field_name: typing_extensions.Literal[
                    "event_type", b"event_type", "request_id", b"request_id"
                ],
            ) -> None: ...

        NAMESPACE_FIELD_NUMBER: builtins.int
        WORKFLOW_ID_FIELD_NUMBER: builtins.int
        RUN_ID_FIELD_NUMBER: builtins.int
        EVENT_REF_FIELD_NUMBER: builtins.int
        REQUEST_ID_REF_FIELD_NUMBER: builtins.int
        namespace: builtins.str
        workflow_id: builtins.str
        run_id: builtins.str
        @property
        def event_ref(self) -> global___Link.WorkflowEvent.EventReference: ...
        @property
        def request_id_ref(self) -> global___Link.WorkflowEvent.RequestIdReference: ...
        def __init__(
            self,
            *,
            namespace: builtins.str = ...,
            workflow_id: builtins.str = ...,
            run_id: builtins.str = ...,
            event_ref: global___Link.WorkflowEvent.EventReference | None = ...,
            request_id_ref: global___Link.WorkflowEvent.RequestIdReference | None = ...,
        ) -> None: ...
        def HasField(
            self,
            field_name: typing_extensions.Literal[
                "event_ref",
                b"event_ref",
                "reference",
                b"reference",
                "request_id_ref",
                b"request_id_ref",
            ],
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal[
                "event_ref",
                b"event_ref",
                "namespace",
                b"namespace",
                "reference",
                b"reference",
                "request_id_ref",
                b"request_id_ref",
                "run_id",
                b"run_id",
                "workflow_id",
                b"workflow_id",
            ],
        ) -> None: ...
        def WhichOneof(
            self, oneof_group: typing_extensions.Literal["reference", b"reference"]
        ) -> typing_extensions.Literal["event_ref", "request_id_ref"] | None: ...

    class BatchJob(google.protobuf.message.Message):
        """A link to a built-in batch job.
        Batch jobs can be used to perform operations on a set of workflows (e.g. terminate, signal, cancel, etc).
        This link can be put on workflow history events generated by actions taken by a batch job.
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        JOB_ID_FIELD_NUMBER: builtins.int
        job_id: builtins.str
        def __init__(
            self,
            *,
            job_id: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self, field_name: typing_extensions.Literal["job_id", b"job_id"]
        ) -> None: ...

    WORKFLOW_EVENT_FIELD_NUMBER: builtins.int
    BATCH_JOB_FIELD_NUMBER: builtins.int
    @property
    def workflow_event(self) -> global___Link.WorkflowEvent: ...
    @property
    def batch_job(self) -> global___Link.BatchJob: ...
    def __init__(
        self,
        *,
        workflow_event: global___Link.WorkflowEvent | None = ...,
        batch_job: global___Link.BatchJob | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "batch_job",
            b"batch_job",
            "variant",
            b"variant",
            "workflow_event",
            b"workflow_event",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "batch_job",
            b"batch_job",
            "variant",
            b"variant",
            "workflow_event",
            b"workflow_event",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["variant", b"variant"]
    ) -> typing_extensions.Literal["workflow_event", "batch_job"] | None: ...

global___Link = Link

class Priority(google.protobuf.message.Message):
    """Priority contains metadata that controls relative ordering of task processing
    when tasks are backed up in a queue. Initially, Priority will be used in
    matching (workflow and activity) task queues. Later it may be used in history
    task queues and in rate limiting decisions.

    Priority is attached to workflows and activities. By default, activities
    inherit Priority from the workflow that created them, but may override fields
    when an activity is started or modified.

    Despite being named "Priority", this message also contains fields that
    control "fairness" mechanisms.

    For all fields, the field not present or equal to zero/empty string means to
    inherit the value from the calling workflow, or if there is no calling
    workflow, then use the default value.

    For all fields other than fairness_key, the zero value isn't meaningful so
    there's no confusion between inherit/default and a meaningful value. For
    fairness_key, the empty string will be interpreted as "inherit". This means
    that if a workflow has a non-empty fairness key, you can't override the
    fairness key of its activity to the empty string.

    The overall semantics of Priority are:
    1. First, consider "priority": higher priority (lower number) goes first.
    2. Then, consider fairness: try to dispatch tasks for different fairness keys
       in proportion to their weight.

    Applications may use any subset of mechanisms that are useful to them and
    leave the other fields to use default values.

    Not all queues in the system may support the "full" semantics of all priority
    fields. (Currently only support in matching task queues is planned.)
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PRIORITY_KEY_FIELD_NUMBER: builtins.int
    FAIRNESS_KEY_FIELD_NUMBER: builtins.int
    FAIRNESS_WEIGHT_FIELD_NUMBER: builtins.int
    priority_key: builtins.int
    """Priority key is a positive integer from 1 to n, where smaller integers
    correspond to higher priorities (tasks run sooner). In general, tasks in
    a queue should be processed in close to priority order, although small
    deviations are possible.

    The maximum priority value (minimum priority) is determined by server
    configuration, and defaults to 5.

    If priority is not present (or zero), then the effective priority will be
    the default priority, which is is calculated by (min+max)/2. With the
    default max of 5, and min of 1, that comes out to 3.
    """
    fairness_key: builtins.str
    """Fairness key is a short string that's used as a key for a fairness
    balancing mechanism. It may correspond to a tenant id, or to a fixed
    string like "high" or "low". The default is the empty string.

    The fairness mechanism attempts to dispatch tasks for a given key in
    proportion to its weight. For example, using a thousand distinct tenant
    ids, each with a weight of 1.0 (the default) will result in each tenant
    getting a roughly equal share of task dispatch throughput.

    (Note: this does not imply equal share of worker capacity! Fairness
    decisions are made based on queue statistics, not
    current worker load.)

    As another example, using keys "high" and "low" with weight 9.0 and 1.0
    respectively will prefer dispatching "high" tasks over "low" tasks at a
    9:1 ratio, while allowing either key to use all worker capacity if the
    other is not present.

    All fairness mechanisms, including rate limits, are best-effort and
    probabilistic. The results may not match what a "perfect" algorithm with
    infinite resources would produce. The more unique keys are used, the less
    accurate the results will be.

    Fairness keys are limited to 64 bytes.
    """
    fairness_weight: builtins.float
    """Fairness weight for a task can come from multiple sources for
    flexibility. From highest to lowest precedence:
    1. Weights for a small set of keys can be overridden in task queue
       configuration with an API.
    2. It can be attached to the workflow/activity in this field.
    3. The default weight of 1.0 will be used.

    Weight values are clamped to the range [0.001, 1000].
    """
    def __init__(
        self,
        *,
        priority_key: builtins.int = ...,
        fairness_key: builtins.str = ...,
        fairness_weight: builtins.float = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "fairness_key",
            b"fairness_key",
            "fairness_weight",
            b"fairness_weight",
            "priority_key",
            b"priority_key",
        ],
    ) -> None: ...

global___Priority = Priority

class WorkerSelector(google.protobuf.message.Message):
    """This is used to send commands to a specific worker or a group of workers.
    Right now, it is used to send commands to a specific worker instance.
    Will be extended to be able to send command to multiple workers.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WORKER_INSTANCE_KEY_FIELD_NUMBER: builtins.int
    worker_instance_key: builtins.str
    """Worker instance key to which the command should be sent."""
    def __init__(
        self,
        *,
        worker_instance_key: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "selector", b"selector", "worker_instance_key", b"worker_instance_key"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "selector", b"selector", "worker_instance_key", b"worker_instance_key"
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["selector", b"selector"]
    ) -> typing_extensions.Literal["worker_instance_key"] | None: ...

global___WorkerSelector = WorkerSelector
