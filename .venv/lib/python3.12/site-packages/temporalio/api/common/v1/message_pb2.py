# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/common/v1/message.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2

from temporalio.api.enums.v1 import (
    common_pb2 as temporal_dot_api_dot_enums_dot_v1_dot_common__pb2,
)
from temporalio.api.enums.v1 import (
    event_type_pb2 as temporal_dot_api_dot_enums_dot_v1_dot_event__type__pb2,
)
from temporalio.api.enums.v1 import (
    reset_pb2 as temporal_dot_api_dot_enums_dot_v1_dot_reset__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n$temporal/api/common/v1/message.proto\x12\x16temporal.api.common.v1\x1a\x1egoogle/protobuf/duration.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a"temporal/api/enums/v1/common.proto\x1a&temporal/api/enums/v1/event_type.proto\x1a!temporal/api/enums/v1/reset.proto"T\n\x08\x44\x61taBlob\x12:\n\rencoding_type\x18\x01 \x01(\x0e\x32#.temporal.api.enums.v1.EncodingType\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c"=\n\x08Payloads\x12\x31\n\x08payloads\x18\x01 \x03(\x0b\x32\x1f.temporal.api.common.v1.Payload"\x89\x01\n\x07Payload\x12?\n\x08metadata\x18\x01 \x03(\x0b\x32-.temporal.api.common.v1.Payload.MetadataEntry\x12\x0c\n\x04\x64\x61ta\x18\x02 \x01(\x0c\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01"\xbe\x01\n\x10SearchAttributes\x12S\n\x0eindexed_fields\x18\x01 \x03(\x0b\x32;.temporal.api.common.v1.SearchAttributes.IndexedFieldsEntry\x1aU\n\x12IndexedFieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12.\n\x05value\x18\x02 \x01(\x0b\x32\x1f.temporal.api.common.v1.Payload:\x02\x38\x01"\x90\x01\n\x04Memo\x12\x38\n\x06\x66ields\x18\x01 \x03(\x0b\x32(.temporal.api.common.v1.Memo.FieldsEntry\x1aN\n\x0b\x46ieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12.\n\x05value\x18\x02 \x01(\x0b\x32\x1f.temporal.api.common.v1.Payload:\x02\x38\x01"\x94\x01\n\x06Header\x12:\n\x06\x66ields\x18\x01 \x03(\x0b\x32*.temporal.api.common.v1.Header.FieldsEntry\x1aN\n\x0b\x46ieldsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12.\n\x05value\x18\x02 \x01(\x0b\x32\x1f.temporal.api.common.v1.Payload:\x02\x38\x01"8\n\x11WorkflowExecution\x12\x13\n\x0bworkflow_id\x18\x01 \x01(\t\x12\x0e\n\x06run_id\x18\x02 \x01(\t"\x1c\n\x0cWorkflowType\x12\x0c\n\x04name\x18\x01 \x01(\t"\x1c\n\x0c\x41\x63tivityType\x12\x0c\n\x04name\x18\x01 \x01(\t"\xd1\x01\n\x0bRetryPolicy\x12\x33\n\x10initial_interval\x18\x01 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x1b\n\x13\x62\x61\x63koff_coefficient\x18\x02 \x01(\x01\x12\x33\n\x10maximum_interval\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x18\n\x10maximum_attempts\x18\x04 \x01(\x05\x12!\n\x19non_retryable_error_types\x18\x05 \x03(\t"F\n\x10MeteringMetadata\x12\x32\n*nonfirst_local_activity_execution_attempts\x18\r \x01(\r">\n\x12WorkerVersionStamp\x12\x10\n\x08\x62uild_id\x18\x01 \x01(\t\x12\x16\n\x0euse_versioning\x18\x03 \x01(\x08"e\n\x19WorkerVersionCapabilities\x12\x10\n\x08\x62uild_id\x18\x01 \x01(\t\x12\x16\n\x0euse_versioning\x18\x02 \x01(\x08\x12\x1e\n\x16\x64\x65ployment_series_name\x18\x04 \x01(\t"\xed\x02\n\x0cResetOptions\x12\x35\n\x13\x66irst_workflow_task\x18\x01 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x12\x34\n\x12last_workflow_task\x18\x02 \x01(\x0b\x32\x16.google.protobuf.EmptyH\x00\x12\x1a\n\x10workflow_task_id\x18\x03 \x01(\x03H\x00\x12\x12\n\x08\x62uild_id\x18\x04 \x01(\tH\x00\x12G\n\x12reset_reapply_type\x18\n \x01(\x0e\x32\'.temporal.api.enums.v1.ResetReapplyTypeB\x02\x18\x01\x12\x18\n\x10\x63urrent_run_only\x18\x0b \x01(\x08\x12S\n\x1breset_reapply_exclude_types\x18\x0c \x03(\x0e\x32..temporal.api.enums.v1.ResetReapplyExcludeTypeB\x08\n\x06target"\xe4\x02\n\x08\x43\x61llback\x12\x37\n\x05nexus\x18\x02 \x01(\x0b\x32&.temporal.api.common.v1.Callback.NexusH\x00\x12=\n\x08internal\x18\x03 \x01(\x0b\x32).temporal.api.common.v1.Callback.InternalH\x00\x12+\n\x05links\x18\x64 \x03(\x0b\x32\x1c.temporal.api.common.v1.Link\x1a\x87\x01\n\x05Nexus\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x42\n\x06header\x18\x02 \x03(\x0b\x32\x32.temporal.api.common.v1.Callback.Nexus.HeaderEntry\x1a-\n\x0bHeaderEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x18\n\x08Internal\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x42\t\n\x07variantJ\x04\x08\x01\x10\x02"\xe9\x04\n\x04Link\x12\x44\n\x0eworkflow_event\x18\x01 \x01(\x0b\x32*.temporal.api.common.v1.Link.WorkflowEventH\x00\x12:\n\tbatch_job\x18\x02 \x01(\x0b\x32%.temporal.api.common.v1.Link.BatchJobH\x00\x1a\xb7\x03\n\rWorkflowEvent\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12\x13\n\x0bworkflow_id\x18\x02 \x01(\t\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12N\n\tevent_ref\x18\x64 \x01(\x0b\x32\x39.temporal.api.common.v1.Link.WorkflowEvent.EventReferenceH\x00\x12W\n\x0erequest_id_ref\x18\x65 \x01(\x0b\x32=.temporal.api.common.v1.Link.WorkflowEvent.RequestIdReferenceH\x00\x1aX\n\x0e\x45ventReference\x12\x10\n\x08\x65vent_id\x18\x01 \x01(\x03\x12\x34\n\nevent_type\x18\x02 \x01(\x0e\x32 .temporal.api.enums.v1.EventType\x1a^\n\x12RequestIdReference\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x34\n\nevent_type\x18\x02 \x01(\x0e\x32 .temporal.api.enums.v1.EventTypeB\x0b\n\treference\x1a\x1a\n\x08\x42\x61tchJob\x12\x0e\n\x06job_id\x18\x01 \x01(\tB\t\n\x07variant"O\n\x08Priority\x12\x14\n\x0cpriority_key\x18\x01 \x01(\x05\x12\x14\n\x0c\x66\x61irness_key\x18\x02 \x01(\t\x12\x17\n\x0f\x66\x61irness_weight\x18\x03 \x01(\x02";\n\x0eWorkerSelector\x12\x1d\n\x13worker_instance_key\x18\x01 \x01(\tH\x00\x42\n\n\x08selectorB\x89\x01\n\x19io.temporal.api.common.v1B\x0cMessageProtoP\x01Z#go.temporal.io/api/common/v1;common\xaa\x02\x18Temporalio.Api.Common.V1\xea\x02\x1bTemporalio::Api::Common::V1b\x06proto3'
)


_DATABLOB = DESCRIPTOR.message_types_by_name["DataBlob"]
_PAYLOADS = DESCRIPTOR.message_types_by_name["Payloads"]
_PAYLOAD = DESCRIPTOR.message_types_by_name["Payload"]
_PAYLOAD_METADATAENTRY = _PAYLOAD.nested_types_by_name["MetadataEntry"]
_SEARCHATTRIBUTES = DESCRIPTOR.message_types_by_name["SearchAttributes"]
_SEARCHATTRIBUTES_INDEXEDFIELDSENTRY = _SEARCHATTRIBUTES.nested_types_by_name[
    "IndexedFieldsEntry"
]
_MEMO = DESCRIPTOR.message_types_by_name["Memo"]
_MEMO_FIELDSENTRY = _MEMO.nested_types_by_name["FieldsEntry"]
_HEADER = DESCRIPTOR.message_types_by_name["Header"]
_HEADER_FIELDSENTRY = _HEADER.nested_types_by_name["FieldsEntry"]
_WORKFLOWEXECUTION = DESCRIPTOR.message_types_by_name["WorkflowExecution"]
_WORKFLOWTYPE = DESCRIPTOR.message_types_by_name["WorkflowType"]
_ACTIVITYTYPE = DESCRIPTOR.message_types_by_name["ActivityType"]
_RETRYPOLICY = DESCRIPTOR.message_types_by_name["RetryPolicy"]
_METERINGMETADATA = DESCRIPTOR.message_types_by_name["MeteringMetadata"]
_WORKERVERSIONSTAMP = DESCRIPTOR.message_types_by_name["WorkerVersionStamp"]
_WORKERVERSIONCAPABILITIES = DESCRIPTOR.message_types_by_name[
    "WorkerVersionCapabilities"
]
_RESETOPTIONS = DESCRIPTOR.message_types_by_name["ResetOptions"]
_CALLBACK = DESCRIPTOR.message_types_by_name["Callback"]
_CALLBACK_NEXUS = _CALLBACK.nested_types_by_name["Nexus"]
_CALLBACK_NEXUS_HEADERENTRY = _CALLBACK_NEXUS.nested_types_by_name["HeaderEntry"]
_CALLBACK_INTERNAL = _CALLBACK.nested_types_by_name["Internal"]
_LINK = DESCRIPTOR.message_types_by_name["Link"]
_LINK_WORKFLOWEVENT = _LINK.nested_types_by_name["WorkflowEvent"]
_LINK_WORKFLOWEVENT_EVENTREFERENCE = _LINK_WORKFLOWEVENT.nested_types_by_name[
    "EventReference"
]
_LINK_WORKFLOWEVENT_REQUESTIDREFERENCE = _LINK_WORKFLOWEVENT.nested_types_by_name[
    "RequestIdReference"
]
_LINK_BATCHJOB = _LINK.nested_types_by_name["BatchJob"]
_PRIORITY = DESCRIPTOR.message_types_by_name["Priority"]
_WORKERSELECTOR = DESCRIPTOR.message_types_by_name["WorkerSelector"]
DataBlob = _reflection.GeneratedProtocolMessageType(
    "DataBlob",
    (_message.Message,),
    {
        "DESCRIPTOR": _DATABLOB,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.DataBlob)
    },
)
_sym_db.RegisterMessage(DataBlob)

Payloads = _reflection.GeneratedProtocolMessageType(
    "Payloads",
    (_message.Message,),
    {
        "DESCRIPTOR": _PAYLOADS,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Payloads)
    },
)
_sym_db.RegisterMessage(Payloads)

Payload = _reflection.GeneratedProtocolMessageType(
    "Payload",
    (_message.Message,),
    {
        "MetadataEntry": _reflection.GeneratedProtocolMessageType(
            "MetadataEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _PAYLOAD_METADATAENTRY,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Payload.MetadataEntry)
            },
        ),
        "DESCRIPTOR": _PAYLOAD,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Payload)
    },
)
_sym_db.RegisterMessage(Payload)
_sym_db.RegisterMessage(Payload.MetadataEntry)

SearchAttributes = _reflection.GeneratedProtocolMessageType(
    "SearchAttributes",
    (_message.Message,),
    {
        "IndexedFieldsEntry": _reflection.GeneratedProtocolMessageType(
            "IndexedFieldsEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _SEARCHATTRIBUTES_INDEXEDFIELDSENTRY,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.SearchAttributes.IndexedFieldsEntry)
            },
        ),
        "DESCRIPTOR": _SEARCHATTRIBUTES,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.SearchAttributes)
    },
)
_sym_db.RegisterMessage(SearchAttributes)
_sym_db.RegisterMessage(SearchAttributes.IndexedFieldsEntry)

Memo = _reflection.GeneratedProtocolMessageType(
    "Memo",
    (_message.Message,),
    {
        "FieldsEntry": _reflection.GeneratedProtocolMessageType(
            "FieldsEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _MEMO_FIELDSENTRY,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Memo.FieldsEntry)
            },
        ),
        "DESCRIPTOR": _MEMO,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Memo)
    },
)
_sym_db.RegisterMessage(Memo)
_sym_db.RegisterMessage(Memo.FieldsEntry)

Header = _reflection.GeneratedProtocolMessageType(
    "Header",
    (_message.Message,),
    {
        "FieldsEntry": _reflection.GeneratedProtocolMessageType(
            "FieldsEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _HEADER_FIELDSENTRY,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Header.FieldsEntry)
            },
        ),
        "DESCRIPTOR": _HEADER,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Header)
    },
)
_sym_db.RegisterMessage(Header)
_sym_db.RegisterMessage(Header.FieldsEntry)

WorkflowExecution = _reflection.GeneratedProtocolMessageType(
    "WorkflowExecution",
    (_message.Message,),
    {
        "DESCRIPTOR": _WORKFLOWEXECUTION,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.WorkflowExecution)
    },
)
_sym_db.RegisterMessage(WorkflowExecution)

WorkflowType = _reflection.GeneratedProtocolMessageType(
    "WorkflowType",
    (_message.Message,),
    {
        "DESCRIPTOR": _WORKFLOWTYPE,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.WorkflowType)
    },
)
_sym_db.RegisterMessage(WorkflowType)

ActivityType = _reflection.GeneratedProtocolMessageType(
    "ActivityType",
    (_message.Message,),
    {
        "DESCRIPTOR": _ACTIVITYTYPE,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.ActivityType)
    },
)
_sym_db.RegisterMessage(ActivityType)

RetryPolicy = _reflection.GeneratedProtocolMessageType(
    "RetryPolicy",
    (_message.Message,),
    {
        "DESCRIPTOR": _RETRYPOLICY,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.RetryPolicy)
    },
)
_sym_db.RegisterMessage(RetryPolicy)

MeteringMetadata = _reflection.GeneratedProtocolMessageType(
    "MeteringMetadata",
    (_message.Message,),
    {
        "DESCRIPTOR": _METERINGMETADATA,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.MeteringMetadata)
    },
)
_sym_db.RegisterMessage(MeteringMetadata)

WorkerVersionStamp = _reflection.GeneratedProtocolMessageType(
    "WorkerVersionStamp",
    (_message.Message,),
    {
        "DESCRIPTOR": _WORKERVERSIONSTAMP,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.WorkerVersionStamp)
    },
)
_sym_db.RegisterMessage(WorkerVersionStamp)

WorkerVersionCapabilities = _reflection.GeneratedProtocolMessageType(
    "WorkerVersionCapabilities",
    (_message.Message,),
    {
        "DESCRIPTOR": _WORKERVERSIONCAPABILITIES,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.WorkerVersionCapabilities)
    },
)
_sym_db.RegisterMessage(WorkerVersionCapabilities)

ResetOptions = _reflection.GeneratedProtocolMessageType(
    "ResetOptions",
    (_message.Message,),
    {
        "DESCRIPTOR": _RESETOPTIONS,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.ResetOptions)
    },
)
_sym_db.RegisterMessage(ResetOptions)

Callback = _reflection.GeneratedProtocolMessageType(
    "Callback",
    (_message.Message,),
    {
        "Nexus": _reflection.GeneratedProtocolMessageType(
            "Nexus",
            (_message.Message,),
            {
                "HeaderEntry": _reflection.GeneratedProtocolMessageType(
                    "HeaderEntry",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _CALLBACK_NEXUS_HEADERENTRY,
                        "__module__": "temporal.api.common.v1.message_pb2",
                        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Callback.Nexus.HeaderEntry)
                    },
                ),
                "DESCRIPTOR": _CALLBACK_NEXUS,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Callback.Nexus)
            },
        ),
        "Internal": _reflection.GeneratedProtocolMessageType(
            "Internal",
            (_message.Message,),
            {
                "DESCRIPTOR": _CALLBACK_INTERNAL,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Callback.Internal)
            },
        ),
        "DESCRIPTOR": _CALLBACK,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Callback)
    },
)
_sym_db.RegisterMessage(Callback)
_sym_db.RegisterMessage(Callback.Nexus)
_sym_db.RegisterMessage(Callback.Nexus.HeaderEntry)
_sym_db.RegisterMessage(Callback.Internal)

Link = _reflection.GeneratedProtocolMessageType(
    "Link",
    (_message.Message,),
    {
        "WorkflowEvent": _reflection.GeneratedProtocolMessageType(
            "WorkflowEvent",
            (_message.Message,),
            {
                "EventReference": _reflection.GeneratedProtocolMessageType(
                    "EventReference",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _LINK_WORKFLOWEVENT_EVENTREFERENCE,
                        "__module__": "temporal.api.common.v1.message_pb2",
                        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Link.WorkflowEvent.EventReference)
                    },
                ),
                "RequestIdReference": _reflection.GeneratedProtocolMessageType(
                    "RequestIdReference",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _LINK_WORKFLOWEVENT_REQUESTIDREFERENCE,
                        "__module__": "temporal.api.common.v1.message_pb2",
                        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Link.WorkflowEvent.RequestIdReference)
                    },
                ),
                "DESCRIPTOR": _LINK_WORKFLOWEVENT,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Link.WorkflowEvent)
            },
        ),
        "BatchJob": _reflection.GeneratedProtocolMessageType(
            "BatchJob",
            (_message.Message,),
            {
                "DESCRIPTOR": _LINK_BATCHJOB,
                "__module__": "temporal.api.common.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Link.BatchJob)
            },
        ),
        "DESCRIPTOR": _LINK,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Link)
    },
)
_sym_db.RegisterMessage(Link)
_sym_db.RegisterMessage(Link.WorkflowEvent)
_sym_db.RegisterMessage(Link.WorkflowEvent.EventReference)
_sym_db.RegisterMessage(Link.WorkflowEvent.RequestIdReference)
_sym_db.RegisterMessage(Link.BatchJob)

Priority = _reflection.GeneratedProtocolMessageType(
    "Priority",
    (_message.Message,),
    {
        "DESCRIPTOR": _PRIORITY,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.Priority)
    },
)
_sym_db.RegisterMessage(Priority)

WorkerSelector = _reflection.GeneratedProtocolMessageType(
    "WorkerSelector",
    (_message.Message,),
    {
        "DESCRIPTOR": _WORKERSELECTOR,
        "__module__": "temporal.api.common.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.common.v1.WorkerSelector)
    },
)
_sym_db.RegisterMessage(WorkerSelector)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\031io.temporal.api.common.v1B\014MessageProtoP\001Z#go.temporal.io/api/common/v1;common\252\002\030Temporalio.Api.Common.V1\352\002\033Temporalio::Api::Common::V1"
    _PAYLOAD_METADATAENTRY._options = None
    _PAYLOAD_METADATAENTRY._serialized_options = b"8\001"
    _SEARCHATTRIBUTES_INDEXEDFIELDSENTRY._options = None
    _SEARCHATTRIBUTES_INDEXEDFIELDSENTRY._serialized_options = b"8\001"
    _MEMO_FIELDSENTRY._options = None
    _MEMO_FIELDSENTRY._serialized_options = b"8\001"
    _HEADER_FIELDSENTRY._options = None
    _HEADER_FIELDSENTRY._serialized_options = b"8\001"
    _RESETOPTIONS.fields_by_name["reset_reapply_type"]._options = None
    _RESETOPTIONS.fields_by_name["reset_reapply_type"]._serialized_options = b"\030\001"
    _CALLBACK_NEXUS_HEADERENTRY._options = None
    _CALLBACK_NEXUS_HEADERENTRY._serialized_options = b"8\001"
    _DATABLOB._serialized_start = 236
    _DATABLOB._serialized_end = 320
    _PAYLOADS._serialized_start = 322
    _PAYLOADS._serialized_end = 383
    _PAYLOAD._serialized_start = 386
    _PAYLOAD._serialized_end = 523
    _PAYLOAD_METADATAENTRY._serialized_start = 476
    _PAYLOAD_METADATAENTRY._serialized_end = 523
    _SEARCHATTRIBUTES._serialized_start = 526
    _SEARCHATTRIBUTES._serialized_end = 716
    _SEARCHATTRIBUTES_INDEXEDFIELDSENTRY._serialized_start = 631
    _SEARCHATTRIBUTES_INDEXEDFIELDSENTRY._serialized_end = 716
    _MEMO._serialized_start = 719
    _MEMO._serialized_end = 863
    _MEMO_FIELDSENTRY._serialized_start = 785
    _MEMO_FIELDSENTRY._serialized_end = 863
    _HEADER._serialized_start = 866
    _HEADER._serialized_end = 1014
    _HEADER_FIELDSENTRY._serialized_start = 785
    _HEADER_FIELDSENTRY._serialized_end = 863
    _WORKFLOWEXECUTION._serialized_start = 1016
    _WORKFLOWEXECUTION._serialized_end = 1072
    _WORKFLOWTYPE._serialized_start = 1074
    _WORKFLOWTYPE._serialized_end = 1102
    _ACTIVITYTYPE._serialized_start = 1104
    _ACTIVITYTYPE._serialized_end = 1132
    _RETRYPOLICY._serialized_start = 1135
    _RETRYPOLICY._serialized_end = 1344
    _METERINGMETADATA._serialized_start = 1346
    _METERINGMETADATA._serialized_end = 1416
    _WORKERVERSIONSTAMP._serialized_start = 1418
    _WORKERVERSIONSTAMP._serialized_end = 1480
    _WORKERVERSIONCAPABILITIES._serialized_start = 1482
    _WORKERVERSIONCAPABILITIES._serialized_end = 1583
    _RESETOPTIONS._serialized_start = 1586
    _RESETOPTIONS._serialized_end = 1951
    _CALLBACK._serialized_start = 1954
    _CALLBACK._serialized_end = 2310
    _CALLBACK_NEXUS._serialized_start = 2132
    _CALLBACK_NEXUS._serialized_end = 2267
    _CALLBACK_NEXUS_HEADERENTRY._serialized_start = 2222
    _CALLBACK_NEXUS_HEADERENTRY._serialized_end = 2267
    _CALLBACK_INTERNAL._serialized_start = 2269
    _CALLBACK_INTERNAL._serialized_end = 2293
    _LINK._serialized_start = 2313
    _LINK._serialized_end = 2930
    _LINK_WORKFLOWEVENT._serialized_start = 2452
    _LINK_WORKFLOWEVENT._serialized_end = 2891
    _LINK_WORKFLOWEVENT_EVENTREFERENCE._serialized_start = 2694
    _LINK_WORKFLOWEVENT_EVENTREFERENCE._serialized_end = 2782
    _LINK_WORKFLOWEVENT_REQUESTIDREFERENCE._serialized_start = 2784
    _LINK_WORKFLOWEVENT_REQUESTIDREFERENCE._serialized_end = 2878
    _LINK_BATCHJOB._serialized_start = 2893
    _LINK_BATCHJOB._serialized_end = 2919
    _PRIORITY._serialized_start = 2932
    _PRIORITY._serialized_end = 3011
    _WORKERSELECTOR._serialized_start = 3013
    _WORKERSELECTOR._serialized_end = 3072
# @@protoc_insertion_point(module_scope)
