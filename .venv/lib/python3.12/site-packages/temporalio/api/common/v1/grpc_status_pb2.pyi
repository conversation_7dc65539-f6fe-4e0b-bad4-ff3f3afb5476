"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys

import google.protobuf.any_pb2
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class GrpcStatus(google.protobuf.message.Message):
    """From https://github.com/grpc/grpc/blob/master/src/proto/grpc/status/status.proto
    since we don't import grpc but still need the status info
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CODE_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    DETAILS_FIELD_NUMBER: builtins.int
    code: builtins.int
    message: builtins.str
    @property
    def details(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        google.protobuf.any_pb2.Any
    ]: ...
    def __init__(
        self,
        *,
        code: builtins.int = ...,
        message: builtins.str = ...,
        details: collections.abc.Iterable[google.protobuf.any_pb2.Any] | None = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "code", b"code", "details", b"details", "message", b"message"
        ],
    ) -> None: ...

global___GrpcStatus = GrpcStatus
