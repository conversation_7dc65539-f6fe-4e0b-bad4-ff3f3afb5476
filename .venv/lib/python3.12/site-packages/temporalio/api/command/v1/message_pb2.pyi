"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys

import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.internal.containers
import google.protobuf.message

import temporalio.api.common.v1.message_pb2
import temporalio.api.enums.v1.command_type_pb2
import temporalio.api.enums.v1.workflow_pb2
import temporalio.api.failure.v1.message_pb2
import temporalio.api.sdk.v1.user_metadata_pb2
import temporalio.api.taskqueue.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class ScheduleActivityTaskCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACTIVITY_ID_FIELD_NUMBER: builtins.int
    ACTIVITY_TYPE_FIELD_NUMBER: builtins.int
    TASK_QUEUE_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    SCHEDULE_TO_CLOSE_TIMEOUT_FIELD_NUMBER: builtins.int
    SCHEDULE_TO_START_TIMEOUT_FIELD_NUMBER: builtins.int
    START_TO_CLOSE_TIMEOUT_FIELD_NUMBER: builtins.int
    HEARTBEAT_TIMEOUT_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    REQUEST_EAGER_EXECUTION_FIELD_NUMBER: builtins.int
    USE_WORKFLOW_BUILD_ID_FIELD_NUMBER: builtins.int
    PRIORITY_FIELD_NUMBER: builtins.int
    activity_id: builtins.str
    @property
    def activity_type(self) -> temporalio.api.common.v1.message_pb2.ActivityType: ...
    @property
    def task_queue(self) -> temporalio.api.taskqueue.v1.message_pb2.TaskQueue: ...
    @property
    def header(self) -> temporalio.api.common.v1.message_pb2.Header: ...
    @property
    def input(self) -> temporalio.api.common.v1.message_pb2.Payloads: ...
    @property
    def schedule_to_close_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Indicates how long the caller is willing to wait for activity completion. The "schedule" time
        is when the activity is initially scheduled, not when the most recent retry is scheduled.
        Limits how long retries will be attempted. Either this or `start_to_close_timeout` must be
        specified. When not specified, defaults to the workflow execution timeout.

        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    @property
    def schedule_to_start_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Limits the time an activity task can stay in a task queue before a worker picks it up. The
        "schedule" time is when the most recent retry is scheduled. This timeout should usually not
        be set: it's useful in specific scenarios like worker-specific task queues. This timeout is
        always non retryable, as all a retry would achieve is to put it back into the same queue.
        Defaults to `schedule_to_close_timeout` or workflow execution timeout if that is not
        specified. More info:
        https://docs.temporal.io/docs/content/what-is-a-schedule-to-start-timeout/

        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    @property
    def start_to_close_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Maximum time an activity is allowed to execute after being picked up by a worker. This
        timeout is always retryable. Either this or `schedule_to_close_timeout` must be specified.

        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    @property
    def heartbeat_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Maximum permitted time between successful worker heartbeats."""
    @property
    def retry_policy(self) -> temporalio.api.common.v1.message_pb2.RetryPolicy:
        """Activities are provided by a default retry policy which is controlled through the service's
        dynamic configuration. Retries will be attempted until `schedule_to_close_timeout` has
        elapsed. To disable retries set retry_policy.maximum_attempts to 1.
        """
    request_eager_execution: builtins.bool
    """Request to start the activity directly bypassing matching service and worker polling
    The slot for executing the activity should be reserved when setting this field to true.
    """
    use_workflow_build_id: builtins.bool
    """If this is set, the activity would be assigned to the Build ID of the workflow. Otherwise,
    Assignment rules of the activity's Task Queue will be used to determine the Build ID.
    """
    @property
    def priority(self) -> temporalio.api.common.v1.message_pb2.Priority:
        """Priority metadata. If this message is not present, or any fields are not
        present, they inherit the values from the workflow.
        """
    def __init__(
        self,
        *,
        activity_id: builtins.str = ...,
        activity_type: temporalio.api.common.v1.message_pb2.ActivityType | None = ...,
        task_queue: temporalio.api.taskqueue.v1.message_pb2.TaskQueue | None = ...,
        header: temporalio.api.common.v1.message_pb2.Header | None = ...,
        input: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
        schedule_to_close_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        schedule_to_start_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        start_to_close_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        heartbeat_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        retry_policy: temporalio.api.common.v1.message_pb2.RetryPolicy | None = ...,
        request_eager_execution: builtins.bool = ...,
        use_workflow_build_id: builtins.bool = ...,
        priority: temporalio.api.common.v1.message_pb2.Priority | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "activity_type",
            b"activity_type",
            "header",
            b"header",
            "heartbeat_timeout",
            b"heartbeat_timeout",
            "input",
            b"input",
            "priority",
            b"priority",
            "retry_policy",
            b"retry_policy",
            "schedule_to_close_timeout",
            b"schedule_to_close_timeout",
            "schedule_to_start_timeout",
            b"schedule_to_start_timeout",
            "start_to_close_timeout",
            b"start_to_close_timeout",
            "task_queue",
            b"task_queue",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "activity_id",
            b"activity_id",
            "activity_type",
            b"activity_type",
            "header",
            b"header",
            "heartbeat_timeout",
            b"heartbeat_timeout",
            "input",
            b"input",
            "priority",
            b"priority",
            "request_eager_execution",
            b"request_eager_execution",
            "retry_policy",
            b"retry_policy",
            "schedule_to_close_timeout",
            b"schedule_to_close_timeout",
            "schedule_to_start_timeout",
            b"schedule_to_start_timeout",
            "start_to_close_timeout",
            b"start_to_close_timeout",
            "task_queue",
            b"task_queue",
            "use_workflow_build_id",
            b"use_workflow_build_id",
        ],
    ) -> None: ...

global___ScheduleActivityTaskCommandAttributes = ScheduleActivityTaskCommandAttributes

class RequestCancelActivityTaskCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SCHEDULED_EVENT_ID_FIELD_NUMBER: builtins.int
    scheduled_event_id: builtins.int
    """The `ACTIVITY_TASK_SCHEDULED` event id for the activity being cancelled."""
    def __init__(
        self,
        *,
        scheduled_event_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "scheduled_event_id", b"scheduled_event_id"
        ],
    ) -> None: ...

global___RequestCancelActivityTaskCommandAttributes = (
    RequestCancelActivityTaskCommandAttributes
)

class StartTimerCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIMER_ID_FIELD_NUMBER: builtins.int
    START_TO_FIRE_TIMEOUT_FIELD_NUMBER: builtins.int
    timer_id: builtins.str
    """An id for the timer, currently live timers must have different ids. Typically autogenerated
    by the SDK.
    """
    @property
    def start_to_fire_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """How long until the timer fires, producing a `TIMER_FIRED` event.

        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    def __init__(
        self,
        *,
        timer_id: builtins.str = ...,
        start_to_fire_timeout: google.protobuf.duration_pb2.Duration | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "start_to_fire_timeout", b"start_to_fire_timeout"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "start_to_fire_timeout", b"start_to_fire_timeout", "timer_id", b"timer_id"
        ],
    ) -> None: ...

global___StartTimerCommandAttributes = StartTimerCommandAttributes

class CompleteWorkflowExecutionCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> temporalio.api.common.v1.message_pb2.Payloads: ...
    def __init__(
        self,
        *,
        result: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["result", b"result"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["result", b"result"]
    ) -> None: ...

global___CompleteWorkflowExecutionCommandAttributes = (
    CompleteWorkflowExecutionCommandAttributes
)

class FailWorkflowExecutionCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FAILURE_FIELD_NUMBER: builtins.int
    @property
    def failure(self) -> temporalio.api.failure.v1.message_pb2.Failure: ...
    def __init__(
        self,
        *,
        failure: temporalio.api.failure.v1.message_pb2.Failure | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["failure", b"failure"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["failure", b"failure"]
    ) -> None: ...

global___FailWorkflowExecutionCommandAttributes = FailWorkflowExecutionCommandAttributes

class CancelTimerCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIMER_ID_FIELD_NUMBER: builtins.int
    timer_id: builtins.str
    """The same timer id from the start timer command"""
    def __init__(
        self,
        *,
        timer_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["timer_id", b"timer_id"]
    ) -> None: ...

global___CancelTimerCommandAttributes = CancelTimerCommandAttributes

class CancelWorkflowExecutionCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DETAILS_FIELD_NUMBER: builtins.int
    @property
    def details(self) -> temporalio.api.common.v1.message_pb2.Payloads: ...
    def __init__(
        self,
        *,
        details: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["details", b"details"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["details", b"details"]
    ) -> None: ...

global___CancelWorkflowExecutionCommandAttributes = (
    CancelWorkflowExecutionCommandAttributes
)

class RequestCancelExternalWorkflowExecutionCommandAttributes(
    google.protobuf.message.Message
):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    WORKFLOW_ID_FIELD_NUMBER: builtins.int
    RUN_ID_FIELD_NUMBER: builtins.int
    CONTROL_FIELD_NUMBER: builtins.int
    CHILD_WORKFLOW_ONLY_FIELD_NUMBER: builtins.int
    REASON_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    workflow_id: builtins.str
    run_id: builtins.str
    control: builtins.str
    """Deprecated."""
    child_workflow_only: builtins.bool
    """Set this to true if the workflow being cancelled is a child of the workflow originating this
    command. The request will be rejected if it is set to true and the target workflow is *not*
    a child of the requesting workflow.
    """
    reason: builtins.str
    """Reason for requesting the cancellation"""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        workflow_id: builtins.str = ...,
        run_id: builtins.str = ...,
        control: builtins.str = ...,
        child_workflow_only: builtins.bool = ...,
        reason: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "child_workflow_only",
            b"child_workflow_only",
            "control",
            b"control",
            "namespace",
            b"namespace",
            "reason",
            b"reason",
            "run_id",
            b"run_id",
            "workflow_id",
            b"workflow_id",
        ],
    ) -> None: ...

global___RequestCancelExternalWorkflowExecutionCommandAttributes = (
    RequestCancelExternalWorkflowExecutionCommandAttributes
)

class SignalExternalWorkflowExecutionCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    EXECUTION_FIELD_NUMBER: builtins.int
    SIGNAL_NAME_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    CONTROL_FIELD_NUMBER: builtins.int
    CHILD_WORKFLOW_ONLY_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    @property
    def execution(self) -> temporalio.api.common.v1.message_pb2.WorkflowExecution: ...
    signal_name: builtins.str
    """The workflow author-defined name of the signal to send to the workflow."""
    @property
    def input(self) -> temporalio.api.common.v1.message_pb2.Payloads:
        """Serialized value(s) to provide with the signal."""
    control: builtins.str
    """Deprecated"""
    child_workflow_only: builtins.bool
    """Set this to true if the workflow being cancelled is a child of the workflow originating this
    command. The request will be rejected if it is set to true and the target workflow is *not*
    a child of the requesting workflow.
    """
    @property
    def header(self) -> temporalio.api.common.v1.message_pb2.Header:
        """Headers that are passed by the workflow that is sending a signal to the external
        workflow that is receiving this signal.
        """
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        execution: temporalio.api.common.v1.message_pb2.WorkflowExecution | None = ...,
        signal_name: builtins.str = ...,
        input: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
        control: builtins.str = ...,
        child_workflow_only: builtins.bool = ...,
        header: temporalio.api.common.v1.message_pb2.Header | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "execution", b"execution", "header", b"header", "input", b"input"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "child_workflow_only",
            b"child_workflow_only",
            "control",
            b"control",
            "execution",
            b"execution",
            "header",
            b"header",
            "input",
            b"input",
            "namespace",
            b"namespace",
            "signal_name",
            b"signal_name",
        ],
    ) -> None: ...

global___SignalExternalWorkflowExecutionCommandAttributes = (
    SignalExternalWorkflowExecutionCommandAttributes
)

class UpsertWorkflowSearchAttributesCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SEARCH_ATTRIBUTES_FIELD_NUMBER: builtins.int
    @property
    def search_attributes(
        self,
    ) -> temporalio.api.common.v1.message_pb2.SearchAttributes: ...
    def __init__(
        self,
        *,
        search_attributes: temporalio.api.common.v1.message_pb2.SearchAttributes
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "search_attributes", b"search_attributes"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "search_attributes", b"search_attributes"
        ],
    ) -> None: ...

global___UpsertWorkflowSearchAttributesCommandAttributes = (
    UpsertWorkflowSearchAttributesCommandAttributes
)

class ModifyWorkflowPropertiesCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    UPSERTED_MEMO_FIELD_NUMBER: builtins.int
    @property
    def upserted_memo(self) -> temporalio.api.common.v1.message_pb2.Memo:
        """If set, update the workflow memo with the provided values. The values will be merged with
        the existing memo. If the user wants to delete values, a default/empty Payload should be
        used as the value for the key being deleted.
        """
    def __init__(
        self,
        *,
        upserted_memo: temporalio.api.common.v1.message_pb2.Memo | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["upserted_memo", b"upserted_memo"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["upserted_memo", b"upserted_memo"]
    ) -> None: ...

global___ModifyWorkflowPropertiesCommandAttributes = (
    ModifyWorkflowPropertiesCommandAttributes
)

class RecordMarkerCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class DetailsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> temporalio.api.common.v1.message_pb2.Payloads: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["value", b"value"]
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    MARKER_NAME_FIELD_NUMBER: builtins.int
    DETAILS_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    FAILURE_FIELD_NUMBER: builtins.int
    marker_name: builtins.str
    @property
    def details(
        self,
    ) -> google.protobuf.internal.containers.MessageMap[
        builtins.str, temporalio.api.common.v1.message_pb2.Payloads
    ]: ...
    @property
    def header(self) -> temporalio.api.common.v1.message_pb2.Header: ...
    @property
    def failure(self) -> temporalio.api.failure.v1.message_pb2.Failure: ...
    def __init__(
        self,
        *,
        marker_name: builtins.str = ...,
        details: collections.abc.Mapping[
            builtins.str, temporalio.api.common.v1.message_pb2.Payloads
        ]
        | None = ...,
        header: temporalio.api.common.v1.message_pb2.Header | None = ...,
        failure: temporalio.api.failure.v1.message_pb2.Failure | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "failure", b"failure", "header", b"header"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "details",
            b"details",
            "failure",
            b"failure",
            "header",
            b"header",
            "marker_name",
            b"marker_name",
        ],
    ) -> None: ...

global___RecordMarkerCommandAttributes = RecordMarkerCommandAttributes

class ContinueAsNewWorkflowExecutionCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WORKFLOW_TYPE_FIELD_NUMBER: builtins.int
    TASK_QUEUE_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    WORKFLOW_RUN_TIMEOUT_FIELD_NUMBER: builtins.int
    WORKFLOW_TASK_TIMEOUT_FIELD_NUMBER: builtins.int
    BACKOFF_START_INTERVAL_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    INITIATOR_FIELD_NUMBER: builtins.int
    FAILURE_FIELD_NUMBER: builtins.int
    LAST_COMPLETION_RESULT_FIELD_NUMBER: builtins.int
    CRON_SCHEDULE_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    MEMO_FIELD_NUMBER: builtins.int
    SEARCH_ATTRIBUTES_FIELD_NUMBER: builtins.int
    INHERIT_BUILD_ID_FIELD_NUMBER: builtins.int
    @property
    def workflow_type(self) -> temporalio.api.common.v1.message_pb2.WorkflowType: ...
    @property
    def task_queue(self) -> temporalio.api.taskqueue.v1.message_pb2.TaskQueue: ...
    @property
    def input(self) -> temporalio.api.common.v1.message_pb2.Payloads: ...
    @property
    def workflow_run_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Timeout of a single workflow run."""
    @property
    def workflow_task_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Timeout of a single workflow task."""
    @property
    def backoff_start_interval(self) -> google.protobuf.duration_pb2.Duration:
        """How long the workflow start will be delayed - not really a "backoff" in the traditional sense."""
    @property
    def retry_policy(self) -> temporalio.api.common.v1.message_pb2.RetryPolicy: ...
    initiator: temporalio.api.enums.v1.workflow_pb2.ContinueAsNewInitiator.ValueType
    """Should be removed"""
    @property
    def failure(self) -> temporalio.api.failure.v1.message_pb2.Failure:
        """Should be removed"""
    @property
    def last_completion_result(self) -> temporalio.api.common.v1.message_pb2.Payloads:
        """Should be removed"""
    cron_schedule: builtins.str
    """Should be removed. Not necessarily unused but unclear and not exposed by SDKs."""
    @property
    def header(self) -> temporalio.api.common.v1.message_pb2.Header: ...
    @property
    def memo(self) -> temporalio.api.common.v1.message_pb2.Memo: ...
    @property
    def search_attributes(
        self,
    ) -> temporalio.api.common.v1.message_pb2.SearchAttributes: ...
    inherit_build_id: builtins.bool
    """If this is set, the new execution inherits the Build ID of the current execution. Otherwise,
    the assignment rules will be used to independently assign a Build ID to the new execution.
    Deprecated. Only considered for versioning v0.2.
    """
    def __init__(
        self,
        *,
        workflow_type: temporalio.api.common.v1.message_pb2.WorkflowType | None = ...,
        task_queue: temporalio.api.taskqueue.v1.message_pb2.TaskQueue | None = ...,
        input: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
        workflow_run_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        workflow_task_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        backoff_start_interval: google.protobuf.duration_pb2.Duration | None = ...,
        retry_policy: temporalio.api.common.v1.message_pb2.RetryPolicy | None = ...,
        initiator: temporalio.api.enums.v1.workflow_pb2.ContinueAsNewInitiator.ValueType = ...,
        failure: temporalio.api.failure.v1.message_pb2.Failure | None = ...,
        last_completion_result: temporalio.api.common.v1.message_pb2.Payloads
        | None = ...,
        cron_schedule: builtins.str = ...,
        header: temporalio.api.common.v1.message_pb2.Header | None = ...,
        memo: temporalio.api.common.v1.message_pb2.Memo | None = ...,
        search_attributes: temporalio.api.common.v1.message_pb2.SearchAttributes
        | None = ...,
        inherit_build_id: builtins.bool = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "backoff_start_interval",
            b"backoff_start_interval",
            "failure",
            b"failure",
            "header",
            b"header",
            "input",
            b"input",
            "last_completion_result",
            b"last_completion_result",
            "memo",
            b"memo",
            "retry_policy",
            b"retry_policy",
            "search_attributes",
            b"search_attributes",
            "task_queue",
            b"task_queue",
            "workflow_run_timeout",
            b"workflow_run_timeout",
            "workflow_task_timeout",
            b"workflow_task_timeout",
            "workflow_type",
            b"workflow_type",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "backoff_start_interval",
            b"backoff_start_interval",
            "cron_schedule",
            b"cron_schedule",
            "failure",
            b"failure",
            "header",
            b"header",
            "inherit_build_id",
            b"inherit_build_id",
            "initiator",
            b"initiator",
            "input",
            b"input",
            "last_completion_result",
            b"last_completion_result",
            "memo",
            b"memo",
            "retry_policy",
            b"retry_policy",
            "search_attributes",
            b"search_attributes",
            "task_queue",
            b"task_queue",
            "workflow_run_timeout",
            b"workflow_run_timeout",
            "workflow_task_timeout",
            b"workflow_task_timeout",
            "workflow_type",
            b"workflow_type",
        ],
    ) -> None: ...

global___ContinueAsNewWorkflowExecutionCommandAttributes = (
    ContinueAsNewWorkflowExecutionCommandAttributes
)

class StartChildWorkflowExecutionCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    WORKFLOW_ID_FIELD_NUMBER: builtins.int
    WORKFLOW_TYPE_FIELD_NUMBER: builtins.int
    TASK_QUEUE_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    WORKFLOW_EXECUTION_TIMEOUT_FIELD_NUMBER: builtins.int
    WORKFLOW_RUN_TIMEOUT_FIELD_NUMBER: builtins.int
    WORKFLOW_TASK_TIMEOUT_FIELD_NUMBER: builtins.int
    PARENT_CLOSE_POLICY_FIELD_NUMBER: builtins.int
    CONTROL_FIELD_NUMBER: builtins.int
    WORKFLOW_ID_REUSE_POLICY_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    CRON_SCHEDULE_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    MEMO_FIELD_NUMBER: builtins.int
    SEARCH_ATTRIBUTES_FIELD_NUMBER: builtins.int
    INHERIT_BUILD_ID_FIELD_NUMBER: builtins.int
    PRIORITY_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    workflow_id: builtins.str
    @property
    def workflow_type(self) -> temporalio.api.common.v1.message_pb2.WorkflowType: ...
    @property
    def task_queue(self) -> temporalio.api.taskqueue.v1.message_pb2.TaskQueue: ...
    @property
    def input(self) -> temporalio.api.common.v1.message_pb2.Payloads: ...
    @property
    def workflow_execution_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Total workflow execution timeout including retries and continue as new."""
    @property
    def workflow_run_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Timeout of a single workflow run."""
    @property
    def workflow_task_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Timeout of a single workflow task."""
    parent_close_policy: (
        temporalio.api.enums.v1.workflow_pb2.ParentClosePolicy.ValueType
    )
    """Default: PARENT_CLOSE_POLICY_TERMINATE."""
    control: builtins.str
    workflow_id_reuse_policy: (
        temporalio.api.enums.v1.workflow_pb2.WorkflowIdReusePolicy.ValueType
    )
    """Default: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE."""
    @property
    def retry_policy(self) -> temporalio.api.common.v1.message_pb2.RetryPolicy: ...
    cron_schedule: builtins.str
    """Establish a cron schedule for the child workflow."""
    @property
    def header(self) -> temporalio.api.common.v1.message_pb2.Header: ...
    @property
    def memo(self) -> temporalio.api.common.v1.message_pb2.Memo: ...
    @property
    def search_attributes(
        self,
    ) -> temporalio.api.common.v1.message_pb2.SearchAttributes: ...
    inherit_build_id: builtins.bool
    """If this is set, the child workflow inherits the Build ID of the parent. Otherwise, the assignment
    rules of the child's Task Queue will be used to independently assign a Build ID to it.
    Deprecated. Only considered for versioning v0.2.
    """
    @property
    def priority(self) -> temporalio.api.common.v1.message_pb2.Priority:
        """Priority metadata. If this message is not present, or any fields are not
        present, they inherit the values from the workflow.
        """
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        workflow_id: builtins.str = ...,
        workflow_type: temporalio.api.common.v1.message_pb2.WorkflowType | None = ...,
        task_queue: temporalio.api.taskqueue.v1.message_pb2.TaskQueue | None = ...,
        input: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
        workflow_execution_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        workflow_run_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        workflow_task_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        parent_close_policy: temporalio.api.enums.v1.workflow_pb2.ParentClosePolicy.ValueType = ...,
        control: builtins.str = ...,
        workflow_id_reuse_policy: temporalio.api.enums.v1.workflow_pb2.WorkflowIdReusePolicy.ValueType = ...,
        retry_policy: temporalio.api.common.v1.message_pb2.RetryPolicy | None = ...,
        cron_schedule: builtins.str = ...,
        header: temporalio.api.common.v1.message_pb2.Header | None = ...,
        memo: temporalio.api.common.v1.message_pb2.Memo | None = ...,
        search_attributes: temporalio.api.common.v1.message_pb2.SearchAttributes
        | None = ...,
        inherit_build_id: builtins.bool = ...,
        priority: temporalio.api.common.v1.message_pb2.Priority | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "header",
            b"header",
            "input",
            b"input",
            "memo",
            b"memo",
            "priority",
            b"priority",
            "retry_policy",
            b"retry_policy",
            "search_attributes",
            b"search_attributes",
            "task_queue",
            b"task_queue",
            "workflow_execution_timeout",
            b"workflow_execution_timeout",
            "workflow_run_timeout",
            b"workflow_run_timeout",
            "workflow_task_timeout",
            b"workflow_task_timeout",
            "workflow_type",
            b"workflow_type",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "control",
            b"control",
            "cron_schedule",
            b"cron_schedule",
            "header",
            b"header",
            "inherit_build_id",
            b"inherit_build_id",
            "input",
            b"input",
            "memo",
            b"memo",
            "namespace",
            b"namespace",
            "parent_close_policy",
            b"parent_close_policy",
            "priority",
            b"priority",
            "retry_policy",
            b"retry_policy",
            "search_attributes",
            b"search_attributes",
            "task_queue",
            b"task_queue",
            "workflow_execution_timeout",
            b"workflow_execution_timeout",
            "workflow_id",
            b"workflow_id",
            "workflow_id_reuse_policy",
            b"workflow_id_reuse_policy",
            "workflow_run_timeout",
            b"workflow_run_timeout",
            "workflow_task_timeout",
            b"workflow_task_timeout",
            "workflow_type",
            b"workflow_type",
        ],
    ) -> None: ...

global___StartChildWorkflowExecutionCommandAttributes = (
    StartChildWorkflowExecutionCommandAttributes
)

class ProtocolMessageCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_ID_FIELD_NUMBER: builtins.int
    message_id: builtins.str
    """The message ID of the message to which this command is a pointer."""
    def __init__(
        self,
        *,
        message_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["message_id", b"message_id"]
    ) -> None: ...

global___ProtocolMessageCommandAttributes = ProtocolMessageCommandAttributes

class ScheduleNexusOperationCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class NexusHeaderEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    ENDPOINT_FIELD_NUMBER: builtins.int
    SERVICE_FIELD_NUMBER: builtins.int
    OPERATION_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    SCHEDULE_TO_CLOSE_TIMEOUT_FIELD_NUMBER: builtins.int
    NEXUS_HEADER_FIELD_NUMBER: builtins.int
    endpoint: builtins.str
    """Endpoint name, must exist in the endpoint registry or this command will fail."""
    service: builtins.str
    """Service name."""
    operation: builtins.str
    """Operation name."""
    @property
    def input(self) -> temporalio.api.common.v1.message_pb2.Payload:
        """Input for the operation. The server converts this into Nexus request content and the appropriate content headers
        internally when sending the StartOperation request. On the handler side, if it is also backed by Temporal, the
        content is transformed back to the original Payload sent in this command.
        """
    @property
    def schedule_to_close_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Schedule-to-close timeout for this operation.
        Indicates how long the caller is willing to wait for operation completion.
        Calls are retried internally by the server.
        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    @property
    def nexus_header(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """Header to attach to the Nexus request.
        Users are responsible for encrypting sensitive data in this header as it is stored in workflow history and
        transmitted to external services as-is.
        This is useful for propagating tracing information.
        Note these headers are not the same as Temporal headers on internal activities and child workflows, these are
        transmitted to Nexus operations that may be external and are not traditional payloads.
        """
    def __init__(
        self,
        *,
        endpoint: builtins.str = ...,
        service: builtins.str = ...,
        operation: builtins.str = ...,
        input: temporalio.api.common.v1.message_pb2.Payload | None = ...,
        schedule_to_close_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        nexus_header: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "input", b"input", "schedule_to_close_timeout", b"schedule_to_close_timeout"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "endpoint",
            b"endpoint",
            "input",
            b"input",
            "nexus_header",
            b"nexus_header",
            "operation",
            b"operation",
            "schedule_to_close_timeout",
            b"schedule_to_close_timeout",
            "service",
            b"service",
        ],
    ) -> None: ...

global___ScheduleNexusOperationCommandAttributes = (
    ScheduleNexusOperationCommandAttributes
)

class RequestCancelNexusOperationCommandAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SCHEDULED_EVENT_ID_FIELD_NUMBER: builtins.int
    scheduled_event_id: builtins.int
    """The `NEXUS_OPERATION_SCHEDULED` event ID (a unique identifier) for the operation to be canceled.
    The operation may ignore cancellation and end up with any completion state.
    """
    def __init__(
        self,
        *,
        scheduled_event_id: builtins.int = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "scheduled_event_id", b"scheduled_event_id"
        ],
    ) -> None: ...

global___RequestCancelNexusOperationCommandAttributes = (
    RequestCancelNexusOperationCommandAttributes
)

class Command(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COMMAND_TYPE_FIELD_NUMBER: builtins.int
    USER_METADATA_FIELD_NUMBER: builtins.int
    SCHEDULE_ACTIVITY_TASK_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    START_TIMER_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    COMPLETE_WORKFLOW_EXECUTION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    FAIL_WORKFLOW_EXECUTION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    REQUEST_CANCEL_ACTIVITY_TASK_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    CANCEL_TIMER_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    CANCEL_WORKFLOW_EXECUTION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    REQUEST_CANCEL_EXTERNAL_WORKFLOW_EXECUTION_COMMAND_ATTRIBUTES_FIELD_NUMBER: (
        builtins.int
    )
    RECORD_MARKER_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    CONTINUE_AS_NEW_WORKFLOW_EXECUTION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    START_CHILD_WORKFLOW_EXECUTION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    SIGNAL_EXTERNAL_WORKFLOW_EXECUTION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    UPSERT_WORKFLOW_SEARCH_ATTRIBUTES_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    PROTOCOL_MESSAGE_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    MODIFY_WORKFLOW_PROPERTIES_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    SCHEDULE_NEXUS_OPERATION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    REQUEST_CANCEL_NEXUS_OPERATION_COMMAND_ATTRIBUTES_FIELD_NUMBER: builtins.int
    command_type: temporalio.api.enums.v1.command_type_pb2.CommandType.ValueType
    @property
    def user_metadata(self) -> temporalio.api.sdk.v1.user_metadata_pb2.UserMetadata:
        """Metadata on the command. This is sometimes carried over to the history event if one is
        created as a result of the command. Most commands won't have this information, and how this
        information is used is dependent upon the interface that reads it.

        Current well-known uses:
         * start_child_workflow_execution_command_attributes - populates
           temporalio.api.workflow.v1.WorkflowExecutionInfo.user_metadata where the summary and details
           are used by user interfaces to show fixed as-of-start workflow summary and details.
         * start_timer_command_attributes - populates temporalio.api.history.v1.HistoryEvent for timer
           started where the summary is used to identify the timer.
        """
    @property
    def schedule_activity_task_command_attributes(
        self,
    ) -> global___ScheduleActivityTaskCommandAttributes: ...
    @property
    def start_timer_command_attributes(
        self,
    ) -> global___StartTimerCommandAttributes: ...
    @property
    def complete_workflow_execution_command_attributes(
        self,
    ) -> global___CompleteWorkflowExecutionCommandAttributes: ...
    @property
    def fail_workflow_execution_command_attributes(
        self,
    ) -> global___FailWorkflowExecutionCommandAttributes: ...
    @property
    def request_cancel_activity_task_command_attributes(
        self,
    ) -> global___RequestCancelActivityTaskCommandAttributes: ...
    @property
    def cancel_timer_command_attributes(
        self,
    ) -> global___CancelTimerCommandAttributes: ...
    @property
    def cancel_workflow_execution_command_attributes(
        self,
    ) -> global___CancelWorkflowExecutionCommandAttributes: ...
    @property
    def request_cancel_external_workflow_execution_command_attributes(
        self,
    ) -> global___RequestCancelExternalWorkflowExecutionCommandAttributes: ...
    @property
    def record_marker_command_attributes(
        self,
    ) -> global___RecordMarkerCommandAttributes: ...
    @property
    def continue_as_new_workflow_execution_command_attributes(
        self,
    ) -> global___ContinueAsNewWorkflowExecutionCommandAttributes: ...
    @property
    def start_child_workflow_execution_command_attributes(
        self,
    ) -> global___StartChildWorkflowExecutionCommandAttributes: ...
    @property
    def signal_external_workflow_execution_command_attributes(
        self,
    ) -> global___SignalExternalWorkflowExecutionCommandAttributes: ...
    @property
    def upsert_workflow_search_attributes_command_attributes(
        self,
    ) -> global___UpsertWorkflowSearchAttributesCommandAttributes: ...
    @property
    def protocol_message_command_attributes(
        self,
    ) -> global___ProtocolMessageCommandAttributes: ...
    @property
    def modify_workflow_properties_command_attributes(
        self,
    ) -> global___ModifyWorkflowPropertiesCommandAttributes:
        """16 is available for use - it was used as part of a prototype that never made it into a release"""
    @property
    def schedule_nexus_operation_command_attributes(
        self,
    ) -> global___ScheduleNexusOperationCommandAttributes: ...
    @property
    def request_cancel_nexus_operation_command_attributes(
        self,
    ) -> global___RequestCancelNexusOperationCommandAttributes: ...
    def __init__(
        self,
        *,
        command_type: temporalio.api.enums.v1.command_type_pb2.CommandType.ValueType = ...,
        user_metadata: temporalio.api.sdk.v1.user_metadata_pb2.UserMetadata
        | None = ...,
        schedule_activity_task_command_attributes: global___ScheduleActivityTaskCommandAttributes
        | None = ...,
        start_timer_command_attributes: global___StartTimerCommandAttributes
        | None = ...,
        complete_workflow_execution_command_attributes: global___CompleteWorkflowExecutionCommandAttributes
        | None = ...,
        fail_workflow_execution_command_attributes: global___FailWorkflowExecutionCommandAttributes
        | None = ...,
        request_cancel_activity_task_command_attributes: global___RequestCancelActivityTaskCommandAttributes
        | None = ...,
        cancel_timer_command_attributes: global___CancelTimerCommandAttributes
        | None = ...,
        cancel_workflow_execution_command_attributes: global___CancelWorkflowExecutionCommandAttributes
        | None = ...,
        request_cancel_external_workflow_execution_command_attributes: global___RequestCancelExternalWorkflowExecutionCommandAttributes
        | None = ...,
        record_marker_command_attributes: global___RecordMarkerCommandAttributes
        | None = ...,
        continue_as_new_workflow_execution_command_attributes: global___ContinueAsNewWorkflowExecutionCommandAttributes
        | None = ...,
        start_child_workflow_execution_command_attributes: global___StartChildWorkflowExecutionCommandAttributes
        | None = ...,
        signal_external_workflow_execution_command_attributes: global___SignalExternalWorkflowExecutionCommandAttributes
        | None = ...,
        upsert_workflow_search_attributes_command_attributes: global___UpsertWorkflowSearchAttributesCommandAttributes
        | None = ...,
        protocol_message_command_attributes: global___ProtocolMessageCommandAttributes
        | None = ...,
        modify_workflow_properties_command_attributes: global___ModifyWorkflowPropertiesCommandAttributes
        | None = ...,
        schedule_nexus_operation_command_attributes: global___ScheduleNexusOperationCommandAttributes
        | None = ...,
        request_cancel_nexus_operation_command_attributes: global___RequestCancelNexusOperationCommandAttributes
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "attributes",
            b"attributes",
            "cancel_timer_command_attributes",
            b"cancel_timer_command_attributes",
            "cancel_workflow_execution_command_attributes",
            b"cancel_workflow_execution_command_attributes",
            "complete_workflow_execution_command_attributes",
            b"complete_workflow_execution_command_attributes",
            "continue_as_new_workflow_execution_command_attributes",
            b"continue_as_new_workflow_execution_command_attributes",
            "fail_workflow_execution_command_attributes",
            b"fail_workflow_execution_command_attributes",
            "modify_workflow_properties_command_attributes",
            b"modify_workflow_properties_command_attributes",
            "protocol_message_command_attributes",
            b"protocol_message_command_attributes",
            "record_marker_command_attributes",
            b"record_marker_command_attributes",
            "request_cancel_activity_task_command_attributes",
            b"request_cancel_activity_task_command_attributes",
            "request_cancel_external_workflow_execution_command_attributes",
            b"request_cancel_external_workflow_execution_command_attributes",
            "request_cancel_nexus_operation_command_attributes",
            b"request_cancel_nexus_operation_command_attributes",
            "schedule_activity_task_command_attributes",
            b"schedule_activity_task_command_attributes",
            "schedule_nexus_operation_command_attributes",
            b"schedule_nexus_operation_command_attributes",
            "signal_external_workflow_execution_command_attributes",
            b"signal_external_workflow_execution_command_attributes",
            "start_child_workflow_execution_command_attributes",
            b"start_child_workflow_execution_command_attributes",
            "start_timer_command_attributes",
            b"start_timer_command_attributes",
            "upsert_workflow_search_attributes_command_attributes",
            b"upsert_workflow_search_attributes_command_attributes",
            "user_metadata",
            b"user_metadata",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "attributes",
            b"attributes",
            "cancel_timer_command_attributes",
            b"cancel_timer_command_attributes",
            "cancel_workflow_execution_command_attributes",
            b"cancel_workflow_execution_command_attributes",
            "command_type",
            b"command_type",
            "complete_workflow_execution_command_attributes",
            b"complete_workflow_execution_command_attributes",
            "continue_as_new_workflow_execution_command_attributes",
            b"continue_as_new_workflow_execution_command_attributes",
            "fail_workflow_execution_command_attributes",
            b"fail_workflow_execution_command_attributes",
            "modify_workflow_properties_command_attributes",
            b"modify_workflow_properties_command_attributes",
            "protocol_message_command_attributes",
            b"protocol_message_command_attributes",
            "record_marker_command_attributes",
            b"record_marker_command_attributes",
            "request_cancel_activity_task_command_attributes",
            b"request_cancel_activity_task_command_attributes",
            "request_cancel_external_workflow_execution_command_attributes",
            b"request_cancel_external_workflow_execution_command_attributes",
            "request_cancel_nexus_operation_command_attributes",
            b"request_cancel_nexus_operation_command_attributes",
            "schedule_activity_task_command_attributes",
            b"schedule_activity_task_command_attributes",
            "schedule_nexus_operation_command_attributes",
            b"schedule_nexus_operation_command_attributes",
            "signal_external_workflow_execution_command_attributes",
            b"signal_external_workflow_execution_command_attributes",
            "start_child_workflow_execution_command_attributes",
            b"start_child_workflow_execution_command_attributes",
            "start_timer_command_attributes",
            b"start_timer_command_attributes",
            "upsert_workflow_search_attributes_command_attributes",
            b"upsert_workflow_search_attributes_command_attributes",
            "user_metadata",
            b"user_metadata",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["attributes", b"attributes"]
    ) -> (
        typing_extensions.Literal[
            "schedule_activity_task_command_attributes",
            "start_timer_command_attributes",
            "complete_workflow_execution_command_attributes",
            "fail_workflow_execution_command_attributes",
            "request_cancel_activity_task_command_attributes",
            "cancel_timer_command_attributes",
            "cancel_workflow_execution_command_attributes",
            "request_cancel_external_workflow_execution_command_attributes",
            "record_marker_command_attributes",
            "continue_as_new_workflow_execution_command_attributes",
            "start_child_workflow_execution_command_attributes",
            "signal_external_workflow_execution_command_attributes",
            "upsert_workflow_search_attributes_command_attributes",
            "protocol_message_command_attributes",
            "modify_workflow_properties_command_attributes",
            "schedule_nexus_operation_command_attributes",
            "request_cancel_nexus_operation_command_attributes",
        ]
        | None
    ): ...

global___Command = Command
