# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/command/v1/message.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2

from temporalio.api.common.v1 import (
    message_pb2 as temporal_dot_api_dot_common_dot_v1_dot_message__pb2,
)
from temporalio.api.enums.v1 import (
    command_type_pb2 as temporal_dot_api_dot_enums_dot_v1_dot_command__type__pb2,
)
from temporalio.api.enums.v1 import (
    workflow_pb2 as temporal_dot_api_dot_enums_dot_v1_dot_workflow__pb2,
)
from temporalio.api.failure.v1 import (
    message_pb2 as temporal_dot_api_dot_failure_dot_v1_dot_message__pb2,
)
from temporalio.api.sdk.v1 import (
    user_metadata_pb2 as temporal_dot_api_dot_sdk_dot_v1_dot_user__metadata__pb2,
)
from temporalio.api.taskqueue.v1 import (
    message_pb2 as temporal_dot_api_dot_taskqueue_dot_v1_dot_message__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n%temporal/api/command/v1/message.proto\x12\x17temporal.api.command.v1\x1a\x1egoogle/protobuf/duration.proto\x1a$temporal/api/enums/v1/workflow.proto\x1a(temporal/api/enums/v1/command_type.proto\x1a$temporal/api/common/v1/message.proto\x1a%temporal/api/failure/v1/message.proto\x1a\'temporal/api/taskqueue/v1/message.proto\x1a\'temporal/api/sdk/v1/user_metadata.proto"\xb6\x05\n%ScheduleActivityTaskCommandAttributes\x12\x13\n\x0b\x61\x63tivity_id\x18\x01 \x01(\t\x12;\n\ractivity_type\x18\x02 \x01(\x0b\x32$.temporal.api.common.v1.ActivityType\x12\x38\n\ntask_queue\x18\x04 \x01(\x0b\x32$.temporal.api.taskqueue.v1.TaskQueue\x12.\n\x06header\x18\x05 \x01(\x0b\x32\x1e.temporal.api.common.v1.Header\x12/\n\x05input\x18\x06 \x01(\x0b\x32 .temporal.api.common.v1.Payloads\x12<\n\x19schedule_to_close_timeout\x18\x07 \x01(\x0b\x32\x19.google.protobuf.Duration\x12<\n\x19schedule_to_start_timeout\x18\x08 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x39\n\x16start_to_close_timeout\x18\t \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x34\n\x11heartbeat_timeout\x18\n \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x39\n\x0cretry_policy\x18\x0b \x01(\x0b\x32#.temporal.api.common.v1.RetryPolicy\x12\x1f\n\x17request_eager_execution\x18\x0c \x01(\x08\x12\x1d\n\x15use_workflow_build_id\x18\r \x01(\x08\x12\x32\n\x08priority\x18\x0e \x01(\x0b\x32 .temporal.api.common.v1.PriorityJ\x04\x08\x03\x10\x04"H\n*RequestCancelActivityTaskCommandAttributes\x12\x1a\n\x12scheduled_event_id\x18\x01 \x01(\x03"i\n\x1bStartTimerCommandAttributes\x12\x10\n\x08timer_id\x18\x01 \x01(\t\x12\x38\n\x15start_to_fire_timeout\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration"^\n*CompleteWorkflowExecutionCommandAttributes\x12\x30\n\x06result\x18\x01 \x01(\x0b\x32 .temporal.api.common.v1.Payloads"[\n&FailWorkflowExecutionCommandAttributes\x12\x31\n\x07\x66\x61ilure\x18\x01 \x01(\x0b\x32 .temporal.api.failure.v1.Failure"0\n\x1c\x43\x61ncelTimerCommandAttributes\x12\x10\n\x08timer_id\x18\x01 \x01(\t"]\n(CancelWorkflowExecutionCommandAttributes\x12\x31\n\x07\x64\x65tails\x18\x01 \x01(\x0b\x32 .temporal.api.common.v1.Payloads"\xb3\x01\n7RequestCancelExternalWorkflowExecutionCommandAttributes\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12\x13\n\x0bworkflow_id\x18\x02 \x01(\t\x12\x0e\n\x06run_id\x18\x03 \x01(\t\x12\x13\n\x07\x63ontrol\x18\x04 \x01(\tB\x02\x18\x01\x12\x1b\n\x13\x63hild_workflow_only\x18\x05 \x01(\x08\x12\x0e\n\x06reason\x18\x06 \x01(\t"\xab\x02\n0SignalExternalWorkflowExecutionCommandAttributes\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12<\n\texecution\x18\x02 \x01(\x0b\x32).temporal.api.common.v1.WorkflowExecution\x12\x13\n\x0bsignal_name\x18\x03 \x01(\t\x12/\n\x05input\x18\x04 \x01(\x0b\x32 .temporal.api.common.v1.Payloads\x12\x13\n\x07\x63ontrol\x18\x05 \x01(\tB\x02\x18\x01\x12\x1b\n\x13\x63hild_workflow_only\x18\x06 \x01(\x08\x12.\n\x06header\x18\x07 \x01(\x0b\x32\x1e.temporal.api.common.v1.Header"v\n/UpsertWorkflowSearchAttributesCommandAttributes\x12\x43\n\x11search_attributes\x18\x01 \x01(\x0b\x32(.temporal.api.common.v1.SearchAttributes"`\n)ModifyWorkflowPropertiesCommandAttributes\x12\x33\n\rupserted_memo\x18\x01 \x01(\x0b\x32\x1c.temporal.api.common.v1.Memo"\xbf\x02\n\x1dRecordMarkerCommandAttributes\x12\x13\n\x0bmarker_name\x18\x01 \x01(\t\x12T\n\x07\x64\x65tails\x18\x02 \x03(\x0b\x32\x43.temporal.api.command.v1.RecordMarkerCommandAttributes.DetailsEntry\x12.\n\x06header\x18\x03 \x01(\x0b\x32\x1e.temporal.api.common.v1.Header\x12\x31\n\x07\x66\x61ilure\x18\x04 \x01(\x0b\x32 .temporal.api.failure.v1.Failure\x1aP\n\x0c\x44\x65tailsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .temporal.api.common.v1.Payloads:\x02\x38\x01"\xcf\x06\n/ContinueAsNewWorkflowExecutionCommandAttributes\x12;\n\rworkflow_type\x18\x01 \x01(\x0b\x32$.temporal.api.common.v1.WorkflowType\x12\x38\n\ntask_queue\x18\x02 \x01(\x0b\x32$.temporal.api.taskqueue.v1.TaskQueue\x12/\n\x05input\x18\x03 \x01(\x0b\x32 .temporal.api.common.v1.Payloads\x12\x37\n\x14workflow_run_timeout\x18\x04 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x38\n\x15workflow_task_timeout\x18\x05 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x39\n\x16\x62\x61\x63koff_start_interval\x18\x06 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x39\n\x0cretry_policy\x18\x07 \x01(\x0b\x32#.temporal.api.common.v1.RetryPolicy\x12@\n\tinitiator\x18\x08 \x01(\x0e\x32-.temporal.api.enums.v1.ContinueAsNewInitiator\x12\x31\n\x07\x66\x61ilure\x18\t \x01(\x0b\x32 .temporal.api.failure.v1.Failure\x12@\n\x16last_completion_result\x18\n \x01(\x0b\x32 .temporal.api.common.v1.Payloads\x12\x15\n\rcron_schedule\x18\x0b \x01(\t\x12.\n\x06header\x18\x0c \x01(\x0b\x32\x1e.temporal.api.common.v1.Header\x12*\n\x04memo\x18\r \x01(\x0b\x32\x1c.temporal.api.common.v1.Memo\x12\x43\n\x11search_attributes\x18\x0e \x01(\x0b\x32(.temporal.api.common.v1.SearchAttributes\x12\x1c\n\x10inherit_build_id\x18\x0f \x01(\x08\x42\x02\x18\x01"\x9d\x07\n,StartChildWorkflowExecutionCommandAttributes\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12\x13\n\x0bworkflow_id\x18\x02 \x01(\t\x12;\n\rworkflow_type\x18\x03 \x01(\x0b\x32$.temporal.api.common.v1.WorkflowType\x12\x38\n\ntask_queue\x18\x04 \x01(\x0b\x32$.temporal.api.taskqueue.v1.TaskQueue\x12/\n\x05input\x18\x05 \x01(\x0b\x32 .temporal.api.common.v1.Payloads\x12=\n\x1aworkflow_execution_timeout\x18\x06 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x37\n\x14workflow_run_timeout\x18\x07 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x38\n\x15workflow_task_timeout\x18\x08 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x45\n\x13parent_close_policy\x18\t \x01(\x0e\x32(.temporal.api.enums.v1.ParentClosePolicy\x12\x0f\n\x07\x63ontrol\x18\n \x01(\t\x12N\n\x18workflow_id_reuse_policy\x18\x0b \x01(\x0e\x32,.temporal.api.enums.v1.WorkflowIdReusePolicy\x12\x39\n\x0cretry_policy\x18\x0c \x01(\x0b\x32#.temporal.api.common.v1.RetryPolicy\x12\x15\n\rcron_schedule\x18\r \x01(\t\x12.\n\x06header\x18\x0e \x01(\x0b\x32\x1e.temporal.api.common.v1.Header\x12*\n\x04memo\x18\x0f \x01(\x0b\x32\x1c.temporal.api.common.v1.Memo\x12\x43\n\x11search_attributes\x18\x10 \x01(\x0b\x32(.temporal.api.common.v1.SearchAttributes\x12\x1c\n\x10inherit_build_id\x18\x11 \x01(\x08\x42\x02\x18\x01\x12\x32\n\x08priority\x18\x12 \x01(\x0b\x32 .temporal.api.common.v1.Priority"6\n ProtocolMessageCommandAttributes\x12\x12\n\nmessage_id\x18\x01 \x01(\t"\xea\x02\n\'ScheduleNexusOperationCommandAttributes\x12\x10\n\x08\x65ndpoint\x18\x01 \x01(\t\x12\x0f\n\x07service\x18\x02 \x01(\t\x12\x11\n\toperation\x18\x03 \x01(\t\x12.\n\x05input\x18\x04 \x01(\x0b\x32\x1f.temporal.api.common.v1.Payload\x12<\n\x19schedule_to_close_timeout\x18\x05 \x01(\x0b\x32\x19.google.protobuf.Duration\x12g\n\x0cnexus_header\x18\x06 \x03(\x0b\x32Q.temporal.api.command.v1.ScheduleNexusOperationCommandAttributes.NexusHeaderEntry\x1a\x32\n\x10NexusHeaderEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01"J\n,RequestCancelNexusOperationCommandAttributes\x12\x1a\n\x12scheduled_event_id\x18\x01 \x01(\x03"\xc2\x11\n\x07\x43ommand\x12\x38\n\x0c\x63ommand_type\x18\x01 \x01(\x0e\x32".temporal.api.enums.v1.CommandType\x12\x39\n\ruser_metadata\x18\xad\x02 \x01(\x0b\x32!.temporal.api.sdk.v1.UserMetadata\x12s\n)schedule_activity_task_command_attributes\x18\x02 \x01(\x0b\x32>.temporal.api.command.v1.ScheduleActivityTaskCommandAttributesH\x00\x12^\n\x1estart_timer_command_attributes\x18\x03 \x01(\x0b\x32\x34.temporal.api.command.v1.StartTimerCommandAttributesH\x00\x12}\n.complete_workflow_execution_command_attributes\x18\x04 \x01(\x0b\x32\x43.temporal.api.command.v1.CompleteWorkflowExecutionCommandAttributesH\x00\x12u\n*fail_workflow_execution_command_attributes\x18\x05 \x01(\x0b\x32?.temporal.api.command.v1.FailWorkflowExecutionCommandAttributesH\x00\x12~\n/request_cancel_activity_task_command_attributes\x18\x06 \x01(\x0b\x32\x43.temporal.api.command.v1.RequestCancelActivityTaskCommandAttributesH\x00\x12`\n\x1f\x63\x61ncel_timer_command_attributes\x18\x07 \x01(\x0b\x32\x35.temporal.api.command.v1.CancelTimerCommandAttributesH\x00\x12y\n,cancel_workflow_execution_command_attributes\x18\x08 \x01(\x0b\x32\x41.temporal.api.command.v1.CancelWorkflowExecutionCommandAttributesH\x00\x12\x99\x01\n=request_cancel_external_workflow_execution_command_attributes\x18\t \x01(\x0b\x32P.temporal.api.command.v1.RequestCancelExternalWorkflowExecutionCommandAttributesH\x00\x12\x62\n record_marker_command_attributes\x18\n \x01(\x0b\x32\x36.temporal.api.command.v1.RecordMarkerCommandAttributesH\x00\x12\x89\x01\n5continue_as_new_workflow_execution_command_attributes\x18\x0b \x01(\x0b\x32H.temporal.api.command.v1.ContinueAsNewWorkflowExecutionCommandAttributesH\x00\x12\x82\x01\n1start_child_workflow_execution_command_attributes\x18\x0c \x01(\x0b\x32\x45.temporal.api.command.v1.StartChildWorkflowExecutionCommandAttributesH\x00\x12\x8a\x01\n5signal_external_workflow_execution_command_attributes\x18\r \x01(\x0b\x32I.temporal.api.command.v1.SignalExternalWorkflowExecutionCommandAttributesH\x00\x12\x88\x01\n4upsert_workflow_search_attributes_command_attributes\x18\x0e \x01(\x0b\x32H.temporal.api.command.v1.UpsertWorkflowSearchAttributesCommandAttributesH\x00\x12h\n#protocol_message_command_attributes\x18\x0f \x01(\x0b\x32\x39.temporal.api.command.v1.ProtocolMessageCommandAttributesH\x00\x12{\n-modify_workflow_properties_command_attributes\x18\x11 \x01(\x0b\x32\x42.temporal.api.command.v1.ModifyWorkflowPropertiesCommandAttributesH\x00\x12w\n+schedule_nexus_operation_command_attributes\x18\x12 \x01(\x0b\<EMAIL>\x00\x12\x82\x01\n1request_cancel_nexus_operation_command_attributes\x18\x13 \x01(\x0b\x32\x45.temporal.api.command.v1.RequestCancelNexusOperationCommandAttributesH\x00\x42\x0c\n\nattributesB\x8e\x01\n\x1aio.temporal.api.command.v1B\x0cMessageProtoP\x01Z%go.temporal.io/api/command/v1;command\xaa\x02\x19Temporalio.Api.Command.V1\xea\x02\x1cTemporalio::Api::Command::V1b\x06proto3'
)


_SCHEDULEACTIVITYTASKCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "ScheduleActivityTaskCommandAttributes"
]
_REQUESTCANCELACTIVITYTASKCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "RequestCancelActivityTaskCommandAttributes"
]
_STARTTIMERCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "StartTimerCommandAttributes"
]
_COMPLETEWORKFLOWEXECUTIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "CompleteWorkflowExecutionCommandAttributes"
]
_FAILWORKFLOWEXECUTIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "FailWorkflowExecutionCommandAttributes"
]
_CANCELTIMERCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "CancelTimerCommandAttributes"
]
_CANCELWORKFLOWEXECUTIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "CancelWorkflowExecutionCommandAttributes"
]
_REQUESTCANCELEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES = (
    DESCRIPTOR.message_types_by_name[
        "RequestCancelExternalWorkflowExecutionCommandAttributes"
    ]
)
_SIGNALEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "SignalExternalWorkflowExecutionCommandAttributes"
]
_UPSERTWORKFLOWSEARCHATTRIBUTESCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "UpsertWorkflowSearchAttributesCommandAttributes"
]
_MODIFYWORKFLOWPROPERTIESCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "ModifyWorkflowPropertiesCommandAttributes"
]
_RECORDMARKERCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "RecordMarkerCommandAttributes"
]
_RECORDMARKERCOMMANDATTRIBUTES_DETAILSENTRY = (
    _RECORDMARKERCOMMANDATTRIBUTES.nested_types_by_name["DetailsEntry"]
)
_CONTINUEASNEWWORKFLOWEXECUTIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "ContinueAsNewWorkflowExecutionCommandAttributes"
]
_STARTCHILDWORKFLOWEXECUTIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "StartChildWorkflowExecutionCommandAttributes"
]
_PROTOCOLMESSAGECOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "ProtocolMessageCommandAttributes"
]
_SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "ScheduleNexusOperationCommandAttributes"
]
_SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES_NEXUSHEADERENTRY = (
    _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES.nested_types_by_name["NexusHeaderEntry"]
)
_REQUESTCANCELNEXUSOPERATIONCOMMANDATTRIBUTES = DESCRIPTOR.message_types_by_name[
    "RequestCancelNexusOperationCommandAttributes"
]
_COMMAND = DESCRIPTOR.message_types_by_name["Command"]
ScheduleActivityTaskCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "ScheduleActivityTaskCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _SCHEDULEACTIVITYTASKCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.ScheduleActivityTaskCommandAttributes)
    },
)
_sym_db.RegisterMessage(ScheduleActivityTaskCommandAttributes)

RequestCancelActivityTaskCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "RequestCancelActivityTaskCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _REQUESTCANCELACTIVITYTASKCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.RequestCancelActivityTaskCommandAttributes)
    },
)
_sym_db.RegisterMessage(RequestCancelActivityTaskCommandAttributes)

StartTimerCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "StartTimerCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _STARTTIMERCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.StartTimerCommandAttributes)
    },
)
_sym_db.RegisterMessage(StartTimerCommandAttributes)

CompleteWorkflowExecutionCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "CompleteWorkflowExecutionCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _COMPLETEWORKFLOWEXECUTIONCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.CompleteWorkflowExecutionCommandAttributes)
    },
)
_sym_db.RegisterMessage(CompleteWorkflowExecutionCommandAttributes)

FailWorkflowExecutionCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "FailWorkflowExecutionCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _FAILWORKFLOWEXECUTIONCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.FailWorkflowExecutionCommandAttributes)
    },
)
_sym_db.RegisterMessage(FailWorkflowExecutionCommandAttributes)

CancelTimerCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "CancelTimerCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _CANCELTIMERCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.CancelTimerCommandAttributes)
    },
)
_sym_db.RegisterMessage(CancelTimerCommandAttributes)

CancelWorkflowExecutionCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "CancelWorkflowExecutionCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _CANCELWORKFLOWEXECUTIONCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.CancelWorkflowExecutionCommandAttributes)
    },
)
_sym_db.RegisterMessage(CancelWorkflowExecutionCommandAttributes)

RequestCancelExternalWorkflowExecutionCommandAttributes = (
    _reflection.GeneratedProtocolMessageType(
        "RequestCancelExternalWorkflowExecutionCommandAttributes",
        (_message.Message,),
        {
            "DESCRIPTOR": _REQUESTCANCELEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES,
            "__module__": "temporal.api.command.v1.message_pb2",
            # @@protoc_insertion_point(class_scope:temporal.api.command.v1.RequestCancelExternalWorkflowExecutionCommandAttributes)
        },
    )
)
_sym_db.RegisterMessage(RequestCancelExternalWorkflowExecutionCommandAttributes)

SignalExternalWorkflowExecutionCommandAttributes = (
    _reflection.GeneratedProtocolMessageType(
        "SignalExternalWorkflowExecutionCommandAttributes",
        (_message.Message,),
        {
            "DESCRIPTOR": _SIGNALEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES,
            "__module__": "temporal.api.command.v1.message_pb2",
            # @@protoc_insertion_point(class_scope:temporal.api.command.v1.SignalExternalWorkflowExecutionCommandAttributes)
        },
    )
)
_sym_db.RegisterMessage(SignalExternalWorkflowExecutionCommandAttributes)

UpsertWorkflowSearchAttributesCommandAttributes = (
    _reflection.GeneratedProtocolMessageType(
        "UpsertWorkflowSearchAttributesCommandAttributes",
        (_message.Message,),
        {
            "DESCRIPTOR": _UPSERTWORKFLOWSEARCHATTRIBUTESCOMMANDATTRIBUTES,
            "__module__": "temporal.api.command.v1.message_pb2",
            # @@protoc_insertion_point(class_scope:temporal.api.command.v1.UpsertWorkflowSearchAttributesCommandAttributes)
        },
    )
)
_sym_db.RegisterMessage(UpsertWorkflowSearchAttributesCommandAttributes)

ModifyWorkflowPropertiesCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "ModifyWorkflowPropertiesCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _MODIFYWORKFLOWPROPERTIESCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.ModifyWorkflowPropertiesCommandAttributes)
    },
)
_sym_db.RegisterMessage(ModifyWorkflowPropertiesCommandAttributes)

RecordMarkerCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "RecordMarkerCommandAttributes",
    (_message.Message,),
    {
        "DetailsEntry": _reflection.GeneratedProtocolMessageType(
            "DetailsEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _RECORDMARKERCOMMANDATTRIBUTES_DETAILSENTRY,
                "__module__": "temporal.api.command.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.command.v1.RecordMarkerCommandAttributes.DetailsEntry)
            },
        ),
        "DESCRIPTOR": _RECORDMARKERCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.RecordMarkerCommandAttributes)
    },
)
_sym_db.RegisterMessage(RecordMarkerCommandAttributes)
_sym_db.RegisterMessage(RecordMarkerCommandAttributes.DetailsEntry)

ContinueAsNewWorkflowExecutionCommandAttributes = (
    _reflection.GeneratedProtocolMessageType(
        "ContinueAsNewWorkflowExecutionCommandAttributes",
        (_message.Message,),
        {
            "DESCRIPTOR": _CONTINUEASNEWWORKFLOWEXECUTIONCOMMANDATTRIBUTES,
            "__module__": "temporal.api.command.v1.message_pb2",
            # @@protoc_insertion_point(class_scope:temporal.api.command.v1.ContinueAsNewWorkflowExecutionCommandAttributes)
        },
    )
)
_sym_db.RegisterMessage(ContinueAsNewWorkflowExecutionCommandAttributes)

StartChildWorkflowExecutionCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "StartChildWorkflowExecutionCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _STARTCHILDWORKFLOWEXECUTIONCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.StartChildWorkflowExecutionCommandAttributes)
    },
)
_sym_db.RegisterMessage(StartChildWorkflowExecutionCommandAttributes)

ProtocolMessageCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "ProtocolMessageCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _PROTOCOLMESSAGECOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.ProtocolMessageCommandAttributes)
    },
)
_sym_db.RegisterMessage(ProtocolMessageCommandAttributes)

ScheduleNexusOperationCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "ScheduleNexusOperationCommandAttributes",
    (_message.Message,),
    {
        "NexusHeaderEntry": _reflection.GeneratedProtocolMessageType(
            "NexusHeaderEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES_NEXUSHEADERENTRY,
                "__module__": "temporal.api.command.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.command.v1.ScheduleNexusOperationCommandAttributes.NexusHeaderEntry)
            },
        ),
        "DESCRIPTOR": _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.ScheduleNexusOperationCommandAttributes)
    },
)
_sym_db.RegisterMessage(ScheduleNexusOperationCommandAttributes)
_sym_db.RegisterMessage(ScheduleNexusOperationCommandAttributes.NexusHeaderEntry)

RequestCancelNexusOperationCommandAttributes = _reflection.GeneratedProtocolMessageType(
    "RequestCancelNexusOperationCommandAttributes",
    (_message.Message,),
    {
        "DESCRIPTOR": _REQUESTCANCELNEXUSOPERATIONCOMMANDATTRIBUTES,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.RequestCancelNexusOperationCommandAttributes)
    },
)
_sym_db.RegisterMessage(RequestCancelNexusOperationCommandAttributes)

Command = _reflection.GeneratedProtocolMessageType(
    "Command",
    (_message.Message,),
    {
        "DESCRIPTOR": _COMMAND,
        "__module__": "temporal.api.command.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.command.v1.Command)
    },
)
_sym_db.RegisterMessage(Command)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\032io.temporal.api.command.v1B\014MessageProtoP\001Z%go.temporal.io/api/command/v1;command\252\002\031Temporalio.Api.Command.V1\352\002\034Temporalio::Api::Command::V1"
    _REQUESTCANCELEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "control"
    ]._options = None
    _REQUESTCANCELEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "control"
    ]._serialized_options = b"\030\001"
    _SIGNALEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "control"
    ]._options = None
    _SIGNALEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "control"
    ]._serialized_options = b"\030\001"
    _RECORDMARKERCOMMANDATTRIBUTES_DETAILSENTRY._options = None
    _RECORDMARKERCOMMANDATTRIBUTES_DETAILSENTRY._serialized_options = b"8\001"
    _CONTINUEASNEWWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "inherit_build_id"
    ]._options = None
    _CONTINUEASNEWWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "inherit_build_id"
    ]._serialized_options = b"\030\001"
    _STARTCHILDWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "inherit_build_id"
    ]._options = None
    _STARTCHILDWORKFLOWEXECUTIONCOMMANDATTRIBUTES.fields_by_name[
        "inherit_build_id"
    ]._serialized_options = b"\030\001"
    _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES_NEXUSHEADERENTRY._options = None
    _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES_NEXUSHEADERENTRY._serialized_options = (
        b"8\001"
    )
    _SCHEDULEACTIVITYTASKCOMMANDATTRIBUTES._serialized_start = 338
    _SCHEDULEACTIVITYTASKCOMMANDATTRIBUTES._serialized_end = 1032
    _REQUESTCANCELACTIVITYTASKCOMMANDATTRIBUTES._serialized_start = 1034
    _REQUESTCANCELACTIVITYTASKCOMMANDATTRIBUTES._serialized_end = 1106
    _STARTTIMERCOMMANDATTRIBUTES._serialized_start = 1108
    _STARTTIMERCOMMANDATTRIBUTES._serialized_end = 1213
    _COMPLETEWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_start = 1215
    _COMPLETEWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_end = 1309
    _FAILWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_start = 1311
    _FAILWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_end = 1402
    _CANCELTIMERCOMMANDATTRIBUTES._serialized_start = 1404
    _CANCELTIMERCOMMANDATTRIBUTES._serialized_end = 1452
    _CANCELWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_start = 1454
    _CANCELWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_end = 1547
    _REQUESTCANCELEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_start = 1550
    _REQUESTCANCELEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_end = 1729
    _SIGNALEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_start = 1732
    _SIGNALEXTERNALWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_end = 2031
    _UPSERTWORKFLOWSEARCHATTRIBUTESCOMMANDATTRIBUTES._serialized_start = 2033
    _UPSERTWORKFLOWSEARCHATTRIBUTESCOMMANDATTRIBUTES._serialized_end = 2151
    _MODIFYWORKFLOWPROPERTIESCOMMANDATTRIBUTES._serialized_start = 2153
    _MODIFYWORKFLOWPROPERTIESCOMMANDATTRIBUTES._serialized_end = 2249
    _RECORDMARKERCOMMANDATTRIBUTES._serialized_start = 2252
    _RECORDMARKERCOMMANDATTRIBUTES._serialized_end = 2571
    _RECORDMARKERCOMMANDATTRIBUTES_DETAILSENTRY._serialized_start = 2491
    _RECORDMARKERCOMMANDATTRIBUTES_DETAILSENTRY._serialized_end = 2571
    _CONTINUEASNEWWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_start = 2574
    _CONTINUEASNEWWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_end = 3421
    _STARTCHILDWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_start = 3424
    _STARTCHILDWORKFLOWEXECUTIONCOMMANDATTRIBUTES._serialized_end = 4349
    _PROTOCOLMESSAGECOMMANDATTRIBUTES._serialized_start = 4351
    _PROTOCOLMESSAGECOMMANDATTRIBUTES._serialized_end = 4405
    _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES._serialized_start = 4408
    _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES._serialized_end = 4770
    _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES_NEXUSHEADERENTRY._serialized_start = 4720
    _SCHEDULENEXUSOPERATIONCOMMANDATTRIBUTES_NEXUSHEADERENTRY._serialized_end = 4770
    _REQUESTCANCELNEXUSOPERATIONCOMMANDATTRIBUTES._serialized_start = 4772
    _REQUESTCANCELNEXUSOPERATIONCOMMANDATTRIBUTES._serialized_end = 4846
    _COMMAND._serialized_start = 4849
    _COMMAND._serialized_end = 7091
# @@protoc_insertion_point(module_scope)
