"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
The MIT License

Copyright (c) 2022 Temporal Technologies Inc.  All rights reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
"""

import builtins
import collections.abc
import sys

import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import google.protobuf.timestamp_pb2

import temporalio.api.enums.v1.cluster_pb2
import temporalio.api.enums.v1.common_pb2
import temporalio.api.version.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class ClusterMetadata(google.protobuf.message.Message):
    """data column"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class IndexSearchAttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___IndexSearchAttributes: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___IndexSearchAttributes | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["value", b"value"]
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    CLUSTER_FIELD_NUMBER: builtins.int
    HISTORY_SHARD_COUNT_FIELD_NUMBER: builtins.int
    CLUSTER_ID_FIELD_NUMBER: builtins.int
    VERSION_INFO_FIELD_NUMBER: builtins.int
    INDEX_SEARCH_ATTRIBUTES_FIELD_NUMBER: builtins.int
    CLUSTER_ADDRESS_FIELD_NUMBER: builtins.int
    FAILOVER_VERSION_INCREMENT_FIELD_NUMBER: builtins.int
    INITIAL_FAILOVER_VERSION_FIELD_NUMBER: builtins.int
    IS_GLOBAL_NAMESPACE_ENABLED_FIELD_NUMBER: builtins.int
    IS_CONNECTION_ENABLED_FIELD_NUMBER: builtins.int
    cluster: builtins.str
    history_shard_count: builtins.int
    cluster_id: builtins.str
    @property
    def version_info(self) -> temporalio.api.version.v1.message_pb2.VersionInfo: ...
    @property
    def index_search_attributes(
        self,
    ) -> google.protobuf.internal.containers.MessageMap[
        builtins.str, global___IndexSearchAttributes
    ]: ...
    cluster_address: builtins.str
    failover_version_increment: builtins.int
    initial_failover_version: builtins.int
    is_global_namespace_enabled: builtins.bool
    is_connection_enabled: builtins.bool
    def __init__(
        self,
        *,
        cluster: builtins.str = ...,
        history_shard_count: builtins.int = ...,
        cluster_id: builtins.str = ...,
        version_info: temporalio.api.version.v1.message_pb2.VersionInfo | None = ...,
        index_search_attributes: collections.abc.Mapping[
            builtins.str, global___IndexSearchAttributes
        ]
        | None = ...,
        cluster_address: builtins.str = ...,
        failover_version_increment: builtins.int = ...,
        initial_failover_version: builtins.int = ...,
        is_global_namespace_enabled: builtins.bool = ...,
        is_connection_enabled: builtins.bool = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["version_info", b"version_info"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "cluster",
            b"cluster",
            "cluster_address",
            b"cluster_address",
            "cluster_id",
            b"cluster_id",
            "failover_version_increment",
            b"failover_version_increment",
            "history_shard_count",
            b"history_shard_count",
            "index_search_attributes",
            b"index_search_attributes",
            "initial_failover_version",
            b"initial_failover_version",
            "is_connection_enabled",
            b"is_connection_enabled",
            "is_global_namespace_enabled",
            b"is_global_namespace_enabled",
            "version_info",
            b"version_info",
        ],
    ) -> None: ...

global___ClusterMetadata = ClusterMetadata

class IndexSearchAttributes(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class CustomSearchAttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: temporalio.api.enums.v1.common_pb2.IndexedValueType.ValueType
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: temporalio.api.enums.v1.common_pb2.IndexedValueType.ValueType = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    CUSTOM_SEARCH_ATTRIBUTES_FIELD_NUMBER: builtins.int
    @property
    def custom_search_attributes(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[
        builtins.str, temporalio.api.enums.v1.common_pb2.IndexedValueType.ValueType
    ]: ...
    def __init__(
        self,
        *,
        custom_search_attributes: collections.abc.Mapping[
            builtins.str, temporalio.api.enums.v1.common_pb2.IndexedValueType.ValueType
        ]
        | None = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "custom_search_attributes", b"custom_search_attributes"
        ],
    ) -> None: ...

global___IndexSearchAttributes = IndexSearchAttributes

class HostInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    identity: builtins.str
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["identity", b"identity"]
    ) -> None: ...

global___HostInfo = HostInfo

class RingInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROLE_FIELD_NUMBER: builtins.int
    MEMBER_COUNT_FIELD_NUMBER: builtins.int
    MEMBERS_FIELD_NUMBER: builtins.int
    role: builtins.str
    member_count: builtins.int
    @property
    def members(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___HostInfo
    ]: ...
    def __init__(
        self,
        *,
        role: builtins.str = ...,
        member_count: builtins.int = ...,
        members: collections.abc.Iterable[global___HostInfo] | None = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "member_count", b"member_count", "members", b"members", "role", b"role"
        ],
    ) -> None: ...

global___RingInfo = RingInfo

class MembershipInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CURRENT_HOST_FIELD_NUMBER: builtins.int
    REACHABLE_MEMBERS_FIELD_NUMBER: builtins.int
    RINGS_FIELD_NUMBER: builtins.int
    @property
    def current_host(self) -> global___HostInfo: ...
    @property
    def reachable_members(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[
        builtins.str
    ]: ...
    @property
    def rings(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___RingInfo
    ]: ...
    def __init__(
        self,
        *,
        current_host: global___HostInfo | None = ...,
        reachable_members: collections.abc.Iterable[builtins.str] | None = ...,
        rings: collections.abc.Iterable[global___RingInfo] | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["current_host", b"current_host"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "current_host",
            b"current_host",
            "reachable_members",
            b"reachable_members",
            "rings",
            b"rings",
        ],
    ) -> None: ...

global___MembershipInfo = MembershipInfo

class ClusterMember(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROLE_FIELD_NUMBER: builtins.int
    HOST_ID_FIELD_NUMBER: builtins.int
    RPC_ADDRESS_FIELD_NUMBER: builtins.int
    RPC_PORT_FIELD_NUMBER: builtins.int
    SESSION_START_TIME_FIELD_NUMBER: builtins.int
    LAST_HEARTBIT_TIME_FIELD_NUMBER: builtins.int
    RECORD_EXPIRY_TIME_FIELD_NUMBER: builtins.int
    role: temporalio.api.enums.v1.cluster_pb2.ClusterMemberRole.ValueType
    host_id: builtins.str
    rpc_address: builtins.str
    rpc_port: builtins.int
    @property
    def session_start_time(self) -> google.protobuf.timestamp_pb2.Timestamp: ...
    @property
    def last_heartbit_time(self) -> google.protobuf.timestamp_pb2.Timestamp: ...
    @property
    def record_expiry_time(self) -> google.protobuf.timestamp_pb2.Timestamp: ...
    def __init__(
        self,
        *,
        role: temporalio.api.enums.v1.cluster_pb2.ClusterMemberRole.ValueType = ...,
        host_id: builtins.str = ...,
        rpc_address: builtins.str = ...,
        rpc_port: builtins.int = ...,
        session_start_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_heartbit_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        record_expiry_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "last_heartbit_time",
            b"last_heartbit_time",
            "record_expiry_time",
            b"record_expiry_time",
            "session_start_time",
            b"session_start_time",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "host_id",
            b"host_id",
            "last_heartbit_time",
            b"last_heartbit_time",
            "record_expiry_time",
            b"record_expiry_time",
            "role",
            b"role",
            "rpc_address",
            b"rpc_address",
            "rpc_port",
            b"rpc_port",
            "session_start_time",
            b"session_start_time",
        ],
    ) -> None: ...

global___ClusterMember = ClusterMember
