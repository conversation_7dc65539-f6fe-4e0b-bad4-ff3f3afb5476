"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys

import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.message

import temporalio.api.common.v1.message_pb2
import temporalio.api.taskqueue.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class ActivityOptions(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TASK_QUEUE_FIELD_NUMBER: builtins.int
    SCHEDULE_TO_CLOSE_TIMEOUT_FIELD_NUMBER: builtins.int
    SCHEDULE_TO_START_TIMEOUT_FIELD_NUMBER: builtins.int
    START_TO_CLOSE_TIMEOUT_FIELD_NUMBER: builtins.int
    HEARTBEAT_TIMEOUT_FIELD_NUMBER: builtins.int
    RETRY_POLICY_FIELD_NUMBER: builtins.int
    @property
    def task_queue(self) -> temporalio.api.taskqueue.v1.message_pb2.TaskQueue: ...
    @property
    def schedule_to_close_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Indicates how long the caller is willing to wait for an activity completion. Limits how long
        retries will be attempted. Either this or `start_to_close_timeout` must be specified.

        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    @property
    def schedule_to_start_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Limits time an activity task can stay in a task queue before a worker picks it up. This
        timeout is always non retryable, as all a retry would achieve is to put it back into the same
        queue. Defaults to `schedule_to_close_timeout` or workflow execution timeout if not
        specified.

        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    @property
    def start_to_close_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Maximum time an activity is allowed to execute after being picked up by a worker. This
        timeout is always retryable. Either this or `schedule_to_close_timeout` must be
        specified.

        (-- api-linter: core::0140::prepositions=disabled
            aip.dev/not-precedent: "to" is used to indicate interval. --)
        """
    @property
    def heartbeat_timeout(self) -> google.protobuf.duration_pb2.Duration:
        """Maximum permitted time between successful worker heartbeats."""
    @property
    def retry_policy(self) -> temporalio.api.common.v1.message_pb2.RetryPolicy: ...
    def __init__(
        self,
        *,
        task_queue: temporalio.api.taskqueue.v1.message_pb2.TaskQueue | None = ...,
        schedule_to_close_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        schedule_to_start_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        start_to_close_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        heartbeat_timeout: google.protobuf.duration_pb2.Duration | None = ...,
        retry_policy: temporalio.api.common.v1.message_pb2.RetryPolicy | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "heartbeat_timeout",
            b"heartbeat_timeout",
            "retry_policy",
            b"retry_policy",
            "schedule_to_close_timeout",
            b"schedule_to_close_timeout",
            "schedule_to_start_timeout",
            b"schedule_to_start_timeout",
            "start_to_close_timeout",
            b"start_to_close_timeout",
            "task_queue",
            b"task_queue",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "heartbeat_timeout",
            b"heartbeat_timeout",
            "retry_policy",
            b"retry_policy",
            "schedule_to_close_timeout",
            b"schedule_to_close_timeout",
            "schedule_to_start_timeout",
            b"schedule_to_start_timeout",
            "start_to_close_timeout",
            b"start_to_close_timeout",
            "task_queue",
            b"task_queue",
        ],
    ) -> None: ...

global___ActivityOptions = ActivityOptions
