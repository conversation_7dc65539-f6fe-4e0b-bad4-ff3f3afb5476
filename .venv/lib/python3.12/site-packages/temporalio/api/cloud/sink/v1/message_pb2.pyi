"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys

import google.protobuf.descriptor
import google.protobuf.message

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class S3Spec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ROLE_NAME_FIELD_NUMBER: builtins.int
    BUCKET_NAME_FIELD_NUMBER: builtins.int
    REGION_FIELD_NUMBER: builtins.int
    KMS_ARN_FIELD_NUMBER: builtins.int
    AWS_ACCOUNT_ID_FIELD_NUMBER: builtins.int
    role_name: builtins.str
    """The IAM role that Temporal Cloud assumes for writing records to the customer's S3 bucket."""
    bucket_name: builtins.str
    """The name of the destination S3 bucket where Tempo<PERSON> will send data."""
    region: builtins.str
    """The region where the S3 bucket is located."""
    kms_arn: builtins.str
    """The AWS Key Management Service (KMS) ARN used for encryption."""
    aws_account_id: builtins.str
    """The AWS account ID associated with the S3 bucket and the assumed role."""
    def __init__(
        self,
        *,
        role_name: builtins.str = ...,
        bucket_name: builtins.str = ...,
        region: builtins.str = ...,
        kms_arn: builtins.str = ...,
        aws_account_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "aws_account_id",
            b"aws_account_id",
            "bucket_name",
            b"bucket_name",
            "kms_arn",
            b"kms_arn",
            "region",
            b"region",
            "role_name",
            b"role_name",
        ],
    ) -> None: ...

global___S3Spec = S3Spec

class GCSSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SA_ID_FIELD_NUMBER: builtins.int
    BUCKET_NAME_FIELD_NUMBER: builtins.int
    GCP_PROJECT_ID_FIELD_NUMBER: builtins.int
    REGION_FIELD_NUMBER: builtins.int
    sa_id: builtins.str
    """The customer service account ID that Temporal Cloud impersonates for writing records to the customer's GCS bucket."""
    bucket_name: builtins.str
    """The name of the destination GCS bucket where Temporal will send data."""
    gcp_project_id: builtins.str
    """The GCP project ID associated with the GCS bucket and service account."""
    region: builtins.str
    """The region of the gcs bucket"""
    def __init__(
        self,
        *,
        sa_id: builtins.str = ...,
        bucket_name: builtins.str = ...,
        gcp_project_id: builtins.str = ...,
        region: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "bucket_name",
            b"bucket_name",
            "gcp_project_id",
            b"gcp_project_id",
            "region",
            b"region",
            "sa_id",
            b"sa_id",
        ],
    ) -> None: ...

global___GCSSpec = GCSSpec
