# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/cloud/sink/v1/message.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n(temporal/api/cloud/sink/v1/message.proto\x12\x1atemporal.api.cloud.sink.v1"i\n\x06S3Spec\x12\x11\n\trole_name\x18\x01 \x01(\t\x12\x13\n\x0b\x62ucket_name\x18\x02 \x01(\t\x12\x0e\n\x06region\x18\x03 \x01(\t\x12\x0f\n\x07kms_arn\x18\x04 \x01(\t\x12\x16\n\x0e\x61ws_account_id\x18\x05 \x01(\t"U\n\x07GCSSpec\x12\r\n\x05sa_id\x18\x01 \x01(\t\x12\x13\n\x0b\x62ucket_name\x18\x02 \x01(\t\x12\x16\n\x0egcp_project_id\x18\x03 \x01(\t\x12\x0e\n\x06region\x18\x04 \x01(\tB\x98\x01\n\x1dio.temporal.api.cloud.sink.v1B\x0cMessageProtoP\x01Z%go.temporal.io/api/cloud/sink/v1;sink\xaa\x02\x1cTemporalio.Api.Cloud.Sink.V1\xea\x02 Temporalio::Api::Cloud::Sink::V1b\x06proto3'
)


_S3SPEC = DESCRIPTOR.message_types_by_name["S3Spec"]
_GCSSPEC = DESCRIPTOR.message_types_by_name["GCSSpec"]
S3Spec = _reflection.GeneratedProtocolMessageType(
    "S3Spec",
    (_message.Message,),
    {
        "DESCRIPTOR": _S3SPEC,
        "__module__": "temporal.api.cloud.sink.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.sink.v1.S3Spec)
    },
)
_sym_db.RegisterMessage(S3Spec)

GCSSpec = _reflection.GeneratedProtocolMessageType(
    "GCSSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _GCSSPEC,
        "__module__": "temporal.api.cloud.sink.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.sink.v1.GCSSpec)
    },
)
_sym_db.RegisterMessage(GCSSpec)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\035io.temporal.api.cloud.sink.v1B\014MessageProtoP\001Z%go.temporal.io/api/cloud/sink/v1;sink\252\002\034Temporalio.Api.Cloud.Sink.V1\352\002 Temporalio::Api::Cloud::Sink::V1"
    _S3SPEC._serialized_start = 72
    _S3SPEC._serialized_end = 177
    _GCSSPEC._serialized_start = 179
    _GCSSPEC._serialized_end = 264
# @@protoc_insertion_point(module_scope)
