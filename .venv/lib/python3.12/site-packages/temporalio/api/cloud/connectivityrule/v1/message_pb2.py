# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/cloud/connectivityrule/v1/message.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from temporalio.api.cloud.resource.v1 import (
    message_pb2 as temporal_dot_api_dot_cloud_dot_resource_dot_v1_dot_message__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n4temporal/api/cloud/connectivityrule/v1/message.proto\x12&temporal.api.cloud.connectivityrule.v1\x1a,temporal/api/cloud/resource/v1/message.proto\x1a\x1fgoogle/protobuf/timestamp.proto"\x96\x02\n\x10\x43onnectivityRule\x12\n\n\x02id\x18\x01 \x01(\t\x12J\n\x04spec\x18\x02 \x01(\x0b\x32<.temporal.api.cloud.connectivityrule.v1.ConnectivityRuleSpec\x12\x18\n\x10resource_version\x18\x04 \x01(\t\x12<\n\x05state\x18\x05 \x01(\x0e\x32-.temporal.api.cloud.resource.v1.ResourceState\x12\x1a\n\x12\x61sync_operation_id\x18\x06 \x01(\t\x12\x30\n\x0c\x63reated_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampJ\x04\x08\x03\x10\x04"\xd9\x01\n\x14\x43onnectivityRuleSpec\x12U\n\x0bpublic_rule\x18\x01 \x01(\x0b\x32>.temporal.api.cloud.connectivityrule.v1.PublicConnectivityRuleH\x00\x12W\n\x0cprivate_rule\x18\x02 \x01(\x0b\x32?.temporal.api.cloud.connectivityrule.v1.PrivateConnectivityRuleH\x00\x42\x11\n\x0f\x63onnection_type"\x18\n\x16PublicConnectivityRule"^\n\x17PrivateConnectivityRule\x12\x15\n\rconnection_id\x18\x01 \x01(\t\x12\x16\n\x0egcp_project_id\x18\x02 \x01(\t\x12\x0e\n\x06region\x18\x03 \x01(\tJ\x04\x08\x04\x10\x05\x42\xd4\x01\n)io.temporal.api.cloud.connectivityrule.v1B\x0cMessageProtoP\x01Z=go.temporal.io/api/cloud/connectivityrule/v1;connectivityrule\xaa\x02(Temporalio.Api.Cloud.ConnectivityRule.V1\xea\x02,Temporalio::Api::Cloud::ConnectivityRule::V1b\x06proto3'
)


_CONNECTIVITYRULE = DESCRIPTOR.message_types_by_name["ConnectivityRule"]
_CONNECTIVITYRULESPEC = DESCRIPTOR.message_types_by_name["ConnectivityRuleSpec"]
_PUBLICCONNECTIVITYRULE = DESCRIPTOR.message_types_by_name["PublicConnectivityRule"]
_PRIVATECONNECTIVITYRULE = DESCRIPTOR.message_types_by_name["PrivateConnectivityRule"]
ConnectivityRule = _reflection.GeneratedProtocolMessageType(
    "ConnectivityRule",
    (_message.Message,),
    {
        "DESCRIPTOR": _CONNECTIVITYRULE,
        "__module__": "temporal.api.cloud.connectivityrule.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.connectivityrule.v1.ConnectivityRule)
    },
)
_sym_db.RegisterMessage(ConnectivityRule)

ConnectivityRuleSpec = _reflection.GeneratedProtocolMessageType(
    "ConnectivityRuleSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _CONNECTIVITYRULESPEC,
        "__module__": "temporal.api.cloud.connectivityrule.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.connectivityrule.v1.ConnectivityRuleSpec)
    },
)
_sym_db.RegisterMessage(ConnectivityRuleSpec)

PublicConnectivityRule = _reflection.GeneratedProtocolMessageType(
    "PublicConnectivityRule",
    (_message.Message,),
    {
        "DESCRIPTOR": _PUBLICCONNECTIVITYRULE,
        "__module__": "temporal.api.cloud.connectivityrule.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.connectivityrule.v1.PublicConnectivityRule)
    },
)
_sym_db.RegisterMessage(PublicConnectivityRule)

PrivateConnectivityRule = _reflection.GeneratedProtocolMessageType(
    "PrivateConnectivityRule",
    (_message.Message,),
    {
        "DESCRIPTOR": _PRIVATECONNECTIVITYRULE,
        "__module__": "temporal.api.cloud.connectivityrule.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.connectivityrule.v1.PrivateConnectivityRule)
    },
)
_sym_db.RegisterMessage(PrivateConnectivityRule)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n)io.temporal.api.cloud.connectivityrule.v1B\014MessageProtoP\001Z=go.temporal.io/api/cloud/connectivityrule/v1;connectivityrule\252\002(Temporalio.Api.Cloud.ConnectivityRule.V1\352\002,Temporalio::Api::Cloud::ConnectivityRule::V1"
    _CONNECTIVITYRULE._serialized_start = 176
    _CONNECTIVITYRULE._serialized_end = 454
    _CONNECTIVITYRULESPEC._serialized_start = 457
    _CONNECTIVITYRULESPEC._serialized_end = 674
    _PUBLICCONNECTIVITYRULE._serialized_start = 676
    _PUBLICCONNECTIVITYRULE._serialized_end = 700
    _PRIVATECONNECTIVITYRULE._serialized_start = 702
    _PRIVATECONNECTIVITYRULE._serialized_end = 796
# @@protoc_insertion_point(module_scope)
