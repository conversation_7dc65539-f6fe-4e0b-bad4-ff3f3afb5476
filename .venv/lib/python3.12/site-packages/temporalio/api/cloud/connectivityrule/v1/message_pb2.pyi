"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys

import google.protobuf.descriptor
import google.protobuf.message
import google.protobuf.timestamp_pb2

import temporalio.api.cloud.resource.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class ConnectivityRule(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the private connectivity rule."""
    @property
    def spec(self) -> global___ConnectivityRuleSpec:
        """The connectivity rule specification."""
    resource_version: builtins.str
    """The current version of the connectivity rule specification.
    The next update operation will have to include this version.
    """
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    async_operation_id: builtins.str
    """The id of the async operation that is creating/updating/deleting the connectivity rule, if any."""
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the connectivity rule was created."""
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        spec: global___ConnectivityRuleSpec | None = ...,
        resource_version: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time", b"created_time", "spec", b"spec"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "created_time",
            b"created_time",
            "id",
            b"id",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
        ],
    ) -> None: ...

global___ConnectivityRule = ConnectivityRule

class ConnectivityRuleSpec(google.protobuf.message.Message):
    """The connectivity rule specification passed in on create/update operations."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PUBLIC_RULE_FIELD_NUMBER: builtins.int
    PRIVATE_RULE_FIELD_NUMBER: builtins.int
    @property
    def public_rule(self) -> global___PublicConnectivityRule:
        """This allows access via public internet."""
    @property
    def private_rule(self) -> global___PrivateConnectivityRule:
        """This allows access via specific private vpc."""
    def __init__(
        self,
        *,
        public_rule: global___PublicConnectivityRule | None = ...,
        private_rule: global___PrivateConnectivityRule | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "connection_type",
            b"connection_type",
            "private_rule",
            b"private_rule",
            "public_rule",
            b"public_rule",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "connection_type",
            b"connection_type",
            "private_rule",
            b"private_rule",
            "public_rule",
            b"public_rule",
        ],
    ) -> None: ...
    def WhichOneof(
        self,
        oneof_group: typing_extensions.Literal["connection_type", b"connection_type"],
    ) -> typing_extensions.Literal["public_rule", "private_rule"] | None: ...

global___ConnectivityRuleSpec = ConnectivityRuleSpec

class PublicConnectivityRule(google.protobuf.message.Message):
    """A public connectivity rule allows access to the namespace via the public internet."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___PublicConnectivityRule = PublicConnectivityRule

class PrivateConnectivityRule(google.protobuf.message.Message):
    """A private connectivity rule allows connections from a specific private vpc only."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONNECTION_ID_FIELD_NUMBER: builtins.int
    GCP_PROJECT_ID_FIELD_NUMBER: builtins.int
    REGION_FIELD_NUMBER: builtins.int
    connection_id: builtins.str
    """Connection id provided to enforce the private connectivity. This is required both by AWS and GCP."""
    gcp_project_id: builtins.str
    """For GCP private connectivity service, GCP needs both GCP project id and the Private Service Connect Connection IDs
    AWS only needs the connection_id
    """
    region: builtins.str
    """The region of the connectivity rule. This should align with the namespace.
    Example: "aws-us-west-2"
    """
    def __init__(
        self,
        *,
        connection_id: builtins.str = ...,
        gcp_project_id: builtins.str = ...,
        region: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "connection_id",
            b"connection_id",
            "gcp_project_id",
            b"gcp_project_id",
            "region",
            b"region",
        ],
    ) -> None: ...

global___PrivateConnectivityRule = PrivateConnectivityRule
