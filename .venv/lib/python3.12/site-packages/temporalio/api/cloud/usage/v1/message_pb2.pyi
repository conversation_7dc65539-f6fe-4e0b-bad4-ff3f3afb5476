"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import google.protobuf.timestamp_pb2

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _RecordType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _RecordTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_RecordType.ValueType],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RECORD_TYPE_UNSPECIFIED: _RecordType.ValueType  # 0
    RECORD_TYPE_ACTIONS: _RecordType.ValueType  # 1
    RECORD_TYPE_ACTIVE_STORAGE: _RecordType.ValueType  # 2
    RECORD_TYPE_RETAINED_STORAGE: _RecordType.ValueType  # 3

class RecordType(_RecordType, metaclass=_RecordTypeEnumTypeWrapper): ...

RECORD_TYPE_UNSPECIFIED: RecordType.ValueType  # 0
RECORD_TYPE_ACTIONS: RecordType.ValueType  # 1
RECORD_TYPE_ACTIVE_STORAGE: RecordType.ValueType  # 2
RECORD_TYPE_RETAINED_STORAGE: RecordType.ValueType  # 3
global___RecordType = RecordType

class _RecordUnit:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _RecordUnitEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_RecordUnit.ValueType],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RECORD_UNIT_UNSPECIFIED: _RecordUnit.ValueType  # 0
    RECORD_UNIT_NUMBER: _RecordUnit.ValueType  # 1
    RECORD_UNIT_BYTE_SECONDS: _RecordUnit.ValueType  # 2

class RecordUnit(_RecordUnit, metaclass=_RecordUnitEnumTypeWrapper): ...

RECORD_UNIT_UNSPECIFIED: RecordUnit.ValueType  # 0
RECORD_UNIT_NUMBER: RecordUnit.ValueType  # 1
RECORD_UNIT_BYTE_SECONDS: RecordUnit.ValueType  # 2
global___RecordUnit = RecordUnit

class _GroupByKey:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _GroupByKeyEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_GroupByKey.ValueType],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    GROUP_BY_KEY_UNSPECIFIED: _GroupByKey.ValueType  # 0
    GROUP_BY_KEY_NAMESPACE: _GroupByKey.ValueType  # 1

class GroupByKey(_GroupByKey, metaclass=_GroupByKeyEnumTypeWrapper): ...

GROUP_BY_KEY_UNSPECIFIED: GroupByKey.ValueType  # 0
GROUP_BY_KEY_NAMESPACE: GroupByKey.ValueType  # 1
global___GroupByKey = GroupByKey

class Summary(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    START_TIME_FIELD_NUMBER: builtins.int
    END_TIME_FIELD_NUMBER: builtins.int
    RECORD_GROUPS_FIELD_NUMBER: builtins.int
    INCOMPLETE_FIELD_NUMBER: builtins.int
    @property
    def start_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """Start of UTC day for now (inclusive)"""
    @property
    def end_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """End of UTC day for now (exclusive)"""
    @property
    def record_groups(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___RecordGroup
    ]:
        """Records grouped by namespace"""
    incomplete: builtins.bool
    """True if data for given time window is not fully available yet (e.g. delays)
    When true, records for the given time range could still be added/updated in the future (until false)
    """
    def __init__(
        self,
        *,
        start_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        end_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        record_groups: collections.abc.Iterable[global___RecordGroup] | None = ...,
        incomplete: builtins.bool = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "end_time", b"end_time", "start_time", b"start_time"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "end_time",
            b"end_time",
            "incomplete",
            b"incomplete",
            "record_groups",
            b"record_groups",
            "start_time",
            b"start_time",
        ],
    ) -> None: ...

global___Summary = Summary

class RecordGroup(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_BYS_FIELD_NUMBER: builtins.int
    RECORDS_FIELD_NUMBER: builtins.int
    @property
    def group_bys(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___GroupBy
    ]:
        """GroupBy keys and their values for this record group. Multiple fields are combined with logical AND."""
    @property
    def records(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___Record
    ]: ...
    def __init__(
        self,
        *,
        group_bys: collections.abc.Iterable[global___GroupBy] | None = ...,
        records: collections.abc.Iterable[global___Record] | None = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "group_bys", b"group_bys", "records", b"records"
        ],
    ) -> None: ...

global___RecordGroup = RecordGroup

class GroupBy(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    key: global___GroupByKey.ValueType
    value: builtins.str
    def __init__(
        self,
        *,
        key: global___GroupByKey.ValueType = ...,
        value: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["key", b"key", "value", b"value"]
    ) -> None: ...

global___GroupBy = GroupBy

class Record(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    UNIT_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    type: global___RecordType.ValueType
    unit: global___RecordUnit.ValueType
    value: builtins.float
    def __init__(
        self,
        *,
        type: global___RecordType.ValueType = ...,
        unit: global___RecordUnit.ValueType = ...,
        value: builtins.float = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "type", b"type", "unit", b"unit", "value", b"value"
        ],
    ) -> None: ...

global___Record = Record
