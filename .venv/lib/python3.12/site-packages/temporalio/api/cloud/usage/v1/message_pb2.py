# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/cloud/usage/v1/message.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n)temporal/api/cloud/usage/v1/message.proto\x12\x1btemporal.api.cloud.usage.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xbc\x01\n\x07Summary\x12.\n\nstart_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12?\n\rrecord_groups\x18\x03 \x03(\x0b\x32(.temporal.api.cloud.usage.v1.RecordGroup\x12\x12\n\nincomplete\x18\x04 \x01(\x08\"|\n\x0bRecordGroup\x12\x37\n\tgroup_bys\x18\x01 \x03(\x0b\x32$.temporal.api.cloud.usage.v1.GroupBy\x12\x34\n\x07records\x18\x02 \x03(\x0b\x32#.temporal.api.cloud.usage.v1.Record\"N\n\x07GroupBy\x12\x34\n\x03key\x18\x01 \x01(\x0e\x32'.temporal.api.cloud.usage.v1.GroupByKey\x12\r\n\x05value\x18\x02 \x01(\t\"\x85\x01\n\x06Record\x12\x35\n\x04type\x18\x01 \x01(\x0e\x32'.temporal.api.cloud.usage.v1.RecordType\x12\x35\n\x04unit\x18\x02 \x01(\x0e\x32'.temporal.api.cloud.usage.v1.RecordUnit\x12\r\n\x05value\x18\x03 \x01(\x01*\x84\x01\n\nRecordType\x12\x1b\n\x17RECORD_TYPE_UNSPECIFIED\x10\x00\x12\x17\n\x13RECORD_TYPE_ACTIONS\x10\x01\x12\x1e\n\x1aRECORD_TYPE_ACTIVE_STORAGE\x10\x02\x12 \n\x1cRECORD_TYPE_RETAINED_STORAGE\x10\x03*_\n\nRecordUnit\x12\x1b\n\x17RECORD_UNIT_UNSPECIFIED\x10\x00\x12\x16\n\x12RECORD_UNIT_NUMBER\x10\x01\x12\x1c\n\x18RECORD_UNIT_BYTE_SECONDS\x10\x02*F\n\nGroupByKey\x12\x1c\n\x18GROUP_BY_KEY_UNSPECIFIED\x10\x00\x12\x1a\n\x16GROUP_BY_KEY_NAMESPACE\x10\x01\x42\x9d\x01\n\x1eio.temporal.api.cloud.usage.v1B\x0cMessageProtoP\x01Z'go.temporal.io/api/cloud/usage/v1;usage\xaa\x02\x1dTemporalio.Api.Cloud.Usage.V1\xea\x02!Temporalio::Api::Cloud::Usage::V1b\x06proto3"
)

_RECORDTYPE = DESCRIPTOR.enum_types_by_name["RecordType"]
RecordType = enum_type_wrapper.EnumTypeWrapper(_RECORDTYPE)
_RECORDUNIT = DESCRIPTOR.enum_types_by_name["RecordUnit"]
RecordUnit = enum_type_wrapper.EnumTypeWrapper(_RECORDUNIT)
_GROUPBYKEY = DESCRIPTOR.enum_types_by_name["GroupByKey"]
GroupByKey = enum_type_wrapper.EnumTypeWrapper(_GROUPBYKEY)
RECORD_TYPE_UNSPECIFIED = 0
RECORD_TYPE_ACTIONS = 1
RECORD_TYPE_ACTIVE_STORAGE = 2
RECORD_TYPE_RETAINED_STORAGE = 3
RECORD_UNIT_UNSPECIFIED = 0
RECORD_UNIT_NUMBER = 1
RECORD_UNIT_BYTE_SECONDS = 2
GROUP_BY_KEY_UNSPECIFIED = 0
GROUP_BY_KEY_NAMESPACE = 1


_SUMMARY = DESCRIPTOR.message_types_by_name["Summary"]
_RECORDGROUP = DESCRIPTOR.message_types_by_name["RecordGroup"]
_GROUPBY = DESCRIPTOR.message_types_by_name["GroupBy"]
_RECORD = DESCRIPTOR.message_types_by_name["Record"]
Summary = _reflection.GeneratedProtocolMessageType(
    "Summary",
    (_message.Message,),
    {
        "DESCRIPTOR": _SUMMARY,
        "__module__": "temporal.api.cloud.usage.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.usage.v1.Summary)
    },
)
_sym_db.RegisterMessage(Summary)

RecordGroup = _reflection.GeneratedProtocolMessageType(
    "RecordGroup",
    (_message.Message,),
    {
        "DESCRIPTOR": _RECORDGROUP,
        "__module__": "temporal.api.cloud.usage.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.usage.v1.RecordGroup)
    },
)
_sym_db.RegisterMessage(RecordGroup)

GroupBy = _reflection.GeneratedProtocolMessageType(
    "GroupBy",
    (_message.Message,),
    {
        "DESCRIPTOR": _GROUPBY,
        "__module__": "temporal.api.cloud.usage.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.usage.v1.GroupBy)
    },
)
_sym_db.RegisterMessage(GroupBy)

Record = _reflection.GeneratedProtocolMessageType(
    "Record",
    (_message.Message,),
    {
        "DESCRIPTOR": _RECORD,
        "__module__": "temporal.api.cloud.usage.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.usage.v1.Record)
    },
)
_sym_db.RegisterMessage(Record)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\036io.temporal.api.cloud.usage.v1B\014MessageProtoP\001Z'go.temporal.io/api/cloud/usage/v1;usage\252\002\035Temporalio.Api.Cloud.Usage.V1\352\002!Temporalio::Api::Cloud::Usage::V1"
    _RECORDTYPE._serialized_start = 641
    _RECORDTYPE._serialized_end = 773
    _RECORDUNIT._serialized_start = 775
    _RECORDUNIT._serialized_end = 870
    _GROUPBYKEY._serialized_start = 872
    _GROUPBYKEY._serialized_end = 942
    _SUMMARY._serialized_start = 108
    _SUMMARY._serialized_end = 296
    _RECORDGROUP._serialized_start = 298
    _RECORDGROUP._serialized_end = 422
    _GROUPBY._serialized_start = 424
    _GROUPBY._serialized_end = 502
    _RECORD._serialized_start = 505
    _RECORD._serialized_end = 638
# @@protoc_insertion_point(module_scope)
