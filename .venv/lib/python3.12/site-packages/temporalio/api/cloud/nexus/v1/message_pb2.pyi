"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys

import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import google.protobuf.timestamp_pb2

import temporalio.api.cloud.resource.v1.message_pb2
import temporalio.api.common.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class EndpointSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    TARGET_SPEC_FIELD_NUMBER: builtins.int
    POLICY_SPECS_FIELD_NUMBER: builtins.int
    DESCRIPTION_DEPRECATED_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    name: builtins.str
    """The name of the endpoint. Must be unique within an account.
    The name must match `^[a-zA-Z][a-zA-Z0-9\\-]*[a-zA-Z0-9]$`.
    This field is mutable.
    """
    @property
    def target_spec(self) -> global___EndpointTargetSpec:
        """Indicates where the endpoint should forward received nexus requests to."""
    @property
    def policy_specs(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___EndpointPolicySpec
    ]:
        """The set of policies (e.g. authorization) for the endpoint. Each request's caller
        must match with at least one of the specs to be accepted by the endpoint.
        This field is mutable.
        """
    description_deprecated: builtins.str
    """Deprecated: Not supported after v0.4.0 api version. Use description instead.
    temporal:versioning:max_version=v0.4.0
    """
    @property
    def description(self) -> temporalio.api.common.v1.message_pb2.Payload:
        """The markdown description of the endpoint - optional.
        temporal:versioning:min_version=v0.4.0
        """
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        target_spec: global___EndpointTargetSpec | None = ...,
        policy_specs: collections.abc.Iterable[global___EndpointPolicySpec]
        | None = ...,
        description_deprecated: builtins.str = ...,
        description: temporalio.api.common.v1.message_pb2.Payload | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "description", b"description", "target_spec", b"target_spec"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "description",
            b"description",
            "description_deprecated",
            b"description_deprecated",
            "name",
            b"name",
            "policy_specs",
            b"policy_specs",
            "target_spec",
            b"target_spec",
        ],
    ) -> None: ...

global___EndpointSpec = EndpointSpec

class EndpointTargetSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WORKER_TARGET_SPEC_FIELD_NUMBER: builtins.int
    @property
    def worker_target_spec(self) -> global___WorkerTargetSpec:
        """A target spec for routing nexus requests to a specific cloud namespace worker."""
    def __init__(
        self,
        *,
        worker_target_spec: global___WorkerTargetSpec | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "variant", b"variant", "worker_target_spec", b"worker_target_spec"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "variant", b"variant", "worker_target_spec", b"worker_target_spec"
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["variant", b"variant"]
    ) -> typing_extensions.Literal["worker_target_spec"] | None: ...

global___EndpointTargetSpec = EndpointTargetSpec

class WorkerTargetSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_ID_FIELD_NUMBER: builtins.int
    TASK_QUEUE_FIELD_NUMBER: builtins.int
    namespace_id: builtins.str
    """The target cloud namespace to route requests to. Namespace must be in same account as the endpoint. This field is mutable."""
    task_queue: builtins.str
    """The task queue on the cloud namespace to route requests to. This field is mutable."""
    def __init__(
        self,
        *,
        namespace_id: builtins.str = ...,
        task_queue: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "namespace_id", b"namespace_id", "task_queue", b"task_queue"
        ],
    ) -> None: ...

global___WorkerTargetSpec = WorkerTargetSpec

class EndpointPolicySpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ALLOWED_CLOUD_NAMESPACE_POLICY_SPEC_FIELD_NUMBER: builtins.int
    @property
    def allowed_cloud_namespace_policy_spec(
        self,
    ) -> global___AllowedCloudNamespacePolicySpec:
        """A policy spec that allows one caller namespace to access the endpoint."""
    def __init__(
        self,
        *,
        allowed_cloud_namespace_policy_spec: global___AllowedCloudNamespacePolicySpec
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "allowed_cloud_namespace_policy_spec",
            b"allowed_cloud_namespace_policy_spec",
            "variant",
            b"variant",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "allowed_cloud_namespace_policy_spec",
            b"allowed_cloud_namespace_policy_spec",
            "variant",
            b"variant",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["variant", b"variant"]
    ) -> typing_extensions.Literal["allowed_cloud_namespace_policy_spec"] | None: ...

global___EndpointPolicySpec = EndpointPolicySpec

class AllowedCloudNamespacePolicySpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_ID_FIELD_NUMBER: builtins.int
    namespace_id: builtins.str
    """The namespace that is allowed to call into this endpoint. Calling namespace must be in same account as the endpoint."""
    def __init__(
        self,
        *,
        namespace_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["namespace_id", b"namespace_id"]
    ) -> None: ...

global___AllowedCloudNamespacePolicySpec = AllowedCloudNamespacePolicySpec

class Endpoint(google.protobuf.message.Message):
    """An endpoint that receives and then routes Nexus requests"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    LAST_MODIFIED_TIME_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the endpoint. This is generated by the server and is immutable."""
    resource_version: builtins.str
    """The current version of the endpoint specification.
    The next update operation must include this version.
    """
    @property
    def spec(self) -> global___EndpointSpec:
        """The endpoint specification."""
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the endpoint.
    For any failed state, reach out to Temporal Cloud support for remediation.
    """
    async_operation_id: builtins.str
    """The id of any ongoing async operation that is creating, updating, or deleting the endpoint, if any."""
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the endpoint was created."""
    @property
    def last_modified_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the endpoint was last modified."""
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        resource_version: builtins.str = ...,
        spec: global___EndpointSpec | None = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_modified_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time",
            b"created_time",
            "last_modified_time",
            b"last_modified_time",
            "spec",
            b"spec",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "created_time",
            b"created_time",
            "id",
            b"id",
            "last_modified_time",
            b"last_modified_time",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
        ],
    ) -> None: ...

global___Endpoint = Endpoint
