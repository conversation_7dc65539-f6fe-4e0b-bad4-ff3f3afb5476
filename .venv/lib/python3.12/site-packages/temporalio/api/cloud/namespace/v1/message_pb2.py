# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/cloud/namespace/v1/message.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2

from temporalio.api.cloud.connectivityrule.v1 import (
    message_pb2 as temporal_dot_api_dot_cloud_dot_connectivityrule_dot_v1_dot_message__pb2,
)
from temporalio.api.cloud.resource.v1 import (
    message_pb2 as temporal_dot_api_dot_cloud_dot_resource_dot_v1_dot_message__pb2,
)
from temporalio.api.cloud.sink.v1 import (
    message_pb2 as temporal_dot_api_dot_cloud_dot_sink_dot_v1_dot_message__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n-temporal/api/cloud/namespace/v1/message.proto\x12\x1ftemporal.api.cloud.namespace.v1\x1a,temporal/api/cloud/resource/v1/message.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a(temporal/api/cloud/sink/v1/message.proto\x1a\x34temporal/api/cloud/connectivityrule/v1/message.proto"\x81\x01\n\x15\x43\x65rtificateFilterSpec\x12\x13\n\x0b\x63ommon_name\x18\x01 \x01(\t\x12\x14\n\x0corganization\x18\x02 \x01(\t\x12\x1b\n\x13organizational_unit\x18\x03 \x01(\t\x12 \n\x18subject_alternative_name\x18\x04 \x01(\t"\xb7\x01\n\x0cMtlsAuthSpec\x12%\n\x1d\x61\x63\x63\x65pted_client_ca_deprecated\x18\x01 \x01(\t\x12\x1a\n\x12\x61\x63\x63\x65pted_client_ca\x18\x04 \x01(\x0c\x12S\n\x13\x63\x65rtificate_filters\x18\x02 \x03(\x0b\x32\x36.temporal.api.cloud.namespace.v1.CertificateFilterSpec\x12\x0f\n\x07\x65nabled\x18\x03 \x01(\x08"!\n\x0e\x41piKeyAuthSpec\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08"\xf4\x02\n\x0f\x43odecServerSpec\x12\x10\n\x08\x65ndpoint\x18\x01 \x01(\t\x12\x19\n\x11pass_access_token\x18\x02 \x01(\x08\x12(\n include_cross_origin_credentials\x18\x03 \x01(\x08\x12\x61\n\x14\x63ustom_error_message\x18\x04 \x01(\x0b\x32\x43.temporal.api.cloud.namespace.v1.CodecServerSpec.CustomErrorMessage\x1a\xa6\x01\n\x12\x43ustomErrorMessage\x12\x61\n\x07\x64\x65\x66\x61ult\x18\x01 \x01(\x0b\x32P.temporal.api.cloud.namespace.v1.CodecServerSpec.CustomErrorMessage.ErrorMessage\x1a-\n\x0c\x45rrorMessage\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0c\n\x04link\x18\x02 \x01(\t"1\n\rLifecycleSpec\x12 \n\x18\x65nable_delete_protection\x18\x01 \x01(\x08"8\n\x14HighAvailabilitySpec\x12 \n\x18\x64isable_managed_failover\x18\x01 \x01(\x08"\x89\t\n\rNamespaceSpec\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07regions\x18\x02 \x03(\t\x12\x16\n\x0eretention_days\x18\x03 \x01(\x05\x12@\n\tmtls_auth\x18\x04 \x01(\x0b\x32-.temporal.api.cloud.namespace.v1.MtlsAuthSpec\x12\x45\n\x0c\x61pi_key_auth\x18\x07 \x01(\x0b\x32/.temporal.api.cloud.namespace.v1.ApiKeyAuthSpec\x12p\n\x18\x63ustom_search_attributes\x18\x05 \x03(\x0b\x32J.temporal.api.cloud.namespace.v1.NamespaceSpec.CustomSearchAttributesEntryB\x02\x18\x01\x12_\n\x11search_attributes\x18\x08 \x03(\x0b\x32\x44.temporal.api.cloud.namespace.v1.NamespaceSpec.SearchAttributesEntry\x12\x46\n\x0c\x63odec_server\x18\x06 \x01(\x0b\x32\x30.temporal.api.cloud.namespace.v1.CodecServerSpec\x12\x41\n\tlifecycle\x18\t \x01(\x0b\x32..temporal.api.cloud.namespace.v1.LifecycleSpec\x12P\n\x11high_availability\x18\n \x01(\x0b\x32\x35.temporal.api.cloud.namespace.v1.HighAvailabilitySpec\x12\x1d\n\x15\x63onnectivity_rule_ids\x18\x0b \x03(\t\x1a=\n\x1b\x43ustomSearchAttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a{\n\x15SearchAttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12Q\n\x05value\x18\x02 \x01(\x0e\x32\x42.temporal.api.cloud.namespace.v1.NamespaceSpec.SearchAttributeType:\x02\x38\x01"\xac\x02\n\x13SearchAttributeType\x12%\n!SEARCH_ATTRIBUTE_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1aSEARCH_ATTRIBUTE_TYPE_TEXT\x10\x01\x12!\n\x1dSEARCH_ATTRIBUTE_TYPE_KEYWORD\x10\x02\x12\x1d\n\x19SEARCH_ATTRIBUTE_TYPE_INT\x10\x03\x12 \n\x1cSEARCH_ATTRIBUTE_TYPE_DOUBLE\x10\x04\x12\x1e\n\x1aSEARCH_ATTRIBUTE_TYPE_BOOL\x10\x05\x12"\n\x1eSEARCH_ATTRIBUTE_TYPE_DATETIME\x10\x06\x12&\n"SEARCH_ATTRIBUTE_TYPE_KEYWORD_LIST\x10\x07"Q\n\tEndpoints\x12\x13\n\x0bweb_address\x18\x01 \x01(\t\x12\x19\n\x11mtls_grpc_address\x18\x02 \x01(\t\x12\x14\n\x0cgrpc_address\x18\x03 \x01(\t"*\n\x06Limits\x12 \n\x18\x61\x63tions_per_second_limit\x18\x01 \x01(\x05"X\n\x12\x41WSPrivateLinkInfo\x12\x1e\n\x16\x61llowed_principal_arns\x18\x01 \x03(\t\x12"\n\x1avpc_endpoint_service_names\x18\x02 \x03(\t"t\n\x13PrivateConnectivity\x12\x0e\n\x06region\x18\x01 \x01(\t\x12M\n\x10\x61ws_private_link\x18\x02 \x01(\x0b\x32\x33.temporal.api.cloud.namespace.v1.AWSPrivateLinkInfo"\xc6\x07\n\tNamespace\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12\x18\n\x10resource_version\x18\x02 \x01(\t\x12<\n\x04spec\x18\x03 \x01(\x0b\x32..temporal.api.cloud.namespace.v1.NamespaceSpec\x12\x1c\n\x10state_deprecated\x18\x04 \x01(\tB\x02\x18\x01\x12<\n\x05state\x18\r \x01(\x0e\x32-.temporal.api.cloud.resource.v1.ResourceState\x12\x1a\n\x12\x61sync_operation_id\x18\x05 \x01(\t\x12=\n\tendpoints\x18\x06 \x01(\x0b\x32*.temporal.api.cloud.namespace.v1.Endpoints\x12\x15\n\ractive_region\x18\x07 \x01(\t\x12\x37\n\x06limits\x18\x08 \x01(\x0b\x32\'.temporal.api.cloud.namespace.v1.Limits\x12T\n\x16private_connectivities\x18\t \x03(\x0b\x32\x34.temporal.api.cloud.namespace.v1.PrivateConnectivity\x12\x30\n\x0c\x63reated_time\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12last_modified_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12S\n\rregion_status\x18\x0c \x03(\x0b\x32<.temporal.api.cloud.namespace.v1.Namespace.RegionStatusEntry\x12T\n\x12\x63onnectivity_rules\x18\x0e \x03(\x0b\x32\x38.temporal.api.cloud.connectivityrule.v1.ConnectivityRule\x12\x42\n\x04tags\x18\x0f \x03(\x0b\x32\x34.temporal.api.cloud.namespace.v1.Namespace.TagsEntry\x1ak\n\x11RegionStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.temporal.api.cloud.namespace.v1.NamespaceRegionStatus:\x02\x38\x01\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01"\x9b\x02\n\x15NamespaceRegionStatus\x12\x1c\n\x10state_deprecated\x18\x01 \x01(\tB\x02\x18\x01\x12K\n\x05state\x18\x03 \x01(\x0e\x32<.temporal.api.cloud.namespace.v1.NamespaceRegionStatus.State\x12\x1a\n\x12\x61sync_operation_id\x18\x02 \x01(\t"{\n\x05State\x12\x15\n\x11STATE_UNSPECIFIED\x10\x00\x12\x10\n\x0cSTATE_ADDING\x10\x01\x12\x10\n\x0cSTATE_ACTIVE\x10\x02\x12\x11\n\rSTATE_PASSIVE\x10\x03\x12\x12\n\x0eSTATE_REMOVING\x10\x04\x12\x10\n\x0cSTATE_FAILED\x10\x05"\x91\x01\n\x0e\x45xportSinkSpec\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\x12.\n\x02s3\x18\x03 \x01(\x0b\x32".temporal.api.cloud.sink.v1.S3Spec\x12\x30\n\x03gcs\x18\x04 \x01(\x0b\x32#.temporal.api.cloud.sink.v1.GCSSpec"\xf6\x03\n\nExportSink\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x18\n\x10resource_version\x18\x02 \x01(\t\x12<\n\x05state\x18\x03 \x01(\x0e\x32-.temporal.api.cloud.resource.v1.ResourceState\x12=\n\x04spec\x18\x04 \x01(\x0b\x32/.temporal.api.cloud.namespace.v1.ExportSinkSpec\x12\x42\n\x06health\x18\x05 \x01(\x0e\x32\x32.temporal.api.cloud.namespace.v1.ExportSink.Health\x12\x15\n\rerror_message\x18\x06 \x01(\t\x12;\n\x17latest_data_export_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12:\n\x16last_health_check_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp"o\n\x06Health\x12\x16\n\x12HEALTH_UNSPECIFIED\x10\x00\x12\r\n\tHEALTH_OK\x10\x01\x12\x19\n\x15HEALTH_ERROR_INTERNAL\x10\x02\x12#\n\x1fHEALTH_ERROR_USER_CONFIGURATION\x10\x03\x42\xb1\x01\n"io.temporal.api.cloud.namespace.v1B\x0cMessageProtoP\x01Z/go.temporal.io/api/cloud/namespace/v1;namespace\xaa\x02!Temporalio.Api.Cloud.Namespace.V1\xea\x02%Temporalio::Api::Cloud::Namespace::V1b\x06proto3'
)


_CERTIFICATEFILTERSPEC = DESCRIPTOR.message_types_by_name["CertificateFilterSpec"]
_MTLSAUTHSPEC = DESCRIPTOR.message_types_by_name["MtlsAuthSpec"]
_APIKEYAUTHSPEC = DESCRIPTOR.message_types_by_name["ApiKeyAuthSpec"]
_CODECSERVERSPEC = DESCRIPTOR.message_types_by_name["CodecServerSpec"]
_CODECSERVERSPEC_CUSTOMERRORMESSAGE = _CODECSERVERSPEC.nested_types_by_name[
    "CustomErrorMessage"
]
_CODECSERVERSPEC_CUSTOMERRORMESSAGE_ERRORMESSAGE = (
    _CODECSERVERSPEC_CUSTOMERRORMESSAGE.nested_types_by_name["ErrorMessage"]
)
_LIFECYCLESPEC = DESCRIPTOR.message_types_by_name["LifecycleSpec"]
_HIGHAVAILABILITYSPEC = DESCRIPTOR.message_types_by_name["HighAvailabilitySpec"]
_NAMESPACESPEC = DESCRIPTOR.message_types_by_name["NamespaceSpec"]
_NAMESPACESPEC_CUSTOMSEARCHATTRIBUTESENTRY = _NAMESPACESPEC.nested_types_by_name[
    "CustomSearchAttributesEntry"
]
_NAMESPACESPEC_SEARCHATTRIBUTESENTRY = _NAMESPACESPEC.nested_types_by_name[
    "SearchAttributesEntry"
]
_ENDPOINTS = DESCRIPTOR.message_types_by_name["Endpoints"]
_LIMITS = DESCRIPTOR.message_types_by_name["Limits"]
_AWSPRIVATELINKINFO = DESCRIPTOR.message_types_by_name["AWSPrivateLinkInfo"]
_PRIVATECONNECTIVITY = DESCRIPTOR.message_types_by_name["PrivateConnectivity"]
_NAMESPACE = DESCRIPTOR.message_types_by_name["Namespace"]
_NAMESPACE_REGIONSTATUSENTRY = _NAMESPACE.nested_types_by_name["RegionStatusEntry"]
_NAMESPACE_TAGSENTRY = _NAMESPACE.nested_types_by_name["TagsEntry"]
_NAMESPACEREGIONSTATUS = DESCRIPTOR.message_types_by_name["NamespaceRegionStatus"]
_EXPORTSINKSPEC = DESCRIPTOR.message_types_by_name["ExportSinkSpec"]
_EXPORTSINK = DESCRIPTOR.message_types_by_name["ExportSink"]
_NAMESPACESPEC_SEARCHATTRIBUTETYPE = _NAMESPACESPEC.enum_types_by_name[
    "SearchAttributeType"
]
_NAMESPACEREGIONSTATUS_STATE = _NAMESPACEREGIONSTATUS.enum_types_by_name["State"]
_EXPORTSINK_HEALTH = _EXPORTSINK.enum_types_by_name["Health"]
CertificateFilterSpec = _reflection.GeneratedProtocolMessageType(
    "CertificateFilterSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _CERTIFICATEFILTERSPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.CertificateFilterSpec)
    },
)
_sym_db.RegisterMessage(CertificateFilterSpec)

MtlsAuthSpec = _reflection.GeneratedProtocolMessageType(
    "MtlsAuthSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _MTLSAUTHSPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.MtlsAuthSpec)
    },
)
_sym_db.RegisterMessage(MtlsAuthSpec)

ApiKeyAuthSpec = _reflection.GeneratedProtocolMessageType(
    "ApiKeyAuthSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _APIKEYAUTHSPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.ApiKeyAuthSpec)
    },
)
_sym_db.RegisterMessage(ApiKeyAuthSpec)

CodecServerSpec = _reflection.GeneratedProtocolMessageType(
    "CodecServerSpec",
    (_message.Message,),
    {
        "CustomErrorMessage": _reflection.GeneratedProtocolMessageType(
            "CustomErrorMessage",
            (_message.Message,),
            {
                "ErrorMessage": _reflection.GeneratedProtocolMessageType(
                    "ErrorMessage",
                    (_message.Message,),
                    {
                        "DESCRIPTOR": _CODECSERVERSPEC_CUSTOMERRORMESSAGE_ERRORMESSAGE,
                        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
                        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.CodecServerSpec.CustomErrorMessage.ErrorMessage)
                    },
                ),
                "DESCRIPTOR": _CODECSERVERSPEC_CUSTOMERRORMESSAGE,
                "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.CodecServerSpec.CustomErrorMessage)
            },
        ),
        "DESCRIPTOR": _CODECSERVERSPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.CodecServerSpec)
    },
)
_sym_db.RegisterMessage(CodecServerSpec)
_sym_db.RegisterMessage(CodecServerSpec.CustomErrorMessage)
_sym_db.RegisterMessage(CodecServerSpec.CustomErrorMessage.ErrorMessage)

LifecycleSpec = _reflection.GeneratedProtocolMessageType(
    "LifecycleSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _LIFECYCLESPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.LifecycleSpec)
    },
)
_sym_db.RegisterMessage(LifecycleSpec)

HighAvailabilitySpec = _reflection.GeneratedProtocolMessageType(
    "HighAvailabilitySpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _HIGHAVAILABILITYSPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.HighAvailabilitySpec)
    },
)
_sym_db.RegisterMessage(HighAvailabilitySpec)

NamespaceSpec = _reflection.GeneratedProtocolMessageType(
    "NamespaceSpec",
    (_message.Message,),
    {
        "CustomSearchAttributesEntry": _reflection.GeneratedProtocolMessageType(
            "CustomSearchAttributesEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _NAMESPACESPEC_CUSTOMSEARCHATTRIBUTESENTRY,
                "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.NamespaceSpec.CustomSearchAttributesEntry)
            },
        ),
        "SearchAttributesEntry": _reflection.GeneratedProtocolMessageType(
            "SearchAttributesEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _NAMESPACESPEC_SEARCHATTRIBUTESENTRY,
                "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.NamespaceSpec.SearchAttributesEntry)
            },
        ),
        "DESCRIPTOR": _NAMESPACESPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.NamespaceSpec)
    },
)
_sym_db.RegisterMessage(NamespaceSpec)
_sym_db.RegisterMessage(NamespaceSpec.CustomSearchAttributesEntry)
_sym_db.RegisterMessage(NamespaceSpec.SearchAttributesEntry)

Endpoints = _reflection.GeneratedProtocolMessageType(
    "Endpoints",
    (_message.Message,),
    {
        "DESCRIPTOR": _ENDPOINTS,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.Endpoints)
    },
)
_sym_db.RegisterMessage(Endpoints)

Limits = _reflection.GeneratedProtocolMessageType(
    "Limits",
    (_message.Message,),
    {
        "DESCRIPTOR": _LIMITS,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.Limits)
    },
)
_sym_db.RegisterMessage(Limits)

AWSPrivateLinkInfo = _reflection.GeneratedProtocolMessageType(
    "AWSPrivateLinkInfo",
    (_message.Message,),
    {
        "DESCRIPTOR": _AWSPRIVATELINKINFO,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.AWSPrivateLinkInfo)
    },
)
_sym_db.RegisterMessage(AWSPrivateLinkInfo)

PrivateConnectivity = _reflection.GeneratedProtocolMessageType(
    "PrivateConnectivity",
    (_message.Message,),
    {
        "DESCRIPTOR": _PRIVATECONNECTIVITY,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.PrivateConnectivity)
    },
)
_sym_db.RegisterMessage(PrivateConnectivity)

Namespace = _reflection.GeneratedProtocolMessageType(
    "Namespace",
    (_message.Message,),
    {
        "RegionStatusEntry": _reflection.GeneratedProtocolMessageType(
            "RegionStatusEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _NAMESPACE_REGIONSTATUSENTRY,
                "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.Namespace.RegionStatusEntry)
            },
        ),
        "TagsEntry": _reflection.GeneratedProtocolMessageType(
            "TagsEntry",
            (_message.Message,),
            {
                "DESCRIPTOR": _NAMESPACE_TAGSENTRY,
                "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
                # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.Namespace.TagsEntry)
            },
        ),
        "DESCRIPTOR": _NAMESPACE,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.Namespace)
    },
)
_sym_db.RegisterMessage(Namespace)
_sym_db.RegisterMessage(Namespace.RegionStatusEntry)
_sym_db.RegisterMessage(Namespace.TagsEntry)

NamespaceRegionStatus = _reflection.GeneratedProtocolMessageType(
    "NamespaceRegionStatus",
    (_message.Message,),
    {
        "DESCRIPTOR": _NAMESPACEREGIONSTATUS,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.NamespaceRegionStatus)
    },
)
_sym_db.RegisterMessage(NamespaceRegionStatus)

ExportSinkSpec = _reflection.GeneratedProtocolMessageType(
    "ExportSinkSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _EXPORTSINKSPEC,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.ExportSinkSpec)
    },
)
_sym_db.RegisterMessage(ExportSinkSpec)

ExportSink = _reflection.GeneratedProtocolMessageType(
    "ExportSink",
    (_message.Message,),
    {
        "DESCRIPTOR": _EXPORTSINK,
        "__module__": "temporal.api.cloud.namespace.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.namespace.v1.ExportSink)
    },
)
_sym_db.RegisterMessage(ExportSink)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b'\n"io.temporal.api.cloud.namespace.v1B\014MessageProtoP\001Z/go.temporal.io/api/cloud/namespace/v1;namespace\252\002!Temporalio.Api.Cloud.Namespace.V1\352\002%Temporalio::Api::Cloud::Namespace::V1'
    _NAMESPACESPEC_CUSTOMSEARCHATTRIBUTESENTRY._options = None
    _NAMESPACESPEC_CUSTOMSEARCHATTRIBUTESENTRY._serialized_options = b"8\001"
    _NAMESPACESPEC_SEARCHATTRIBUTESENTRY._options = None
    _NAMESPACESPEC_SEARCHATTRIBUTESENTRY._serialized_options = b"8\001"
    _NAMESPACESPEC.fields_by_name["custom_search_attributes"]._options = None
    _NAMESPACESPEC.fields_by_name[
        "custom_search_attributes"
    ]._serialized_options = b"\030\001"
    _NAMESPACE_REGIONSTATUSENTRY._options = None
    _NAMESPACE_REGIONSTATUSENTRY._serialized_options = b"8\001"
    _NAMESPACE_TAGSENTRY._options = None
    _NAMESPACE_TAGSENTRY._serialized_options = b"8\001"
    _NAMESPACE.fields_by_name["state_deprecated"]._options = None
    _NAMESPACE.fields_by_name["state_deprecated"]._serialized_options = b"\030\001"
    _NAMESPACEREGIONSTATUS.fields_by_name["state_deprecated"]._options = None
    _NAMESPACEREGIONSTATUS.fields_by_name[
        "state_deprecated"
    ]._serialized_options = b"\030\001"
    _CERTIFICATEFILTERSPEC._serialized_start = 258
    _CERTIFICATEFILTERSPEC._serialized_end = 387
    _MTLSAUTHSPEC._serialized_start = 390
    _MTLSAUTHSPEC._serialized_end = 573
    _APIKEYAUTHSPEC._serialized_start = 575
    _APIKEYAUTHSPEC._serialized_end = 608
    _CODECSERVERSPEC._serialized_start = 611
    _CODECSERVERSPEC._serialized_end = 983
    _CODECSERVERSPEC_CUSTOMERRORMESSAGE._serialized_start = 817
    _CODECSERVERSPEC_CUSTOMERRORMESSAGE._serialized_end = 983
    _CODECSERVERSPEC_CUSTOMERRORMESSAGE_ERRORMESSAGE._serialized_start = 938
    _CODECSERVERSPEC_CUSTOMERRORMESSAGE_ERRORMESSAGE._serialized_end = 983
    _LIFECYCLESPEC._serialized_start = 985
    _LIFECYCLESPEC._serialized_end = 1034
    _HIGHAVAILABILITYSPEC._serialized_start = 1036
    _HIGHAVAILABILITYSPEC._serialized_end = 1092
    _NAMESPACESPEC._serialized_start = 1095
    _NAMESPACESPEC._serialized_end = 2256
    _NAMESPACESPEC_CUSTOMSEARCHATTRIBUTESENTRY._serialized_start = 1767
    _NAMESPACESPEC_CUSTOMSEARCHATTRIBUTESENTRY._serialized_end = 1828
    _NAMESPACESPEC_SEARCHATTRIBUTESENTRY._serialized_start = 1830
    _NAMESPACESPEC_SEARCHATTRIBUTESENTRY._serialized_end = 1953
    _NAMESPACESPEC_SEARCHATTRIBUTETYPE._serialized_start = 1956
    _NAMESPACESPEC_SEARCHATTRIBUTETYPE._serialized_end = 2256
    _ENDPOINTS._serialized_start = 2258
    _ENDPOINTS._serialized_end = 2339
    _LIMITS._serialized_start = 2341
    _LIMITS._serialized_end = 2383
    _AWSPRIVATELINKINFO._serialized_start = 2385
    _AWSPRIVATELINKINFO._serialized_end = 2473
    _PRIVATECONNECTIVITY._serialized_start = 2475
    _PRIVATECONNECTIVITY._serialized_end = 2591
    _NAMESPACE._serialized_start = 2594
    _NAMESPACE._serialized_end = 3560
    _NAMESPACE_REGIONSTATUSENTRY._serialized_start = 3408
    _NAMESPACE_REGIONSTATUSENTRY._serialized_end = 3515
    _NAMESPACE_TAGSENTRY._serialized_start = 3517
    _NAMESPACE_TAGSENTRY._serialized_end = 3560
    _NAMESPACEREGIONSTATUS._serialized_start = 3563
    _NAMESPACEREGIONSTATUS._serialized_end = 3846
    _NAMESPACEREGIONSTATUS_STATE._serialized_start = 3723
    _NAMESPACEREGIONSTATUS_STATE._serialized_end = 3846
    _EXPORTSINKSPEC._serialized_start = 3849
    _EXPORTSINKSPEC._serialized_end = 3994
    _EXPORTSINK._serialized_start = 3997
    _EXPORTSINK._serialized_end = 4499
    _EXPORTSINK_HEALTH._serialized_start = 4388
    _EXPORTSINK_HEALTH._serialized_end = 4499
# @@protoc_insertion_point(module_scope)
