"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import google.protobuf.timestamp_pb2

import temporalio.api.cloud.connectivityrule.v1.message_pb2
import temporalio.api.cloud.resource.v1.message_pb2
import temporalio.api.cloud.sink.v1.message_pb2

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class CertificateFilterSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COMMON_NAME_FIELD_NUMBER: builtins.int
    ORGANIZATION_FIELD_NUMBER: builtins.int
    ORGANIZATIONAL_UNIT_FIELD_NUMBER: builtins.int
    SUBJECT_ALTERNATIVE_NAME_FIELD_NUMBER: builtins.int
    common_name: builtins.str
    """The common_name in the certificate.
    Optional, default is empty.
    """
    organization: builtins.str
    """The organization in the certificate.
    Optional, default is empty.
    """
    organizational_unit: builtins.str
    """The organizational_unit in the certificate.
    Optional, default is empty.
    """
    subject_alternative_name: builtins.str
    """The subject_alternative_name in the certificate.
    Optional, default is empty.
    """
    def __init__(
        self,
        *,
        common_name: builtins.str = ...,
        organization: builtins.str = ...,
        organizational_unit: builtins.str = ...,
        subject_alternative_name: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "common_name",
            b"common_name",
            "organization",
            b"organization",
            "organizational_unit",
            b"organizational_unit",
            "subject_alternative_name",
            b"subject_alternative_name",
        ],
    ) -> None: ...

global___CertificateFilterSpec = CertificateFilterSpec

class MtlsAuthSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACCEPTED_CLIENT_CA_DEPRECATED_FIELD_NUMBER: builtins.int
    ACCEPTED_CLIENT_CA_FIELD_NUMBER: builtins.int
    CERTIFICATE_FILTERS_FIELD_NUMBER: builtins.int
    ENABLED_FIELD_NUMBER: builtins.int
    accepted_client_ca_deprecated: builtins.str
    """The base64 encoded ca cert(s) in PEM format that the clients can use for authentication and authorization.
    This must only be one value, but the CA can have a chain.

    (-- api-linter: core::0140::base64=disabled --)
    Deprecated: Not supported after v0.2.0 api version. Use accepted_client_ca instead.
    temporal:versioning:max_version=v0.2.0
    """
    accepted_client_ca: builtins.bytes
    """The ca cert(s) in PEM format that the clients can use for authentication and authorization.
    This must only be one value, but the CA can have a chain.
    temporal:versioning:min_version=v0.2.0
    """
    @property
    def certificate_filters(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___CertificateFilterSpec
    ]:
        """Certificate filters which, if specified, only allow connections from client certificates whose distinguished name properties match at least one of the filters.
        This allows limiting access to specific end-entity certificates.
        Optional, default is empty.
        """
    enabled: builtins.bool
    """Flag to enable mTLS auth (default: disabled).
    Note: disabling mTLS auth will cause existing mTLS connections to fail.
    temporal:versioning:min_version=v0.2.0
    """
    def __init__(
        self,
        *,
        accepted_client_ca_deprecated: builtins.str = ...,
        accepted_client_ca: builtins.bytes = ...,
        certificate_filters: collections.abc.Iterable[global___CertificateFilterSpec]
        | None = ...,
        enabled: builtins.bool = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "accepted_client_ca",
            b"accepted_client_ca",
            "accepted_client_ca_deprecated",
            b"accepted_client_ca_deprecated",
            "certificate_filters",
            b"certificate_filters",
            "enabled",
            b"enabled",
        ],
    ) -> None: ...

global___MtlsAuthSpec = MtlsAuthSpec

class ApiKeyAuthSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENABLED_FIELD_NUMBER: builtins.int
    enabled: builtins.bool
    """Flag to enable API key auth (default: disabled).
    Note: disabling API key auth will cause existing API key connections to fail.
    """
    def __init__(
        self,
        *,
        enabled: builtins.bool = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["enabled", b"enabled"]
    ) -> None: ...

global___ApiKeyAuthSpec = ApiKeyAuthSpec

class CodecServerSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class CustomErrorMessage(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        class ErrorMessage(google.protobuf.message.Message):
            DESCRIPTOR: google.protobuf.descriptor.Descriptor

            MESSAGE_FIELD_NUMBER: builtins.int
            LINK_FIELD_NUMBER: builtins.int
            message: builtins.str
            """A message to display."""
            link: builtins.str
            """A link that is displayed along side the configured message."""
            def __init__(
                self,
                *,
                message: builtins.str = ...,
                link: builtins.str = ...,
            ) -> None: ...
            def ClearField(
                self,
                field_name: typing_extensions.Literal[
                    "link", b"link", "message", b"message"
                ],
            ) -> None: ...

        DEFAULT_FIELD_NUMBER: builtins.int
        @property
        def default(self) -> global___CodecServerSpec.CustomErrorMessage.ErrorMessage:
            """The error message to display by default for any remote codec server errors."""
        def __init__(
            self,
            *,
            default: global___CodecServerSpec.CustomErrorMessage.ErrorMessage
            | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["default", b"default"]
        ) -> builtins.bool: ...
        def ClearField(
            self, field_name: typing_extensions.Literal["default", b"default"]
        ) -> None: ...

    ENDPOINT_FIELD_NUMBER: builtins.int
    PASS_ACCESS_TOKEN_FIELD_NUMBER: builtins.int
    INCLUDE_CROSS_ORIGIN_CREDENTIALS_FIELD_NUMBER: builtins.int
    CUSTOM_ERROR_MESSAGE_FIELD_NUMBER: builtins.int
    endpoint: builtins.str
    """The codec server endpoint."""
    pass_access_token: builtins.bool
    """Whether to pass the user access token with your endpoint."""
    include_cross_origin_credentials: builtins.bool
    """Whether to include cross-origin credentials."""
    @property
    def custom_error_message(self) -> global___CodecServerSpec.CustomErrorMessage:
        """A custom error message to display for remote codec server errors.
        temporal:versioning:min_version=v0.5.1
        """
    def __init__(
        self,
        *,
        endpoint: builtins.str = ...,
        pass_access_token: builtins.bool = ...,
        include_cross_origin_credentials: builtins.bool = ...,
        custom_error_message: global___CodecServerSpec.CustomErrorMessage | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "custom_error_message", b"custom_error_message"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "custom_error_message",
            b"custom_error_message",
            "endpoint",
            b"endpoint",
            "include_cross_origin_credentials",
            b"include_cross_origin_credentials",
            "pass_access_token",
            b"pass_access_token",
        ],
    ) -> None: ...

global___CodecServerSpec = CodecServerSpec

class LifecycleSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENABLE_DELETE_PROTECTION_FIELD_NUMBER: builtins.int
    enable_delete_protection: builtins.bool
    """Flag to enable delete protection for the namespace."""
    def __init__(
        self,
        *,
        enable_delete_protection: builtins.bool = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "enable_delete_protection", b"enable_delete_protection"
        ],
    ) -> None: ...

global___LifecycleSpec = LifecycleSpec

class HighAvailabilitySpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DISABLE_MANAGED_FAILOVER_FIELD_NUMBER: builtins.int
    disable_managed_failover: builtins.bool
    """Flag to disable managed failover for the namespace."""
    def __init__(
        self,
        *,
        disable_managed_failover: builtins.bool = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "disable_managed_failover", b"disable_managed_failover"
        ],
    ) -> None: ...

global___HighAvailabilitySpec = HighAvailabilitySpec

class NamespaceSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _SearchAttributeType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _SearchAttributeTypeEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            NamespaceSpec._SearchAttributeType.ValueType
        ],
        builtins.type,
    ):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        SEARCH_ATTRIBUTE_TYPE_UNSPECIFIED: (
            NamespaceSpec._SearchAttributeType.ValueType
        )  # 0
        SEARCH_ATTRIBUTE_TYPE_TEXT: NamespaceSpec._SearchAttributeType.ValueType  # 1
        SEARCH_ATTRIBUTE_TYPE_KEYWORD: NamespaceSpec._SearchAttributeType.ValueType  # 2
        SEARCH_ATTRIBUTE_TYPE_INT: NamespaceSpec._SearchAttributeType.ValueType  # 3
        SEARCH_ATTRIBUTE_TYPE_DOUBLE: NamespaceSpec._SearchAttributeType.ValueType  # 4
        SEARCH_ATTRIBUTE_TYPE_BOOL: NamespaceSpec._SearchAttributeType.ValueType  # 5
        SEARCH_ATTRIBUTE_TYPE_DATETIME: (
            NamespaceSpec._SearchAttributeType.ValueType
        )  # 6
        SEARCH_ATTRIBUTE_TYPE_KEYWORD_LIST: (
            NamespaceSpec._SearchAttributeType.ValueType
        )  # 7

    class SearchAttributeType(
        _SearchAttributeType, metaclass=_SearchAttributeTypeEnumTypeWrapper
    ): ...
    SEARCH_ATTRIBUTE_TYPE_UNSPECIFIED: NamespaceSpec.SearchAttributeType.ValueType  # 0
    SEARCH_ATTRIBUTE_TYPE_TEXT: NamespaceSpec.SearchAttributeType.ValueType  # 1
    SEARCH_ATTRIBUTE_TYPE_KEYWORD: NamespaceSpec.SearchAttributeType.ValueType  # 2
    SEARCH_ATTRIBUTE_TYPE_INT: NamespaceSpec.SearchAttributeType.ValueType  # 3
    SEARCH_ATTRIBUTE_TYPE_DOUBLE: NamespaceSpec.SearchAttributeType.ValueType  # 4
    SEARCH_ATTRIBUTE_TYPE_BOOL: NamespaceSpec.SearchAttributeType.ValueType  # 5
    SEARCH_ATTRIBUTE_TYPE_DATETIME: NamespaceSpec.SearchAttributeType.ValueType  # 6
    SEARCH_ATTRIBUTE_TYPE_KEYWORD_LIST: NamespaceSpec.SearchAttributeType.ValueType  # 7

    class CustomSearchAttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    class SearchAttributesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: global___NamespaceSpec.SearchAttributeType.ValueType
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___NamespaceSpec.SearchAttributeType.ValueType = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    NAME_FIELD_NUMBER: builtins.int
    REGIONS_FIELD_NUMBER: builtins.int
    RETENTION_DAYS_FIELD_NUMBER: builtins.int
    MTLS_AUTH_FIELD_NUMBER: builtins.int
    API_KEY_AUTH_FIELD_NUMBER: builtins.int
    CUSTOM_SEARCH_ATTRIBUTES_FIELD_NUMBER: builtins.int
    SEARCH_ATTRIBUTES_FIELD_NUMBER: builtins.int
    CODEC_SERVER_FIELD_NUMBER: builtins.int
    LIFECYCLE_FIELD_NUMBER: builtins.int
    HIGH_AVAILABILITY_FIELD_NUMBER: builtins.int
    CONNECTIVITY_RULE_IDS_FIELD_NUMBER: builtins.int
    name: builtins.str
    """The name to use for the namespace.
    This will create a namespace that's available at '<name>.<account>.tmprl.cloud:7233'.
    The name is immutable. Once set, it cannot be changed.
    """
    @property
    def regions(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """The ids of the regions where the namespace should be available.
        The GetRegions API can be used to get the list of valid region ids.
        Specifying more than one region makes the namespace "global", which is currently a preview only feature with restricted access.
        Please reach out to Temporal support for more information on global namespaces.
        When provisioned the global namespace will be active on the first region in the list and passive on the rest.
        Number of supported regions is 2.
        The regions is immutable. Once set, it cannot be changed.
        Example: ["aws-us-west-2"].
        """
    retention_days: builtins.int
    """The number of days the workflows data will be retained for.
    Changes to the retention period may impact your storage costs.
    Any changes to the retention period will be applied to all new running workflows.
    """
    @property
    def mtls_auth(self) -> global___MtlsAuthSpec:
        """The mTLS auth configuration for the namespace.
        If unspecified, mTLS will be disabled.
        """
    @property
    def api_key_auth(self) -> global___ApiKeyAuthSpec:
        """The API key auth configuration for the namespace.
        If unspecified, API keys will be disabled.
        temporal:versioning:min_version=v0.2.0
        """
    @property
    def custom_search_attributes(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """The custom search attributes to use for the namespace.
        The name of the attribute is the key and the type is the value.
        Supported attribute types: text, keyword, int, double, bool, datetime, keyword_list.
        NOTE: currently deleting a search attribute is not supported.
        Optional, default is empty.
        Deprecated: Not supported after v0.3.0 api version. Use search_attributes instead.
        temporal:versioning:max_version=v0.3.0
        """
    @property
    def search_attributes(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[
        builtins.str, global___NamespaceSpec.SearchAttributeType.ValueType
    ]:
        """The custom search attributes to use for the namespace.
        The name of the attribute is the key and the type is the value.
        Note: currently deleting a search attribute is not supported.
        Optional, default is empty.
        temporal:versioning:min_version=v0.3.0
        temporal:enums:replaces=custom_search_attributes
        """
    @property
    def codec_server(self) -> global___CodecServerSpec:
        """Codec server spec used by UI to decode payloads for all users interacting with this namespace.
        Optional, default is unset.
        """
    @property
    def lifecycle(self) -> global___LifecycleSpec:
        """The lifecycle configuration for the namespace.
        temporal:versioning:min_version=v0.4.0
        """
    @property
    def high_availability(self) -> global___HighAvailabilitySpec:
        """The high availability configuration for the namespace.
        temporal:versioning:min_version=v0.4.0
        """
    @property
    def connectivity_rule_ids(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """The private connectivity configuration for the namespace.
        This will apply the connectivity rules specified to the namespace.
        temporal:versioning:min_version=v0.6.0
        """
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        regions: collections.abc.Iterable[builtins.str] | None = ...,
        retention_days: builtins.int = ...,
        mtls_auth: global___MtlsAuthSpec | None = ...,
        api_key_auth: global___ApiKeyAuthSpec | None = ...,
        custom_search_attributes: collections.abc.Mapping[builtins.str, builtins.str]
        | None = ...,
        search_attributes: collections.abc.Mapping[
            builtins.str, global___NamespaceSpec.SearchAttributeType.ValueType
        ]
        | None = ...,
        codec_server: global___CodecServerSpec | None = ...,
        lifecycle: global___LifecycleSpec | None = ...,
        high_availability: global___HighAvailabilitySpec | None = ...,
        connectivity_rule_ids: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "api_key_auth",
            b"api_key_auth",
            "codec_server",
            b"codec_server",
            "high_availability",
            b"high_availability",
            "lifecycle",
            b"lifecycle",
            "mtls_auth",
            b"mtls_auth",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "api_key_auth",
            b"api_key_auth",
            "codec_server",
            b"codec_server",
            "connectivity_rule_ids",
            b"connectivity_rule_ids",
            "custom_search_attributes",
            b"custom_search_attributes",
            "high_availability",
            b"high_availability",
            "lifecycle",
            b"lifecycle",
            "mtls_auth",
            b"mtls_auth",
            "name",
            b"name",
            "regions",
            b"regions",
            "retention_days",
            b"retention_days",
            "search_attributes",
            b"search_attributes",
        ],
    ) -> None: ...

global___NamespaceSpec = NamespaceSpec

class Endpoints(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WEB_ADDRESS_FIELD_NUMBER: builtins.int
    MTLS_GRPC_ADDRESS_FIELD_NUMBER: builtins.int
    GRPC_ADDRESS_FIELD_NUMBER: builtins.int
    web_address: builtins.str
    """The web UI address."""
    mtls_grpc_address: builtins.str
    """The gRPC address for mTLS client connections (may be empty if mTLS is disabled)."""
    grpc_address: builtins.str
    """The gRPC address for API key client connections (may be empty if API keys are disabled)."""
    def __init__(
        self,
        *,
        web_address: builtins.str = ...,
        mtls_grpc_address: builtins.str = ...,
        grpc_address: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "grpc_address",
            b"grpc_address",
            "mtls_grpc_address",
            b"mtls_grpc_address",
            "web_address",
            b"web_address",
        ],
    ) -> None: ...

global___Endpoints = Endpoints

class Limits(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACTIONS_PER_SECOND_LIMIT_FIELD_NUMBER: builtins.int
    actions_per_second_limit: builtins.int
    """The number of actions per second (APS) that is currently allowed for the namespace.
    The namespace may be throttled if its APS exceeds the limit.
    """
    def __init__(
        self,
        *,
        actions_per_second_limit: builtins.int = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "actions_per_second_limit", b"actions_per_second_limit"
        ],
    ) -> None: ...

global___Limits = Limits

class AWSPrivateLinkInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ALLOWED_PRINCIPAL_ARNS_FIELD_NUMBER: builtins.int
    VPC_ENDPOINT_SERVICE_NAMES_FIELD_NUMBER: builtins.int
    @property
    def allowed_principal_arns(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """The list of principal arns that are allowed to access the namespace on the private link."""
    @property
    def vpc_endpoint_service_names(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """The list of vpc endpoint service names that are associated with the namespace."""
    def __init__(
        self,
        *,
        allowed_principal_arns: collections.abc.Iterable[builtins.str] | None = ...,
        vpc_endpoint_service_names: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "allowed_principal_arns",
            b"allowed_principal_arns",
            "vpc_endpoint_service_names",
            b"vpc_endpoint_service_names",
        ],
    ) -> None: ...

global___AWSPrivateLinkInfo = AWSPrivateLinkInfo

class PrivateConnectivity(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REGION_FIELD_NUMBER: builtins.int
    AWS_PRIVATE_LINK_FIELD_NUMBER: builtins.int
    region: builtins.str
    """The id of the region where the private connectivity applies."""
    @property
    def aws_private_link(self) -> global___AWSPrivateLinkInfo:
        """The AWS PrivateLink info.
        This will only be set for an aws region.
        """
    def __init__(
        self,
        *,
        region: builtins.str = ...,
        aws_private_link: global___AWSPrivateLinkInfo | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["aws_private_link", b"aws_private_link"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "aws_private_link", b"aws_private_link", "region", b"region"
        ],
    ) -> None: ...

global___PrivateConnectivity = PrivateConnectivity

class Namespace(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class RegionStatusEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___NamespaceRegionStatus: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___NamespaceRegionStatus | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["value", b"value"]
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    class TagsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    NAMESPACE_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    STATE_DEPRECATED_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    ENDPOINTS_FIELD_NUMBER: builtins.int
    ACTIVE_REGION_FIELD_NUMBER: builtins.int
    LIMITS_FIELD_NUMBER: builtins.int
    PRIVATE_CONNECTIVITIES_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    LAST_MODIFIED_TIME_FIELD_NUMBER: builtins.int
    REGION_STATUS_FIELD_NUMBER: builtins.int
    CONNECTIVITY_RULES_FIELD_NUMBER: builtins.int
    TAGS_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace identifier."""
    resource_version: builtins.str
    """The current version of the namespace specification.
    The next update operation will have to include this version.
    """
    @property
    def spec(self) -> global___NamespaceSpec:
        """The namespace specification."""
    state_deprecated: builtins.str
    """The current state of the namespace.
    Deprecated: Not supported after v0.3.0 api version. Use state instead.
    temporal:versioning:max_version=v0.3.0
    """
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the namespace.
    For any failed state, reach out to Temporal Cloud support for remediation.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=state_deprecated
    """
    async_operation_id: builtins.str
    """The id of the async operation that is creating/updating/deleting the namespace, if any."""
    @property
    def endpoints(self) -> global___Endpoints:
        """The endpoints for the namespace."""
    active_region: builtins.str
    """The currently active region for the namespace."""
    @property
    def limits(self) -> global___Limits:
        """The limits set on the namespace currently."""
    @property
    def private_connectivities(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___PrivateConnectivity
    ]:
        """The private connectivities for the namespace, if any."""
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the namespace was created."""
    @property
    def last_modified_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the namespace was last modified.
        Will not be set if the namespace has never been modified.
        """
    @property
    def region_status(
        self,
    ) -> google.protobuf.internal.containers.MessageMap[
        builtins.str, global___NamespaceRegionStatus
    ]:
        """The status of each region where the namespace is available.
        The id of the region is the key and the status is the value of the map.
        """
    @property
    def connectivity_rules(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRule
    ]:
        """The connectivity rules that are set on this namespace."""
    @property
    def tags(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """The tags for the namespace."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        resource_version: builtins.str = ...,
        spec: global___NamespaceSpec | None = ...,
        state_deprecated: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        endpoints: global___Endpoints | None = ...,
        active_region: builtins.str = ...,
        limits: global___Limits | None = ...,
        private_connectivities: collections.abc.Iterable[global___PrivateConnectivity]
        | None = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_modified_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        region_status: collections.abc.Mapping[
            builtins.str, global___NamespaceRegionStatus
        ]
        | None = ...,
        connectivity_rules: collections.abc.Iterable[
            temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRule
        ]
        | None = ...,
        tags: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time",
            b"created_time",
            "endpoints",
            b"endpoints",
            "last_modified_time",
            b"last_modified_time",
            "limits",
            b"limits",
            "spec",
            b"spec",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "active_region",
            b"active_region",
            "async_operation_id",
            b"async_operation_id",
            "connectivity_rules",
            b"connectivity_rules",
            "created_time",
            b"created_time",
            "endpoints",
            b"endpoints",
            "last_modified_time",
            b"last_modified_time",
            "limits",
            b"limits",
            "namespace",
            b"namespace",
            "private_connectivities",
            b"private_connectivities",
            "region_status",
            b"region_status",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
            "state_deprecated",
            b"state_deprecated",
            "tags",
            b"tags",
        ],
    ) -> None: ...

global___Namespace = Namespace

class NamespaceRegionStatus(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _State:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _StateEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            NamespaceRegionStatus._State.ValueType
        ],
        builtins.type,
    ):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        STATE_UNSPECIFIED: NamespaceRegionStatus._State.ValueType  # 0
        STATE_ADDING: NamespaceRegionStatus._State.ValueType  # 1
        """The region is being added to the namespace."""
        STATE_ACTIVE: NamespaceRegionStatus._State.ValueType  # 2
        """The namespace is active in this region."""
        STATE_PASSIVE: NamespaceRegionStatus._State.ValueType  # 3
        """The namespace is passive in this region."""
        STATE_REMOVING: NamespaceRegionStatus._State.ValueType  # 4
        """The region is being removed from the namespace."""
        STATE_FAILED: NamespaceRegionStatus._State.ValueType  # 5
        """The region failed to be added/removed, check failure_reason in the last async_operation status for more details."""

    class State(_State, metaclass=_StateEnumTypeWrapper): ...
    STATE_UNSPECIFIED: NamespaceRegionStatus.State.ValueType  # 0
    STATE_ADDING: NamespaceRegionStatus.State.ValueType  # 1
    """The region is being added to the namespace."""
    STATE_ACTIVE: NamespaceRegionStatus.State.ValueType  # 2
    """The namespace is active in this region."""
    STATE_PASSIVE: NamespaceRegionStatus.State.ValueType  # 3
    """The namespace is passive in this region."""
    STATE_REMOVING: NamespaceRegionStatus.State.ValueType  # 4
    """The region is being removed from the namespace."""
    STATE_FAILED: NamespaceRegionStatus.State.ValueType  # 5
    """The region failed to be added/removed, check failure_reason in the last async_operation status for more details."""

    STATE_DEPRECATED_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    state_deprecated: builtins.str
    """The current state of the namespace region.
    Possible values: adding, active, passive, removing, failed.
    For any failed state, reach out to Temporal Cloud support for remediation.
    Deprecated: Not supported after v0.3.0 api version. Use state instead.
    temporal:versioning:max_version=v0.3.0
    """
    state: global___NamespaceRegionStatus.State.ValueType
    """The current state of the namespace region.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=state_deprecated
    """
    async_operation_id: builtins.str
    """The id of the async operation that is making changes to where the namespace is available, if any."""
    def __init__(
        self,
        *,
        state_deprecated: builtins.str = ...,
        state: global___NamespaceRegionStatus.State.ValueType = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "state",
            b"state",
            "state_deprecated",
            b"state_deprecated",
        ],
    ) -> None: ...

global___NamespaceRegionStatus = NamespaceRegionStatus

class ExportSinkSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    ENABLED_FIELD_NUMBER: builtins.int
    S3_FIELD_NUMBER: builtins.int
    GCS_FIELD_NUMBER: builtins.int
    name: builtins.str
    """The unique name of the export sink, it can't be changed once set."""
    enabled: builtins.bool
    """A flag indicating whether the export sink is enabled or not."""
    @property
    def s3(self) -> temporalio.api.cloud.sink.v1.message_pb2.S3Spec:
        """The S3 configuration details when destination_type is S3."""
    @property
    def gcs(self) -> temporalio.api.cloud.sink.v1.message_pb2.GCSSpec:
        """The GCS configuration details when destination_type is GCS."""
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        enabled: builtins.bool = ...,
        s3: temporalio.api.cloud.sink.v1.message_pb2.S3Spec | None = ...,
        gcs: temporalio.api.cloud.sink.v1.message_pb2.GCSSpec | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["gcs", b"gcs", "s3", b"s3"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "enabled", b"enabled", "gcs", b"gcs", "name", b"name", "s3", b"s3"
        ],
    ) -> None: ...

global___ExportSinkSpec = ExportSinkSpec

class ExportSink(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Health:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _HealthEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            ExportSink._Health.ValueType
        ],
        builtins.type,
    ):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        HEALTH_UNSPECIFIED: ExportSink._Health.ValueType  # 0
        HEALTH_OK: ExportSink._Health.ValueType  # 1
        HEALTH_ERROR_INTERNAL: ExportSink._Health.ValueType  # 2
        HEALTH_ERROR_USER_CONFIGURATION: ExportSink._Health.ValueType  # 3

    class Health(_Health, metaclass=_HealthEnumTypeWrapper): ...
    HEALTH_UNSPECIFIED: ExportSink.Health.ValueType  # 0
    HEALTH_OK: ExportSink.Health.ValueType  # 1
    HEALTH_ERROR_INTERNAL: ExportSink.Health.ValueType  # 2
    HEALTH_ERROR_USER_CONFIGURATION: ExportSink.Health.ValueType  # 3

    NAME_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    HEALTH_FIELD_NUMBER: builtins.int
    ERROR_MESSAGE_FIELD_NUMBER: builtins.int
    LATEST_DATA_EXPORT_TIME_FIELD_NUMBER: builtins.int
    LAST_HEALTH_CHECK_TIME_FIELD_NUMBER: builtins.int
    name: builtins.str
    """The unique name of the export sink."""
    resource_version: builtins.str
    """The version of the export sink resource."""
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the export sink."""
    @property
    def spec(self) -> global___ExportSinkSpec:
        """The specification details of the export sink."""
    health: global___ExportSink.Health.ValueType
    """The health status of the export sink."""
    error_message: builtins.str
    """An error message describing any issues with the export sink, if applicable."""
    @property
    def latest_data_export_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The timestamp of the latest successful data export."""
    @property
    def last_health_check_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The timestamp of the last health check performed on the export sink."""
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        resource_version: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        spec: global___ExportSinkSpec | None = ...,
        health: global___ExportSink.Health.ValueType = ...,
        error_message: builtins.str = ...,
        latest_data_export_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_health_check_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "last_health_check_time",
            b"last_health_check_time",
            "latest_data_export_time",
            b"latest_data_export_time",
            "spec",
            b"spec",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "error_message",
            b"error_message",
            "health",
            b"health",
            "last_health_check_time",
            b"last_health_check_time",
            "latest_data_export_time",
            b"latest_data_export_time",
            "name",
            b"name",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
        ],
    ) -> None: ...

global___ExportSink = ExportSink
