"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class Region(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _CloudProvider:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _CloudProviderEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            Region._CloudProvider.ValueType
        ],
        builtins.type,
    ):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        CLOUD_PROVIDER_UNSPECIFIED: Region._CloudProvider.ValueType  # 0
        CLOUD_PROVIDER_AWS: Region._CloudProvider.ValueType  # 1
        CLOUD_PROVIDER_GCP: Region._CloudProvider.ValueType  # 2

    class CloudProvider(_CloudProvider, metaclass=_CloudProviderEnumTypeWrapper):
        """The cloud provider that's hosting the region."""

    CLOUD_PROVIDER_UNSPECIFIED: Region.CloudProvider.ValueType  # 0
    CLOUD_PROVIDER_AWS: Region.CloudProvider.ValueType  # 1
    CLOUD_PROVIDER_GCP: Region.CloudProvider.ValueType  # 2

    ID_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_DEPRECATED_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_FIELD_NUMBER: builtins.int
    CLOUD_PROVIDER_REGION_FIELD_NUMBER: builtins.int
    LOCATION_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the temporal cloud region."""
    cloud_provider_deprecated: builtins.str
    """The name of the cloud provider that's hosting the region.
    Currently only "aws" is supported.
    Deprecated: Not supported after v0.3.0 api version. Use cloud_provider instead. 
    temporal:versioning:max_version=v0.3.0
    """
    cloud_provider: global___Region.CloudProvider.ValueType
    """The cloud provider that's hosting the region.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=cloud_provider_deprecated
    """
    cloud_provider_region: builtins.str
    """The region identifier as defined by the cloud provider."""
    location: builtins.str
    """The human readable location of the region."""
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        cloud_provider_deprecated: builtins.str = ...,
        cloud_provider: global___Region.CloudProvider.ValueType = ...,
        cloud_provider_region: builtins.str = ...,
        location: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "cloud_provider",
            b"cloud_provider",
            "cloud_provider_deprecated",
            b"cloud_provider_deprecated",
            "cloud_provider_region",
            b"cloud_provider_region",
            "id",
            b"id",
            "location",
            b"location",
        ],
    ) -> None: ...

global___Region = Region
