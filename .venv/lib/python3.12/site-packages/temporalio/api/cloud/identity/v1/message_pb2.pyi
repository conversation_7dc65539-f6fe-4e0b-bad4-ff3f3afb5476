"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import google.protobuf.timestamp_pb2

import temporalio.api.cloud.resource.v1.message_pb2

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _OwnerType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _OwnerTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_OwnerType.ValueType],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    OWNER_TYPE_UNSPECIFIED: _OwnerType.ValueType  # 0
    OWNER_TYPE_USER: _OwnerType.ValueType  # 1
    """The owner is a user."""
    OWNER_TYPE_SERVICE_ACCOUNT: _OwnerType.ValueType  # 2
    """The owner is a service account."""

class OwnerType(_OwnerType, metaclass=_OwnerTypeEnumTypeWrapper): ...

OWNER_TYPE_UNSPECIFIED: OwnerType.ValueType  # 0
OWNER_TYPE_USER: OwnerType.ValueType  # 1
"""The owner is a user."""
OWNER_TYPE_SERVICE_ACCOUNT: OwnerType.ValueType  # 2
"""The owner is a service account."""
global___OwnerType = OwnerType

class AccountAccess(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Role:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _RoleEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            AccountAccess._Role.ValueType
        ],
        builtins.type,
    ):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        ROLE_UNSPECIFIED: AccountAccess._Role.ValueType  # 0
        ROLE_OWNER: AccountAccess._Role.ValueType  # 1
        """Gives full access to the account, including users, namespaces, and billing."""
        ROLE_ADMIN: AccountAccess._Role.ValueType  # 2
        """Gives full access to the account, including users and namespaces."""
        ROLE_DEVELOPER: AccountAccess._Role.ValueType  # 3
        """Gives access to create namespaces on the account."""
        ROLE_FINANCE_ADMIN: AccountAccess._Role.ValueType  # 4
        """Gives read only access and write access for billing."""
        ROLE_READ: AccountAccess._Role.ValueType  # 5
        """Gives read only access to the account."""
        ROLE_METRICS_READ: AccountAccess._Role.ValueType  # 6
        """Gives read only access to the account metrics."""

    class Role(_Role, metaclass=_RoleEnumTypeWrapper): ...
    ROLE_UNSPECIFIED: AccountAccess.Role.ValueType  # 0
    ROLE_OWNER: AccountAccess.Role.ValueType  # 1
    """Gives full access to the account, including users, namespaces, and billing."""
    ROLE_ADMIN: AccountAccess.Role.ValueType  # 2
    """Gives full access to the account, including users and namespaces."""
    ROLE_DEVELOPER: AccountAccess.Role.ValueType  # 3
    """Gives access to create namespaces on the account."""
    ROLE_FINANCE_ADMIN: AccountAccess.Role.ValueType  # 4
    """Gives read only access and write access for billing."""
    ROLE_READ: AccountAccess.Role.ValueType  # 5
    """Gives read only access to the account."""
    ROLE_METRICS_READ: AccountAccess.Role.ValueType  # 6
    """Gives read only access to the account metrics."""

    ROLE_DEPRECATED_FIELD_NUMBER: builtins.int
    ROLE_FIELD_NUMBER: builtins.int
    role_deprecated: builtins.str
    """The role on the account, should be one of [owner, admin, developer, financeadmin, read, metricsread]
    owner - gives full access to the account, including users, namespaces, and billing
    admin - gives full access the account, including users and namespaces
    developer - gives access to create namespaces on the account
    financeadmin - gives read only access and write access for billing
    read - gives read only access to the account
    metricsread - gives read only access to all namespace metrics
    Deprecated: Not supported after v0.3.0 api version. Use role instead.
    temporal:versioning:max_version=v0.3.0
    """
    role: global___AccountAccess.Role.ValueType
    """The role on the account.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=role_deprecated
    """
    def __init__(
        self,
        *,
        role_deprecated: builtins.str = ...,
        role: global___AccountAccess.Role.ValueType = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "role", b"role", "role_deprecated", b"role_deprecated"
        ],
    ) -> None: ...

global___AccountAccess = AccountAccess

class NamespaceAccess(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Permission:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _PermissionEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            NamespaceAccess._Permission.ValueType
        ],
        builtins.type,
    ):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        PERMISSION_UNSPECIFIED: NamespaceAccess._Permission.ValueType  # 0
        PERMISSION_ADMIN: NamespaceAccess._Permission.ValueType  # 1
        """Gives full access to the namespace, including assigning namespace access to other users."""
        PERMISSION_WRITE: NamespaceAccess._Permission.ValueType  # 2
        """Gives write access to the namespace configuration and workflows within the namespace."""
        PERMISSION_READ: NamespaceAccess._Permission.ValueType  # 3
        """Gives read only access to the namespace configuration and workflows within the namespace."""

    class Permission(_Permission, metaclass=_PermissionEnumTypeWrapper): ...
    PERMISSION_UNSPECIFIED: NamespaceAccess.Permission.ValueType  # 0
    PERMISSION_ADMIN: NamespaceAccess.Permission.ValueType  # 1
    """Gives full access to the namespace, including assigning namespace access to other users."""
    PERMISSION_WRITE: NamespaceAccess.Permission.ValueType  # 2
    """Gives write access to the namespace configuration and workflows within the namespace."""
    PERMISSION_READ: NamespaceAccess.Permission.ValueType  # 3
    """Gives read only access to the namespace configuration and workflows within the namespace."""

    PERMISSION_DEPRECATED_FIELD_NUMBER: builtins.int
    PERMISSION_FIELD_NUMBER: builtins.int
    permission_deprecated: builtins.str
    """The permission to the namespace, should be one of [admin, write, read]
    admin - gives full access to the namespace, including assigning namespace access to other users
    write - gives write access to the namespace configuration and workflows within the namespace
    read - gives read only access to the namespace configuration and workflows within the namespace
    Deprecated: Not supported after v0.3.0 api version. Use permission instead.
    temporal:versioning:max_version=v0.3.0
    """
    permission: global___NamespaceAccess.Permission.ValueType
    """The permission to the namespace.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=permission_deprecated
    """
    def __init__(
        self,
        *,
        permission_deprecated: builtins.str = ...,
        permission: global___NamespaceAccess.Permission.ValueType = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "permission",
            b"permission",
            "permission_deprecated",
            b"permission_deprecated",
        ],
    ) -> None: ...

global___NamespaceAccess = NamespaceAccess

class Access(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class NamespaceAccessesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        @property
        def value(self) -> global___NamespaceAccess: ...
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: global___NamespaceAccess | None = ...,
        ) -> None: ...
        def HasField(
            self, field_name: typing_extensions.Literal["value", b"value"]
        ) -> builtins.bool: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    ACCOUNT_ACCESS_FIELD_NUMBER: builtins.int
    NAMESPACE_ACCESSES_FIELD_NUMBER: builtins.int
    @property
    def account_access(self) -> global___AccountAccess:
        """The account access"""
    @property
    def namespace_accesses(
        self,
    ) -> google.protobuf.internal.containers.MessageMap[
        builtins.str, global___NamespaceAccess
    ]:
        """The map of namespace accesses
        The key is the namespace name and the value is the access to the namespace
        """
    def __init__(
        self,
        *,
        account_access: global___AccountAccess | None = ...,
        namespace_accesses: collections.abc.Mapping[
            builtins.str, global___NamespaceAccess
        ]
        | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["account_access", b"account_access"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "account_access",
            b"account_access",
            "namespace_accesses",
            b"namespace_accesses",
        ],
    ) -> None: ...

global___Access = Access

class NamespaceScopedAccess(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    ACCESS_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace the service account is assigned to - immutable."""
    @property
    def access(self) -> global___NamespaceAccess:
        """The namespace access assigned to the service account - mutable."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        access: global___NamespaceAccess | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["access", b"access"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "access", b"access", "namespace", b"namespace"
        ],
    ) -> None: ...

global___NamespaceScopedAccess = NamespaceScopedAccess

class UserSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EMAIL_FIELD_NUMBER: builtins.int
    ACCESS_FIELD_NUMBER: builtins.int
    email: builtins.str
    """The email address associated to the user"""
    @property
    def access(self) -> global___Access:
        """The access to assigned to the user"""
    def __init__(
        self,
        *,
        email: builtins.str = ...,
        access: global___Access | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["access", b"access"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["access", b"access", "email", b"email"],
    ) -> None: ...

global___UserSpec = UserSpec

class Invitation(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CREATED_TIME_FIELD_NUMBER: builtins.int
    EXPIRED_TIME_FIELD_NUMBER: builtins.int
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the user was created"""
    @property
    def expired_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the invitation expires or has expired"""
    def __init__(
        self,
        *,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        expired_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time", b"created_time", "expired_time", b"expired_time"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "created_time", b"created_time", "expired_time", b"expired_time"
        ],
    ) -> None: ...

global___Invitation = Invitation

class User(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    STATE_DEPRECATED_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    INVITATION_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    LAST_MODIFIED_TIME_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the user"""
    resource_version: builtins.str
    """The current version of the user specification
    The next update operation will have to include this version
    """
    @property
    def spec(self) -> global___UserSpec:
        """The user specification"""
    state_deprecated: builtins.str
    """The current state of the user
    Deprecated: Not supported after v0.3.0 api version. Use state instead.
    temporal:versioning:max_version=v0.3.0
    """
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the user.
    For any failed state, reach out to Temporal Cloud support for remediation.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=state_deprecated
    """
    async_operation_id: builtins.str
    """The id of the async operation that is creating/updating/deleting the user, if any"""
    @property
    def invitation(self) -> global___Invitation:
        """The details of the open invitation sent to the user, if any"""
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the user was created"""
    @property
    def last_modified_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the user was last modified
        Will not be set if the user has never been modified
        """
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        resource_version: builtins.str = ...,
        spec: global___UserSpec | None = ...,
        state_deprecated: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        invitation: global___Invitation | None = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_modified_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time",
            b"created_time",
            "invitation",
            b"invitation",
            "last_modified_time",
            b"last_modified_time",
            "spec",
            b"spec",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "created_time",
            b"created_time",
            "id",
            b"id",
            "invitation",
            b"invitation",
            "last_modified_time",
            b"last_modified_time",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
            "state_deprecated",
            b"state_deprecated",
        ],
    ) -> None: ...

global___User = User

class GoogleGroupSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EMAIL_ADDRESS_FIELD_NUMBER: builtins.int
    email_address: builtins.str
    """The email address of the Google group.
    The email address is immutable. Once set during creation, it cannot be changed.
    """
    def __init__(
        self,
        *,
        email_address: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["email_address", b"email_address"]
    ) -> None: ...

global___GoogleGroupSpec = GoogleGroupSpec

class SCIMGroupSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDP_ID_FIELD_NUMBER: builtins.int
    idp_id: builtins.str
    """The id used in the upstream identity provider."""
    def __init__(
        self,
        *,
        idp_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["idp_id", b"idp_id"]
    ) -> None: ...

global___SCIMGroupSpec = SCIMGroupSpec

class CloudGroupSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___CloudGroupSpec = CloudGroupSpec

class UserGroupSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DISPLAY_NAME_FIELD_NUMBER: builtins.int
    ACCESS_FIELD_NUMBER: builtins.int
    GOOGLE_GROUP_FIELD_NUMBER: builtins.int
    SCIM_GROUP_FIELD_NUMBER: builtins.int
    CLOUD_GROUP_FIELD_NUMBER: builtins.int
    display_name: builtins.str
    """The display name of the group."""
    @property
    def access(self) -> global___Access:
        """The access assigned to the group."""
    @property
    def google_group(self) -> global___GoogleGroupSpec:
        """The specification of the google group that this group is associated with."""
    @property
    def scim_group(self) -> global___SCIMGroupSpec:
        """The specification of the SCIM group that this group is associated with.
        SCIM groups cannot be created or deleted directly, but their access can be managed.
        """
    @property
    def cloud_group(self) -> global___CloudGroupSpec:
        """The specification for a Cloud group. Cloud groups can manage members using
        the add and remove member APIs.
        """
    def __init__(
        self,
        *,
        display_name: builtins.str = ...,
        access: global___Access | None = ...,
        google_group: global___GoogleGroupSpec | None = ...,
        scim_group: global___SCIMGroupSpec | None = ...,
        cloud_group: global___CloudGroupSpec | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "access",
            b"access",
            "cloud_group",
            b"cloud_group",
            "google_group",
            b"google_group",
            "group_type",
            b"group_type",
            "scim_group",
            b"scim_group",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "access",
            b"access",
            "cloud_group",
            b"cloud_group",
            "display_name",
            b"display_name",
            "google_group",
            b"google_group",
            "group_type",
            b"group_type",
            "scim_group",
            b"scim_group",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["group_type", b"group_type"]
    ) -> (
        typing_extensions.Literal["google_group", "scim_group", "cloud_group"] | None
    ): ...

global___UserGroupSpec = UserGroupSpec

class UserGroup(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    STATE_DEPRECATED_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    LAST_MODIFIED_TIME_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the group"""
    resource_version: builtins.str
    """The current version of the group specification
    The next update operation will have to include this version
    """
    @property
    def spec(self) -> global___UserGroupSpec:
        """The group specification"""
    state_deprecated: builtins.str
    """The current state of the group.
    Deprecated: Not supported after v0.3.0 api version. Use state instead.
    temporal:versioning:max_version=v0.3.0
    """
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the group.
    For any failed state, reach out to Temporal Cloud support for remediation.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=state_deprecated
    """
    async_operation_id: builtins.str
    """The id of the async operation that is creating/updating/deleting the group, if any"""
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the group was created"""
    @property
    def last_modified_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the group was last modified
        Will not be set if the group has never been modified
        """
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        resource_version: builtins.str = ...,
        spec: global___UserGroupSpec | None = ...,
        state_deprecated: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_modified_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time",
            b"created_time",
            "last_modified_time",
            b"last_modified_time",
            "spec",
            b"spec",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "created_time",
            b"created_time",
            "id",
            b"id",
            "last_modified_time",
            b"last_modified_time",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
            "state_deprecated",
            b"state_deprecated",
        ],
    ) -> None: ...

global___UserGroup = UserGroup

class UserGroupMemberId(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_ID_FIELD_NUMBER: builtins.int
    user_id: builtins.str
    def __init__(
        self,
        *,
        user_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "member_type", b"member_type", "user_id", b"user_id"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "member_type", b"member_type", "user_id", b"user_id"
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["member_type", b"member_type"]
    ) -> typing_extensions.Literal["user_id"] | None: ...

global___UserGroupMemberId = UserGroupMemberId

class UserGroupMember(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MEMBER_ID_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    @property
    def member_id(self) -> global___UserGroupMemberId: ...
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp: ...
    def __init__(
        self,
        *,
        member_id: global___UserGroupMemberId | None = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time", b"created_time", "member_id", b"member_id"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "created_time", b"created_time", "member_id", b"member_id"
        ],
    ) -> None: ...

global___UserGroupMember = UserGroupMember

class ServiceAccount(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    STATE_DEPRECATED_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    LAST_MODIFIED_TIME_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the service account."""
    resource_version: builtins.str
    """The current version of the service account specification.
    The next update operation will have to include this version.
    """
    @property
    def spec(self) -> global___ServiceAccountSpec:
        """The service account specification."""
    state_deprecated: builtins.str
    """The current state of the service account.
    Possible values: activating, activationfailed, active, updating, updatefailed, deleting, deletefailed, deleted, suspending, suspendfailed, suspended.
    For any failed state, reach out to Temporal Cloud support for remediation.
    Deprecated: Not supported after v0.3.0 api version. Use state instead.
    temporal:versioning:max_version=v0.3.0
    """
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the service account.
    For any failed state, reach out to Temporal Cloud support for remediation.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=state_deprecated
    """
    async_operation_id: builtins.str
    """The id of the async operation that is creating/updating/deleting the service account, if any."""
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the service account was created."""
    @property
    def last_modified_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the service account was last modified
        Will not be set if the service account has never been modified.
        """
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        resource_version: builtins.str = ...,
        spec: global___ServiceAccountSpec | None = ...,
        state_deprecated: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_modified_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time",
            b"created_time",
            "last_modified_time",
            b"last_modified_time",
            "spec",
            b"spec",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "created_time",
            b"created_time",
            "id",
            b"id",
            "last_modified_time",
            b"last_modified_time",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
            "state_deprecated",
            b"state_deprecated",
        ],
    ) -> None: ...

global___ServiceAccount = ServiceAccount

class ServiceAccountSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    ACCESS_FIELD_NUMBER: builtins.int
    NAMESPACE_SCOPED_ACCESS_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    name: builtins.str
    """The name associated with the service account.
    The name is mutable, but must be unique across all your active service accounts.
    """
    @property
    def access(self) -> global___Access:
        """Note: one of `Access` or `NamespaceScopedAccess` must be provided, but not both.
        The access assigned to the service account.
        If set, creates an account scoped service account.
        The access is mutable.
        """
    @property
    def namespace_scoped_access(self) -> global___NamespaceScopedAccess:
        """The namespace scoped access assigned to the service account.
        If set, creates a namespace scoped service account (limited to a single namespace).
        The namespace scoped access is partially mutable.
        Refer to `NamespaceScopedAccess` for details.
        """
    description: builtins.str
    """The description associated with the service account - optional.
    The description is mutable.
    """
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        access: global___Access | None = ...,
        namespace_scoped_access: global___NamespaceScopedAccess | None = ...,
        description: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "access", b"access", "namespace_scoped_access", b"namespace_scoped_access"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "access",
            b"access",
            "description",
            b"description",
            "name",
            b"name",
            "namespace_scoped_access",
            b"namespace_scoped_access",
        ],
    ) -> None: ...

global___ServiceAccountSpec = ServiceAccountSpec

class ApiKey(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    STATE_DEPRECATED_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    CREATED_TIME_FIELD_NUMBER: builtins.int
    LAST_MODIFIED_TIME_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the API Key."""
    resource_version: builtins.str
    """The current version of the API key specification.
    The next update operation will have to include this version.
    """
    @property
    def spec(self) -> global___ApiKeySpec:
        """The API key specification."""
    state_deprecated: builtins.str
    """The current state of the API key.
    Possible values: activating, activationfailed, active, updating, updatefailed, deleting, deletefailed, deleted, suspending, suspendfailed, suspended.
    For any failed state, reach out to Temporal Cloud support for remediation.
    Deprecated: Not supported after v0.3.0 api version. Use state instead.
    temporal:versioning:max_version=v0.3.0
    """
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the API key.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=state_deprecated
    """
    async_operation_id: builtins.str
    """The id of the async operation that is creating/updating/deleting the API key, if any."""
    @property
    def created_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the API key was created."""
    @property
    def last_modified_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the API key was last modified.
        Will not be set if the API key has never been modified.
        """
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        resource_version: builtins.str = ...,
        spec: global___ApiKeySpec | None = ...,
        state_deprecated: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        created_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        last_modified_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "created_time",
            b"created_time",
            "last_modified_time",
            b"last_modified_time",
            "spec",
            b"spec",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "created_time",
            b"created_time",
            "id",
            b"id",
            "last_modified_time",
            b"last_modified_time",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
            "state_deprecated",
            b"state_deprecated",
        ],
    ) -> None: ...

global___ApiKey = ApiKey

class ApiKeySpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OWNER_ID_FIELD_NUMBER: builtins.int
    OWNER_TYPE_DEPRECATED_FIELD_NUMBER: builtins.int
    OWNER_TYPE_FIELD_NUMBER: builtins.int
    DISPLAY_NAME_FIELD_NUMBER: builtins.int
    DESCRIPTION_FIELD_NUMBER: builtins.int
    EXPIRY_TIME_FIELD_NUMBER: builtins.int
    DISABLED_FIELD_NUMBER: builtins.int
    owner_id: builtins.str
    """The id of the owner to create the API key for.
    The owner id is immutable. Once set during creation, it cannot be changed.
    The owner id is the id of the user when the owner type is user.
    The owner id is the id of the service account when the owner type is service account.
    """
    owner_type_deprecated: builtins.str
    """The type of the owner to create the API key for.
    The owner type is immutable. Once set during creation, it cannot be changed.
    Possible values: user, service-account.
    Deprecated: Not supported after v0.3.0 api version. Use owner_type instead.
    temporal:versioning:max_version=v0.3.0
    """
    owner_type: global___OwnerType.ValueType
    """The type of the owner to create the API key for.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=owner_type_deprecated
    """
    display_name: builtins.str
    """The display name of the API key."""
    description: builtins.str
    """The description of the API key."""
    @property
    def expiry_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The expiry time of the API key."""
    disabled: builtins.bool
    """True if the API key is disabled."""
    def __init__(
        self,
        *,
        owner_id: builtins.str = ...,
        owner_type_deprecated: builtins.str = ...,
        owner_type: global___OwnerType.ValueType = ...,
        display_name: builtins.str = ...,
        description: builtins.str = ...,
        expiry_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        disabled: builtins.bool = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["expiry_time", b"expiry_time"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "description",
            b"description",
            "disabled",
            b"disabled",
            "display_name",
            b"display_name",
            "expiry_time",
            b"expiry_time",
            "owner_id",
            b"owner_id",
            "owner_type",
            b"owner_type",
            "owner_type_deprecated",
            b"owner_type_deprecated",
        ],
    ) -> None: ...

global___ApiKeySpec = ApiKeySpec
