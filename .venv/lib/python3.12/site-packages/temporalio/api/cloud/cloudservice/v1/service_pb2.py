# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/cloud/cloudservice/v1/service.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2

from temporalio.api.cloud.cloudservice.v1 import (
    request_response_pb2 as temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n0temporal/api/cloud/cloudservice/v1/service.proto\x12"temporal.api.cloud.cloudservice.v1\x1a\x39temporal/api/cloud/cloudservice/v1/request_response.proto\x1a\x1cgoogle/api/annotations.proto2\xf2R\n\x0c\x43loudService\x12\x8b\x01\n\x08GetUsers\x12\x33.temporal.api.cloud.cloudservice.v1.GetUsersRequest\x1a\x34.temporal.api.cloud.cloudservice.v1.GetUsersResponse"\x14\x82\xd3\xe4\x93\x02\x0e\x12\x0c/cloud/users\x12\x92\x01\n\x07GetUser\x12\x32.temporal.api.cloud.cloudservice.v1.GetUserRequest\x1a\x33.temporal.api.cloud.cloudservice.v1.GetUserResponse"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/cloud/users/{user_id}\x12\x94\x01\n\nCreateUser\x12\x35.temporal.api.cloud.cloudservice.v1.CreateUserRequest\x1a\x36.temporal.api.cloud.cloudservice.v1.CreateUserResponse"\x17\x82\xd3\xe4\x93\x02\x11"\x0c/cloud/users:\x01*\x12\x9e\x01\n\nUpdateUser\x12\x35.temporal.api.cloud.cloudservice.v1.UpdateUserRequest\x1a\x36.temporal.api.cloud.cloudservice.v1.UpdateUserResponse"!\x82\xd3\xe4\x93\x02\x1b"\x16/cloud/users/{user_id}:\x01*\x12\x9b\x01\n\nDeleteUser\x12\x35.temporal.api.cloud.cloudservice.v1.DeleteUserRequest\x1a\x36.temporal.api.cloud.cloudservice.v1.DeleteUserResponse"\x1e\x82\xd3\xe4\x93\x02\x18*\x16/cloud/users/{user_id}\x12\xe0\x01\n\x16SetUserNamespaceAccess\x12\x41.temporal.api.cloud.cloudservice.v1.SetUserNamespaceAccessRequest\x1a\x42.temporal.api.cloud.cloudservice.v1.SetUserNamespaceAccessResponse"?\x82\xd3\xe4\x93\x02\x39"4/cloud/namespaces/{namespace}/users/{user_id}/access:\x01*\x12\xc0\x01\n\x11GetAsyncOperation\x12<.temporal.api.cloud.cloudservice.v1.GetAsyncOperationRequest\x1a=.temporal.api.cloud.cloudservice.v1.GetAsyncOperationResponse".\x82\xd3\xe4\x93\x02(\x12&/cloud/operations/{async_operation_id}\x12\xa8\x01\n\x0f\x43reateNamespace\x12:.temporal.api.cloud.cloudservice.v1.CreateNamespaceRequest\x1a;.temporal.api.cloud.cloudservice.v1.CreateNamespaceResponse"\x1c\x82\xd3\xe4\x93\x02\x16"\x11/cloud/namespaces:\x01*\x12\x9f\x01\n\rGetNamespaces\x12\x38.temporal.api.cloud.cloudservice.v1.GetNamespacesRequest\x1a\x39.temporal.api.cloud.cloudservice.v1.GetNamespacesResponse"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/cloud/namespaces\x12\xa8\x01\n\x0cGetNamespace\x12\x37.temporal.api.cloud.cloudservice.v1.GetNamespaceRequest\x1a\x38.temporal.api.cloud.cloudservice.v1.GetNamespaceResponse"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/cloud/namespaces/{namespace}\x12\xb4\x01\n\x0fUpdateNamespace\x12:.temporal.api.cloud.cloudservice.v1.UpdateNamespaceRequest\x1a;.temporal.api.cloud.cloudservice.v1.UpdateNamespaceResponse"(\x82\xd3\xe4\x93\x02""\x1d/cloud/namespaces/{namespace}:\x01*\x12\xf7\x01\n\x1bRenameCustomSearchAttribute\x12\x46.temporal.api.cloud.cloudservice.v1.RenameCustomSearchAttributeRequest\x1aG.temporal.api.cloud.cloudservice.v1.RenameCustomSearchAttributeResponse"G\x82\xd3\xe4\x93\x02\x41"</cloud/namespaces/{namespace}/rename-custom-search-attribute:\x01*\x12\xb1\x01\n\x0f\x44\x65leteNamespace\x12:.temporal.api.cloud.cloudservice.v1.DeleteNamespaceRequest\x1a;.temporal.api.cloud.cloudservice.v1.DeleteNamespaceResponse"%\x82\xd3\xe4\x93\x02\x1f*\x1d/cloud/namespaces/{namespace}\x12\xdc\x01\n\x17\x46\x61iloverNamespaceRegion\x12\x42.temporal.api.cloud.cloudservice.v1.FailoverNamespaceRegionRequest\x1a\x43.temporal.api.cloud.cloudservice.v1.FailoverNamespaceRegionResponse"8\x82\xd3\xe4\x93\x02\x32"-/cloud/namespaces/{namespace}/failover-region:\x01*\x12\xc8\x01\n\x12\x41\x64\x64NamespaceRegion\x12=.temporal.api.cloud.cloudservice.v1.AddNamespaceRegionRequest\x1a>.temporal.api.cloud.cloudservice.v1.AddNamespaceRegionResponse"3\x82\xd3\xe4\x93\x02-"(/cloud/namespaces/{namespace}/add-region:\x01*\x12\xd4\x01\n\x15\x44\x65leteNamespaceRegion\<EMAIL>\x1a\x41.temporal.api.cloud.cloudservice.v1.DeleteNamespaceRegionResponse"6\x82\xd3\xe4\x93\x02\x30*./cloud/namespaces/{namespace}/regions/{region}\x12\x93\x01\n\nGetRegions\x12\x35.temporal.api.cloud.cloudservice.v1.GetRegionsRequest\x1a\x36.temporal.api.cloud.cloudservice.v1.GetRegionsResponse"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/cloud/regions\x12\x99\x01\n\tGetRegion\x12\x34.temporal.api.cloud.cloudservice.v1.GetRegionRequest\x1a\x35.temporal.api.cloud.cloudservice.v1.GetRegionResponse"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/cloud/regions/{region}\x12\x94\x01\n\nGetApiKeys\x12\x35.temporal.api.cloud.cloudservice.v1.GetApiKeysRequest\x1a\x36.temporal.api.cloud.cloudservice.v1.GetApiKeysResponse"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/cloud/api-keys\x12\x9a\x01\n\tGetApiKey\x12\x34.temporal.api.cloud.cloudservice.v1.GetApiKeyRequest\x1a\x35.temporal.api.cloud.cloudservice.v1.GetApiKeyResponse" \x82\xd3\xe4\x93\x02\x1a\x12\x18/cloud/api-keys/{key_id}\x12\x9d\x01\n\x0c\x43reateApiKey\x12\x37.temporal.api.cloud.cloudservice.v1.CreateApiKeyRequest\x1a\x38.temporal.api.cloud.cloudservice.v1.CreateApiKeyResponse"\x1a\x82\xd3\xe4\x93\x02\x14"\x0f/cloud/api-keys:\x01*\x12\xa6\x01\n\x0cUpdateApiKey\x12\x37.temporal.api.cloud.cloudservice.v1.UpdateApiKeyRequest\x1a\x38.temporal.api.cloud.cloudservice.v1.UpdateApiKeyResponse"#\x82\xd3\xe4\x93\x02\x1d"\x18/cloud/api-keys/{key_id}:\x01*\x12\xa3\x01\n\x0c\x44\x65leteApiKey\x12\x37.temporal.api.cloud.cloudservice.v1.DeleteApiKeyRequest\x1a\x38.temporal.api.cloud.cloudservice.v1.DeleteApiKeyResponse" \x82\xd3\xe4\x93\x02\x1a*\x18/cloud/api-keys/{key_id}\x12\xb0\x01\n\x11GetNexusEndpoints\x12<.temporal.api.cloud.cloudservice.v1.GetNexusEndpointsRequest\x1a=.temporal.api.cloud.cloudservice.v1.GetNexusEndpointsResponse"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/cloud/nexus/endpoints\x12\xbb\x01\n\x10GetNexusEndpoint\x12;.temporal.api.cloud.cloudservice.v1.GetNexusEndpointRequest\x1a<.temporal.api.cloud.cloudservice.v1.GetNexusEndpointResponse",\x82\xd3\xe4\x93\x02&\x12$/cloud/nexus/endpoints/{endpoint_id}\x12\xb9\x01\n\x13\x43reateNexusEndpoint\x12>.temporal.api.cloud.cloudservice.v1.CreateNexusEndpointRequest\x1a?.temporal.api.cloud.cloudservice.v1.CreateNexusEndpointResponse"!\x82\xd3\xe4\x93\x02\x1b"\x16/cloud/nexus/endpoints:\x01*\x12\xc7\x01\n\x13UpdateNexusEndpoint\x12>.temporal.api.cloud.cloudservice.v1.UpdateNexusEndpointRequest\x1a?.temporal.api.cloud.cloudservice.v1.UpdateNexusEndpointResponse"/\x82\xd3\xe4\x93\x02)"$/cloud/nexus/endpoints/{endpoint_id}:\x01*\x12\xc4\x01\n\x13\x44\x65leteNexusEndpoint\x12>.temporal.api.cloud.cloudservice.v1.DeleteNexusEndpointRequest\x1a?.temporal.api.cloud.cloudservice.v1.DeleteNexusEndpointResponse",\x82\xd3\xe4\x93\x02&*$/cloud/nexus/endpoints/{endpoint_id}\x12\xa0\x01\n\rGetUserGroups\x12\x38.temporal.api.cloud.cloudservice.v1.GetUserGroupsRequest\x1a\x39.temporal.api.cloud.cloudservice.v1.GetUserGroupsResponse"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/cloud/user-groups\x12\xa8\x01\n\x0cGetUserGroup\x12\x37.temporal.api.cloud.cloudservice.v1.GetUserGroupRequest\x1a\x38.temporal.api.cloud.cloudservice.v1.GetUserGroupResponse"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/cloud/user-groups/{group_id}\x12\xa9\x01\n\x0f\x43reateUserGroup\x12:.temporal.api.cloud.cloudservice.v1.CreateUserGroupRequest\x1a;.temporal.api.cloud.cloudservice.v1.CreateUserGroupResponse"\x1d\x82\xd3\xe4\x93\x02\x17"\x12/cloud/user-groups:\x01*\x12\xb4\x01\n\x0fUpdateUserGroup\x12:.temporal.api.cloud.cloudservice.v1.UpdateUserGroupRequest\x1a;.temporal.api.cloud.cloudservice.v1.UpdateUserGroupResponse"(\x82\xd3\xe4\x93\x02""\x1d/cloud/user-groups/{group_id}:\x01*\x12\xb1\x01\n\x0f\x44\x65leteUserGroup\x12:.temporal.api.cloud.cloudservice.v1.DeleteUserGroupRequest\x1a;.temporal.api.cloud.cloudservice.v1.DeleteUserGroupResponse"%\x82\xd3\xe4\x93\x02\x1f*\x1d/cloud/user-groups/{group_id}\x12\xf6\x01\n\x1bSetUserGroupNamespaceAccess\x12\x46.temporal.api.cloud.cloudservice.v1.SetUserGroupNamespaceAccessRequest\x1aG.temporal.api.cloud.cloudservice.v1.SetUserGroupNamespaceAccessResponse"F\x82\xd3\xe4\x93\x02@";/cloud/namespaces/{namespace}/user-groups/{group_id}/access:\x01*\x12\xc5\x01\n\x12\x41\x64\x64UserGroupMember\x12=.temporal.api.cloud.cloudservice.v1.AddUserGroupMemberRequest\x1a>.temporal.api.cloud.cloudservice.v1.AddUserGroupMemberResponse"0\x82\xd3\xe4\x93\x02*"%/cloud/user-groups/{group_id}/members:\x01*\x12\xd4\x01\n\x15RemoveUserGroupMember\<EMAIL>\x1a\x41.temporal.api.cloud.cloudservice.v1.RemoveUserGroupMemberResponse"6\x82\xd3\xe4\x93\x02\x30"+/cloud/user-groups/{group_id}/remove-member:\x01*\x12\xc5\x01\n\x13GetUserGroupMembers\x12>.temporal.api.cloud.cloudservice.v1.GetUserGroupMembersRequest\x1a?.temporal.api.cloud.cloudservice.v1.GetUserGroupMembersResponse"-\x82\xd3\xe4\x93\x02\'\x12%/cloud/user-groups/{group_id}/members\x12\xbd\x01\n\x14\x43reateServiceAccount\x12?.temporal.api.cloud.cloudservice.v1.CreateServiceAccountRequest\<EMAIL>""\x82\xd3\xe4\x93\x02\x1c"\x17/cloud/service-accounts:\x01*\x12\xc6\x01\n\x11GetServiceAccount\x12<.temporal.api.cloud.cloudservice.v1.GetServiceAccountRequest\x1a=.temporal.api.cloud.cloudservice.v1.GetServiceAccountResponse"4\x82\xd3\xe4\x93\x02.\x12,/cloud/service-accounts/{service_account_id}\x12\xb4\x01\n\x12GetServiceAccounts\x12=.temporal.api.cloud.cloudservice.v1.GetServiceAccountsRequest\x1a>.temporal.api.cloud.cloudservice.v1.GetServiceAccountsResponse"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/cloud/service-accounts\x12\xd2\x01\n\x14UpdateServiceAccount\x12?.temporal.api.cloud.cloudservice.v1.UpdateServiceAccountRequest\<EMAIL>"7\x82\xd3\xe4\x93\x02\x31",/cloud/service-accounts/{service_account_id}:\x01*\x12\xcf\x01\n\x14\x44\x65leteServiceAccount\x12?.temporal.api.cloud.cloudservice.v1.DeleteServiceAccountRequest\<EMAIL>"4\x82\xd3\xe4\x93\x02.*,/cloud/service-accounts/{service_account_id}\x12\x8b\x01\n\x08GetUsage\x12\x33.temporal.api.cloud.cloudservice.v1.GetUsageRequest\x1a\x34.temporal.api.cloud.cloudservice.v1.GetUsageResponse"\x14\x82\xd3\xe4\x93\x02\x0e\x12\x0c/cloud/usage\x12\x93\x01\n\nGetAccount\x12\x35.temporal.api.cloud.cloudservice.v1.GetAccountRequest\x1a\x36.temporal.api.cloud.cloudservice.v1.GetAccountResponse"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/cloud/account\x12\x9f\x01\n\rUpdateAccount\x12\x38.temporal.api.cloud.cloudservice.v1.UpdateAccountRequest\x1a\x39.temporal.api.cloud.cloudservice.v1.UpdateAccountResponse"\x19\x82\xd3\xe4\x93\x02\x13"\x0e/cloud/account:\x01*\x12\xdf\x01\n\x19\x43reateNamespaceExportSink\x12\x44.temporal.api.cloud.cloudservice.v1.CreateNamespaceExportSinkRequest\x1a\x45.temporal.api.cloud.cloudservice.v1.CreateNamespaceExportSinkResponse"5\x82\xd3\xe4\x93\x02/"*/cloud/namespaces/{namespace}/export-sinks:\x01*\x12\xda\x01\n\x16GetNamespaceExportSink\x12\x41.temporal.api.cloud.cloudservice.v1.GetNamespaceExportSinkRequest\x1a\x42.temporal.api.cloud.cloudservice.v1.GetNamespaceExportSinkResponse"9\x82\xd3\xe4\x93\x02\x33\x12\x31/cloud/namespaces/{namespace}/export-sinks/{name}\x12\xd6\x01\n\x17GetNamespaceExportSinks\x12\x42.temporal.api.cloud.cloudservice.v1.GetNamespaceExportSinksRequest\x1a\x43.temporal.api.cloud.cloudservice.v1.GetNamespaceExportSinksResponse"2\x82\xd3\xe4\x93\x02,\x12*/cloud/namespaces/{namespace}/export-sinks\x12\xeb\x01\n\x19UpdateNamespaceExportSink\x12\x44.temporal.api.cloud.cloudservice.v1.UpdateNamespaceExportSinkRequest\x1a\x45.temporal.api.cloud.cloudservice.v1.UpdateNamespaceExportSinkResponse"A\x82\xd3\xe4\x93\x02;"6/cloud/namespaces/{namespace}/export-sinks/{spec.name}:\x01*\x12\xe3\x01\n\x19\x44\x65leteNamespaceExportSink\x12\x44.temporal.api.cloud.cloudservice.v1.DeleteNamespaceExportSinkRequest\x1a\x45.temporal.api.cloud.cloudservice.v1.DeleteNamespaceExportSinkResponse"9\x82\xd3\xe4\x93\x02\x33*1/cloud/namespaces/{namespace}/export-sinks/{name}\x12\xee\x01\n\x1bValidateNamespaceExportSink\x12\x46.temporal.api.cloud.cloudservice.v1.ValidateNamespaceExportSinkRequest\x1aG.temporal.api.cloud.cloudservice.v1.ValidateNamespaceExportSinkResponse">\x82\xd3\xe4\x93\x02\x38"3/cloud/namespaces/{namespace}/export-sinks/validate:\x01*\x12\xcc\x01\n\x13UpdateNamespaceTags\x12>.temporal.api.cloud.cloudservice.v1.UpdateNamespaceTagsRequest\x1a?.temporal.api.cloud.cloudservice.v1.UpdateNamespaceTagsResponse"4\x82\xd3\xe4\x93\x02.")/cloud/namespaces/{namespace}/update-tags:\x01*\x12\xc5\x01\n\x16\x43reateConnectivityRule\x12\x41.temporal.api.cloud.cloudservice.v1.CreateConnectivityRuleRequest\x1a\x42.temporal.api.cloud.cloudservice.v1.CreateConnectivityRuleResponse"$\x82\xd3\xe4\x93\x02\x1e"\x19/cloud/connectivity-rules:\x01*\x12\xd0\x01\n\x13GetConnectivityRule\x12>.temporal.api.cloud.cloudservice.v1.GetConnectivityRuleRequest\x1a?.temporal.api.cloud.cloudservice.v1.GetConnectivityRuleResponse"8\x82\xd3\xe4\x93\x02\x32\x12\x30/cloud/connectivity-rules/{connectivity_rule_id}\x12\xbc\x01\n\x14GetConnectivityRules\x12?.temporal.api.cloud.cloudservice.v1.GetConnectivityRulesRequest\<EMAIL>"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/cloud/connectivity-rules\x12\xd9\x01\n\x16\x44\x65leteConnectivityRule\x12\x41.temporal.api.cloud.cloudservice.v1.DeleteConnectivityRuleRequest\x1a\x42.temporal.api.cloud.cloudservice.v1.DeleteConnectivityRuleResponse"8\x82\xd3\xe4\x93\x02\x32*0/cloud/connectivity-rules/{connectivity_rule_id}B\xc0\x01\n%io.temporal.api.cloud.cloudservice.v1B\x0cServiceProtoP\x01Z5go.temporal.io/api/cloud/cloudservice/v1;cloudservice\xaa\x02$Temporalio.Api.Cloud.CloudService.V1\xea\x02(Temporalio::Api::Cloud::CloudService::V1b\x06proto3'
)


_CLOUDSERVICE = DESCRIPTOR.services_by_name["CloudService"]
if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n%io.temporal.api.cloud.cloudservice.v1B\014ServiceProtoP\001Z5go.temporal.io/api/cloud/cloudservice/v1;cloudservice\252\002$Temporalio.Api.Cloud.CloudService.V1\352\002(Temporalio::Api::Cloud::CloudService::V1"
    _CLOUDSERVICE.methods_by_name["GetUsers"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetUsers"
    ]._serialized_options = b"\202\323\344\223\002\016\022\014/cloud/users"
    _CLOUDSERVICE.methods_by_name["GetUser"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetUser"
    ]._serialized_options = b"\202\323\344\223\002\030\022\026/cloud/users/{user_id}"
    _CLOUDSERVICE.methods_by_name["CreateUser"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateUser"
    ]._serialized_options = b'\202\323\344\223\002\021"\014/cloud/users:\001*'
    _CLOUDSERVICE.methods_by_name["UpdateUser"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateUser"
    ]._serialized_options = b'\202\323\344\223\002\033"\026/cloud/users/{user_id}:\001*'
    _CLOUDSERVICE.methods_by_name["DeleteUser"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteUser"
    ]._serialized_options = b"\202\323\344\223\002\030*\026/cloud/users/{user_id}"
    _CLOUDSERVICE.methods_by_name["SetUserNamespaceAccess"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "SetUserNamespaceAccess"
    ]._serialized_options = b'\202\323\344\223\0029"4/cloud/namespaces/{namespace}/users/{user_id}/access:\001*'
    _CLOUDSERVICE.methods_by_name["GetAsyncOperation"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetAsyncOperation"
    ]._serialized_options = (
        b"\202\323\344\223\002(\022&/cloud/operations/{async_operation_id}"
    )
    _CLOUDSERVICE.methods_by_name["CreateNamespace"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateNamespace"
    ]._serialized_options = b'\202\323\344\223\002\026"\021/cloud/namespaces:\001*'
    _CLOUDSERVICE.methods_by_name["GetNamespaces"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetNamespaces"
    ]._serialized_options = b"\202\323\344\223\002\023\022\021/cloud/namespaces"
    _CLOUDSERVICE.methods_by_name["GetNamespace"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetNamespace"
    ]._serialized_options = (
        b"\202\323\344\223\002\037\022\035/cloud/namespaces/{namespace}"
    )
    _CLOUDSERVICE.methods_by_name["UpdateNamespace"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateNamespace"
    ]._serialized_options = (
        b'\202\323\344\223\002""\035/cloud/namespaces/{namespace}:\001*'
    )
    _CLOUDSERVICE.methods_by_name["RenameCustomSearchAttribute"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "RenameCustomSearchAttribute"
    ]._serialized_options = b'\202\323\344\223\002A"</cloud/namespaces/{namespace}/rename-custom-search-attribute:\001*'
    _CLOUDSERVICE.methods_by_name["DeleteNamespace"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteNamespace"
    ]._serialized_options = (
        b"\202\323\344\223\002\037*\035/cloud/namespaces/{namespace}"
    )
    _CLOUDSERVICE.methods_by_name["FailoverNamespaceRegion"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "FailoverNamespaceRegion"
    ]._serialized_options = (
        b'\202\323\344\223\0022"-/cloud/namespaces/{namespace}/failover-region:\001*'
    )
    _CLOUDSERVICE.methods_by_name["AddNamespaceRegion"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "AddNamespaceRegion"
    ]._serialized_options = (
        b'\202\323\344\223\002-"(/cloud/namespaces/{namespace}/add-region:\001*'
    )
    _CLOUDSERVICE.methods_by_name["DeleteNamespaceRegion"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteNamespaceRegion"
    ]._serialized_options = (
        b"\202\323\344\223\0020*./cloud/namespaces/{namespace}/regions/{region}"
    )
    _CLOUDSERVICE.methods_by_name["GetRegions"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetRegions"
    ]._serialized_options = b"\202\323\344\223\002\020\022\016/cloud/regions"
    _CLOUDSERVICE.methods_by_name["GetRegion"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetRegion"
    ]._serialized_options = b"\202\323\344\223\002\031\022\027/cloud/regions/{region}"
    _CLOUDSERVICE.methods_by_name["GetApiKeys"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetApiKeys"
    ]._serialized_options = b"\202\323\344\223\002\021\022\017/cloud/api-keys"
    _CLOUDSERVICE.methods_by_name["GetApiKey"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetApiKey"
    ]._serialized_options = b"\202\323\344\223\002\032\022\030/cloud/api-keys/{key_id}"
    _CLOUDSERVICE.methods_by_name["CreateApiKey"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateApiKey"
    ]._serialized_options = b'\202\323\344\223\002\024"\017/cloud/api-keys:\001*'
    _CLOUDSERVICE.methods_by_name["UpdateApiKey"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateApiKey"
    ]._serialized_options = (
        b'\202\323\344\223\002\035"\030/cloud/api-keys/{key_id}:\001*'
    )
    _CLOUDSERVICE.methods_by_name["DeleteApiKey"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteApiKey"
    ]._serialized_options = b"\202\323\344\223\002\032*\030/cloud/api-keys/{key_id}"
    _CLOUDSERVICE.methods_by_name["GetNexusEndpoints"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetNexusEndpoints"
    ]._serialized_options = b"\202\323\344\223\002\030\022\026/cloud/nexus/endpoints"
    _CLOUDSERVICE.methods_by_name["GetNexusEndpoint"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetNexusEndpoint"
    ]._serialized_options = (
        b"\202\323\344\223\002&\022$/cloud/nexus/endpoints/{endpoint_id}"
    )
    _CLOUDSERVICE.methods_by_name["CreateNexusEndpoint"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateNexusEndpoint"
    ]._serialized_options = b'\202\323\344\223\002\033"\026/cloud/nexus/endpoints:\001*'
    _CLOUDSERVICE.methods_by_name["UpdateNexusEndpoint"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateNexusEndpoint"
    ]._serialized_options = (
        b'\202\323\344\223\002)"$/cloud/nexus/endpoints/{endpoint_id}:\001*'
    )
    _CLOUDSERVICE.methods_by_name["DeleteNexusEndpoint"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteNexusEndpoint"
    ]._serialized_options = (
        b"\202\323\344\223\002&*$/cloud/nexus/endpoints/{endpoint_id}"
    )
    _CLOUDSERVICE.methods_by_name["GetUserGroups"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetUserGroups"
    ]._serialized_options = b"\202\323\344\223\002\024\022\022/cloud/user-groups"
    _CLOUDSERVICE.methods_by_name["GetUserGroup"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetUserGroup"
    ]._serialized_options = (
        b"\202\323\344\223\002\037\022\035/cloud/user-groups/{group_id}"
    )
    _CLOUDSERVICE.methods_by_name["CreateUserGroup"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateUserGroup"
    ]._serialized_options = b'\202\323\344\223\002\027"\022/cloud/user-groups:\001*'
    _CLOUDSERVICE.methods_by_name["UpdateUserGroup"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateUserGroup"
    ]._serialized_options = (
        b'\202\323\344\223\002""\035/cloud/user-groups/{group_id}:\001*'
    )
    _CLOUDSERVICE.methods_by_name["DeleteUserGroup"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteUserGroup"
    ]._serialized_options = (
        b"\202\323\344\223\002\037*\035/cloud/user-groups/{group_id}"
    )
    _CLOUDSERVICE.methods_by_name["SetUserGroupNamespaceAccess"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "SetUserGroupNamespaceAccess"
    ]._serialized_options = b'\202\323\344\223\002@";/cloud/namespaces/{namespace}/user-groups/{group_id}/access:\001*'
    _CLOUDSERVICE.methods_by_name["AddUserGroupMember"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "AddUserGroupMember"
    ]._serialized_options = (
        b'\202\323\344\223\002*"%/cloud/user-groups/{group_id}/members:\001*'
    )
    _CLOUDSERVICE.methods_by_name["RemoveUserGroupMember"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "RemoveUserGroupMember"
    ]._serialized_options = (
        b'\202\323\344\223\0020"+/cloud/user-groups/{group_id}/remove-member:\001*'
    )
    _CLOUDSERVICE.methods_by_name["GetUserGroupMembers"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetUserGroupMembers"
    ]._serialized_options = (
        b"\202\323\344\223\002'\022%/cloud/user-groups/{group_id}/members"
    )
    _CLOUDSERVICE.methods_by_name["CreateServiceAccount"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateServiceAccount"
    ]._serialized_options = (
        b'\202\323\344\223\002\034"\027/cloud/service-accounts:\001*'
    )
    _CLOUDSERVICE.methods_by_name["GetServiceAccount"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetServiceAccount"
    ]._serialized_options = (
        b"\202\323\344\223\002.\022,/cloud/service-accounts/{service_account_id}"
    )
    _CLOUDSERVICE.methods_by_name["GetServiceAccounts"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetServiceAccounts"
    ]._serialized_options = b"\202\323\344\223\002\031\022\027/cloud/service-accounts"
    _CLOUDSERVICE.methods_by_name["UpdateServiceAccount"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateServiceAccount"
    ]._serialized_options = (
        b'\202\323\344\223\0021",/cloud/service-accounts/{service_account_id}:\001*'
    )
    _CLOUDSERVICE.methods_by_name["DeleteServiceAccount"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteServiceAccount"
    ]._serialized_options = (
        b"\202\323\344\223\002.*,/cloud/service-accounts/{service_account_id}"
    )
    _CLOUDSERVICE.methods_by_name["GetUsage"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetUsage"
    ]._serialized_options = b"\202\323\344\223\002\016\022\014/cloud/usage"
    _CLOUDSERVICE.methods_by_name["GetAccount"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetAccount"
    ]._serialized_options = b"\202\323\344\223\002\020\022\016/cloud/account"
    _CLOUDSERVICE.methods_by_name["UpdateAccount"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateAccount"
    ]._serialized_options = b'\202\323\344\223\002\023"\016/cloud/account:\001*'
    _CLOUDSERVICE.methods_by_name["CreateNamespaceExportSink"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateNamespaceExportSink"
    ]._serialized_options = (
        b'\202\323\344\223\002/"*/cloud/namespaces/{namespace}/export-sinks:\001*'
    )
    _CLOUDSERVICE.methods_by_name["GetNamespaceExportSink"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetNamespaceExportSink"
    ]._serialized_options = (
        b"\202\323\344\223\0023\0221/cloud/namespaces/{namespace}/export-sinks/{name}"
    )
    _CLOUDSERVICE.methods_by_name["GetNamespaceExportSinks"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetNamespaceExportSinks"
    ]._serialized_options = (
        b"\202\323\344\223\002,\022*/cloud/namespaces/{namespace}/export-sinks"
    )
    _CLOUDSERVICE.methods_by_name["UpdateNamespaceExportSink"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateNamespaceExportSink"
    ]._serialized_options = b'\202\323\344\223\002;"6/cloud/namespaces/{namespace}/export-sinks/{spec.name}:\001*'
    _CLOUDSERVICE.methods_by_name["DeleteNamespaceExportSink"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteNamespaceExportSink"
    ]._serialized_options = (
        b"\202\323\344\223\0023*1/cloud/namespaces/{namespace}/export-sinks/{name}"
    )
    _CLOUDSERVICE.methods_by_name["ValidateNamespaceExportSink"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "ValidateNamespaceExportSink"
    ]._serialized_options = b'\202\323\344\223\0028"3/cloud/namespaces/{namespace}/export-sinks/validate:\001*'
    _CLOUDSERVICE.methods_by_name["UpdateNamespaceTags"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "UpdateNamespaceTags"
    ]._serialized_options = (
        b'\202\323\344\223\002.")/cloud/namespaces/{namespace}/update-tags:\001*'
    )
    _CLOUDSERVICE.methods_by_name["CreateConnectivityRule"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "CreateConnectivityRule"
    ]._serialized_options = (
        b'\202\323\344\223\002\036"\031/cloud/connectivity-rules:\001*'
    )
    _CLOUDSERVICE.methods_by_name["GetConnectivityRule"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetConnectivityRule"
    ]._serialized_options = (
        b"\202\323\344\223\0022\0220/cloud/connectivity-rules/{connectivity_rule_id}"
    )
    _CLOUDSERVICE.methods_by_name["GetConnectivityRules"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "GetConnectivityRules"
    ]._serialized_options = b"\202\323\344\223\002\033\022\031/cloud/connectivity-rules"
    _CLOUDSERVICE.methods_by_name["DeleteConnectivityRule"]._options = None
    _CLOUDSERVICE.methods_by_name[
        "DeleteConnectivityRule"
    ]._serialized_options = (
        b"\202\323\344\223\0022*0/cloud/connectivity-rules/{connectivity_rule_id}"
    )
    _CLOUDSERVICE._serialized_start = 178
    _CLOUDSERVICE._serialized_end = 10788
# @@protoc_insertion_point(module_scope)
