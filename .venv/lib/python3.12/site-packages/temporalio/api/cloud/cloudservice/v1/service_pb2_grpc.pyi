"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import abc

import grpc

import temporalio.api.cloud.cloudservice.v1.request_response_pb2

class CloudServiceStub:
    """WARNING: This service is currently experimental and may change in
    incompatible ways.
    """

    def __init__(self, channel: grpc.Channel) -> None: ...
    GetUsers: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsersRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsersResponse,
    ]
    """Gets all known users"""
    GetUser: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserResponse,
    ]
    """Get a user"""
    CreateUser: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserResponse,
    ]
    """Create a user"""
    UpdateUser: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserResponse,
    ]
    """Update a user"""
    DeleteUser: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserResponse,
    ]
    """Delete a user"""
    SetUserNamespaceAccess: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserNamespaceAccessRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserNamespaceAccessResponse,
    ]
    """Set a user's access to a namespace"""
    GetAsyncOperation: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAsyncOperationRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAsyncOperationResponse,
    ]
    """Get the latest information on an async operation"""
    CreateNamespace: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceResponse,
    ]
    """Create a new namespace"""
    GetNamespaces: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespacesRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespacesResponse,
    ]
    """Get all namespaces"""
    GetNamespace: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceResponse,
    ]
    """Get a namespace"""
    UpdateNamespace: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceResponse,
    ]
    """Update a namespace"""
    RenameCustomSearchAttribute: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.RenameCustomSearchAttributeRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.RenameCustomSearchAttributeResponse,
    ]
    """Rename an existing customer search attribute"""
    DeleteNamespace: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceResponse,
    ]
    """Delete a namespace"""
    FailoverNamespaceRegion: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.FailoverNamespaceRegionRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.FailoverNamespaceRegionResponse,
    ]
    """Failover a multi-region namespace"""
    AddNamespaceRegion: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddNamespaceRegionRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddNamespaceRegionResponse,
    ]
    """Add a new region to a namespace"""
    DeleteNamespaceRegion: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceRegionRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceRegionResponse,
    ]
    """Delete a region from a namespace"""
    GetRegions: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionsRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionsResponse,
    ]
    """Get all regions"""
    GetRegion: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionResponse,
    ]
    """Get a region"""
    GetApiKeys: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeysRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeysResponse,
    ]
    """Get all known API keys"""
    GetApiKey: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeyRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeyResponse,
    ]
    """Get an API key"""
    CreateApiKey: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateApiKeyRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateApiKeyResponse,
    ]
    """Create an API key"""
    UpdateApiKey: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateApiKeyRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateApiKeyResponse,
    ]
    """Update an API key"""
    DeleteApiKey: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteApiKeyRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteApiKeyResponse,
    ]
    """Delete an API key"""
    GetNexusEndpoints: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointsRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointsResponse,
    ]
    """Gets nexus endpoints"""
    GetNexusEndpoint: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointResponse,
    ]
    """Get a nexus endpoint"""
    CreateNexusEndpoint: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNexusEndpointRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNexusEndpointResponse,
    ]
    """Create a nexus endpoint"""
    UpdateNexusEndpoint: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNexusEndpointRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNexusEndpointResponse,
    ]
    """Update a nexus endpoint"""
    DeleteNexusEndpoint: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNexusEndpointRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNexusEndpointResponse,
    ]
    """Delete a nexus endpoint"""
    GetUserGroups: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupsRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupsResponse,
    ]
    """Get all user groups"""
    GetUserGroup: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupResponse,
    ]
    """Get a user group"""
    CreateUserGroup: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserGroupRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserGroupResponse,
    ]
    """Create new a user group"""
    UpdateUserGroup: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserGroupRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserGroupResponse,
    ]
    """Update a user group"""
    DeleteUserGroup: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserGroupRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserGroupResponse,
    ]
    """Delete a user group"""
    SetUserGroupNamespaceAccess: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserGroupNamespaceAccessRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserGroupNamespaceAccessResponse,
    ]
    """Set a user group's access to a namespace"""
    AddUserGroupMember: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddUserGroupMemberRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddUserGroupMemberResponse,
    ]
    """Add a member to the group, can only be used with Cloud group types."""
    RemoveUserGroupMember: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.RemoveUserGroupMemberRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.RemoveUserGroupMemberResponse,
    ]
    """Remove a member from the group, can only be used with Cloud group types."""
    GetUserGroupMembers: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupMembersRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupMembersResponse,
    ]
    CreateServiceAccount: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateServiceAccountRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateServiceAccountResponse,
    ]
    """Create a service account."""
    GetServiceAccount: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountResponse,
    ]
    """Get a service account."""
    GetServiceAccounts: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountsRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountsResponse,
    ]
    """Get service accounts."""
    UpdateServiceAccount: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateServiceAccountRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateServiceAccountResponse,
    ]
    """Update a service account."""
    DeleteServiceAccount: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteServiceAccountRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteServiceAccountResponse,
    ]
    """Delete a service account."""
    GetUsage: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsageRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsageResponse,
    ]
    """WARNING: Pre-Release Feature
    Get usage data across namespaces
    """
    GetAccount: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAccountRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAccountResponse,
    ]
    """Get account information."""
    UpdateAccount: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateAccountRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateAccountResponse,
    ]
    """Update account information."""
    CreateNamespaceExportSink: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceExportSinkRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceExportSinkResponse,
    ]
    """Create an export sink"""
    GetNamespaceExportSink: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinkRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinkResponse,
    ]
    """Get an export sink"""
    GetNamespaceExportSinks: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinksRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinksResponse,
    ]
    """Get export sinks"""
    UpdateNamespaceExportSink: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceExportSinkRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceExportSinkResponse,
    ]
    """Update an export sink"""
    DeleteNamespaceExportSink: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceExportSinkRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceExportSinkResponse,
    ]
    """Delete an export sink"""
    ValidateNamespaceExportSink: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.ValidateNamespaceExportSinkRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.ValidateNamespaceExportSinkResponse,
    ]
    """Validates an export sink configuration by delivering an empty test file to the specified sink.
    This operation verifies that the sink is correctly configured, accessible, and ready for data export.
    """
    UpdateNamespaceTags: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceTagsRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceTagsResponse,
    ]
    """Update the tags for a namespace"""
    CreateConnectivityRule: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateConnectivityRuleRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateConnectivityRuleResponse,
    ]
    """Creates a connectivity rule"""
    GetConnectivityRule: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRuleRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRuleResponse,
    ]
    """Gets a connectivity rule by id"""
    GetConnectivityRules: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRulesRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRulesResponse,
    ]
    """Lists connectivity rules by account"""
    DeleteConnectivityRule: grpc.UnaryUnaryMultiCallable[
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteConnectivityRuleRequest,
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteConnectivityRuleResponse,
    ]
    """Deletes a connectivity rule by id"""

class CloudServiceServicer(metaclass=abc.ABCMeta):
    """WARNING: This service is currently experimental and may change in
    incompatible ways.
    """

    @abc.abstractmethod
    def GetUsers(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsersRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsersResponse:
        """Gets all known users"""
    @abc.abstractmethod
    def GetUser(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserResponse:
        """Get a user"""
    @abc.abstractmethod
    def CreateUser(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserResponse:
        """Create a user"""
    @abc.abstractmethod
    def UpdateUser(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserResponse:
        """Update a user"""
    @abc.abstractmethod
    def DeleteUser(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserResponse:
        """Delete a user"""
    @abc.abstractmethod
    def SetUserNamespaceAccess(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserNamespaceAccessRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserNamespaceAccessResponse:
        """Set a user's access to a namespace"""
    @abc.abstractmethod
    def GetAsyncOperation(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAsyncOperationRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAsyncOperationResponse:
        """Get the latest information on an async operation"""
    @abc.abstractmethod
    def CreateNamespace(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceResponse:
        """Create a new namespace"""
    @abc.abstractmethod
    def GetNamespaces(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespacesRequest,
        context: grpc.ServicerContext,
    ) -> (
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespacesResponse
    ):
        """Get all namespaces"""
    @abc.abstractmethod
    def GetNamespace(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceResponse:
        """Get a namespace"""
    @abc.abstractmethod
    def UpdateNamespace(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceResponse:
        """Update a namespace"""
    @abc.abstractmethod
    def RenameCustomSearchAttribute(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.RenameCustomSearchAttributeRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.RenameCustomSearchAttributeResponse:
        """Rename an existing customer search attribute"""
    @abc.abstractmethod
    def DeleteNamespace(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceResponse:
        """Delete a namespace"""
    @abc.abstractmethod
    def FailoverNamespaceRegion(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.FailoverNamespaceRegionRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.FailoverNamespaceRegionResponse:
        """Failover a multi-region namespace"""
    @abc.abstractmethod
    def AddNamespaceRegion(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddNamespaceRegionRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddNamespaceRegionResponse:
        """Add a new region to a namespace"""
    @abc.abstractmethod
    def DeleteNamespaceRegion(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceRegionRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceRegionResponse:
        """Delete a region from a namespace"""
    @abc.abstractmethod
    def GetRegions(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionsRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionsResponse:
        """Get all regions"""
    @abc.abstractmethod
    def GetRegion(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetRegionResponse:
        """Get a region"""
    @abc.abstractmethod
    def GetApiKeys(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeysRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeysResponse:
        """Get all known API keys"""
    @abc.abstractmethod
    def GetApiKey(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeyRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetApiKeyResponse:
        """Get an API key"""
    @abc.abstractmethod
    def CreateApiKey(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateApiKeyRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateApiKeyResponse:
        """Create an API key"""
    @abc.abstractmethod
    def UpdateApiKey(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateApiKeyRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateApiKeyResponse:
        """Update an API key"""
    @abc.abstractmethod
    def DeleteApiKey(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteApiKeyRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteApiKeyResponse:
        """Delete an API key"""
    @abc.abstractmethod
    def GetNexusEndpoints(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointsRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointsResponse:
        """Gets nexus endpoints"""
    @abc.abstractmethod
    def GetNexusEndpoint(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNexusEndpointResponse:
        """Get a nexus endpoint"""
    @abc.abstractmethod
    def CreateNexusEndpoint(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNexusEndpointRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNexusEndpointResponse:
        """Create a nexus endpoint"""
    @abc.abstractmethod
    def UpdateNexusEndpoint(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNexusEndpointRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNexusEndpointResponse:
        """Update a nexus endpoint"""
    @abc.abstractmethod
    def DeleteNexusEndpoint(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNexusEndpointRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNexusEndpointResponse:
        """Delete a nexus endpoint"""
    @abc.abstractmethod
    def GetUserGroups(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupsRequest,
        context: grpc.ServicerContext,
    ) -> (
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupsResponse
    ):
        """Get all user groups"""
    @abc.abstractmethod
    def GetUserGroup(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupResponse:
        """Get a user group"""
    @abc.abstractmethod
    def CreateUserGroup(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserGroupRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateUserGroupResponse:
        """Create new a user group"""
    @abc.abstractmethod
    def UpdateUserGroup(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserGroupRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateUserGroupResponse:
        """Update a user group"""
    @abc.abstractmethod
    def DeleteUserGroup(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserGroupRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteUserGroupResponse:
        """Delete a user group"""
    @abc.abstractmethod
    def SetUserGroupNamespaceAccess(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserGroupNamespaceAccessRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.SetUserGroupNamespaceAccessResponse:
        """Set a user group's access to a namespace"""
    @abc.abstractmethod
    def AddUserGroupMember(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddUserGroupMemberRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.AddUserGroupMemberResponse:
        """Add a member to the group, can only be used with Cloud group types."""
    @abc.abstractmethod
    def RemoveUserGroupMember(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.RemoveUserGroupMemberRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.RemoveUserGroupMemberResponse:
        """Remove a member from the group, can only be used with Cloud group types."""
    @abc.abstractmethod
    def GetUserGroupMembers(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupMembersRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUserGroupMembersResponse: ...
    @abc.abstractmethod
    def CreateServiceAccount(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateServiceAccountRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateServiceAccountResponse:
        """Create a service account."""
    @abc.abstractmethod
    def GetServiceAccount(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountResponse:
        """Get a service account."""
    @abc.abstractmethod
    def GetServiceAccounts(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountsRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetServiceAccountsResponse:
        """Get service accounts."""
    @abc.abstractmethod
    def UpdateServiceAccount(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateServiceAccountRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateServiceAccountResponse:
        """Update a service account."""
    @abc.abstractmethod
    def DeleteServiceAccount(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteServiceAccountRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteServiceAccountResponse:
        """Delete a service account."""
    @abc.abstractmethod
    def GetUsage(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsageRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetUsageResponse:
        """WARNING: Pre-Release Feature
        Get usage data across namespaces
        """
    @abc.abstractmethod
    def GetAccount(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAccountRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetAccountResponse:
        """Get account information."""
    @abc.abstractmethod
    def UpdateAccount(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateAccountRequest,
        context: grpc.ServicerContext,
    ) -> (
        temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateAccountResponse
    ):
        """Update account information."""
    @abc.abstractmethod
    def CreateNamespaceExportSink(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceExportSinkRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateNamespaceExportSinkResponse:
        """Create an export sink"""
    @abc.abstractmethod
    def GetNamespaceExportSink(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinkRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinkResponse:
        """Get an export sink"""
    @abc.abstractmethod
    def GetNamespaceExportSinks(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinksRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetNamespaceExportSinksResponse:
        """Get export sinks"""
    @abc.abstractmethod
    def UpdateNamespaceExportSink(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceExportSinkRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceExportSinkResponse:
        """Update an export sink"""
    @abc.abstractmethod
    def DeleteNamespaceExportSink(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceExportSinkRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteNamespaceExportSinkResponse:
        """Delete an export sink"""
    @abc.abstractmethod
    def ValidateNamespaceExportSink(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.ValidateNamespaceExportSinkRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.ValidateNamespaceExportSinkResponse:
        """Validates an export sink configuration by delivering an empty test file to the specified sink.
        This operation verifies that the sink is correctly configured, accessible, and ready for data export.
        """
    @abc.abstractmethod
    def UpdateNamespaceTags(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceTagsRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.UpdateNamespaceTagsResponse:
        """Update the tags for a namespace"""
    @abc.abstractmethod
    def CreateConnectivityRule(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateConnectivityRuleRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.CreateConnectivityRuleResponse:
        """Creates a connectivity rule"""
    @abc.abstractmethod
    def GetConnectivityRule(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRuleRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRuleResponse:
        """Gets a connectivity rule by id"""
    @abc.abstractmethod
    def GetConnectivityRules(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRulesRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.GetConnectivityRulesResponse:
        """Lists connectivity rules by account"""
    @abc.abstractmethod
    def DeleteConnectivityRule(
        self,
        request: temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteConnectivityRuleRequest,
        context: grpc.ServicerContext,
    ) -> temporalio.api.cloud.cloudservice.v1.request_response_pb2.DeleteConnectivityRuleResponse:
        """Deletes a connectivity rule by id"""

def add_CloudServiceServicer_to_server(
    servicer: CloudServiceServicer, server: grpc.Server
) -> None: ...
