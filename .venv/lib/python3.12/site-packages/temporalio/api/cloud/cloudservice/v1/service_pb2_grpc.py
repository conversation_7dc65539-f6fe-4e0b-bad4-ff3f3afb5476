# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""

import grpc

from temporalio.api.cloud.cloudservice.v1 import (
    request_response_pb2 as temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2,
)


class CloudServiceStub(object):
    """WARNING: This service is currently experimental and may change in
    incompatible ways.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetUsers = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUsers",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsersRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsersResponse.FromString,
        )
        self.GetUser = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUser",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserResponse.FromString,
        )
        self.CreateUser = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateUser",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserResponse.FromString,
        )
        self.UpdateUser = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateUser",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserResponse.FromString,
        )
        self.DeleteUser = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteUser",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserResponse.FromString,
        )
        self.SetUserNamespaceAccess = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/SetUserNamespaceAccess",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserNamespaceAccessRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserNamespaceAccessResponse.FromString,
        )
        self.GetAsyncOperation = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetAsyncOperation",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAsyncOperationRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAsyncOperationResponse.FromString,
        )
        self.CreateNamespace = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateNamespace",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceResponse.FromString,
        )
        self.GetNamespaces = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespaces",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespacesRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespacesResponse.FromString,
        )
        self.GetNamespace = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespace",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceResponse.FromString,
        )
        self.UpdateNamespace = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNamespace",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceResponse.FromString,
        )
        self.RenameCustomSearchAttribute = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/RenameCustomSearchAttribute",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RenameCustomSearchAttributeRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RenameCustomSearchAttributeResponse.FromString,
        )
        self.DeleteNamespace = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNamespace",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceResponse.FromString,
        )
        self.FailoverNamespaceRegion = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/FailoverNamespaceRegion",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.FailoverNamespaceRegionRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.FailoverNamespaceRegionResponse.FromString,
        )
        self.AddNamespaceRegion = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/AddNamespaceRegion",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddNamespaceRegionRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddNamespaceRegionResponse.FromString,
        )
        self.DeleteNamespaceRegion = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNamespaceRegion",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRegionRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRegionResponse.FromString,
        )
        self.GetRegions = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetRegions",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionsRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionsResponse.FromString,
        )
        self.GetRegion = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetRegion",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionResponse.FromString,
        )
        self.GetApiKeys = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetApiKeys",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeysRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeysResponse.FromString,
        )
        self.GetApiKey = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetApiKey",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeyRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeyResponse.FromString,
        )
        self.CreateApiKey = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateApiKey",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateApiKeyRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateApiKeyResponse.FromString,
        )
        self.UpdateApiKey = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateApiKey",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateApiKeyRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateApiKeyResponse.FromString,
        )
        self.DeleteApiKey = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteApiKey",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteApiKeyRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteApiKeyResponse.FromString,
        )
        self.GetNexusEndpoints = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNexusEndpoints",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointsRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointsResponse.FromString,
        )
        self.GetNexusEndpoint = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNexusEndpoint",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointResponse.FromString,
        )
        self.CreateNexusEndpoint = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateNexusEndpoint",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNexusEndpointRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNexusEndpointResponse.FromString,
        )
        self.UpdateNexusEndpoint = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNexusEndpoint",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNexusEndpointRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNexusEndpointResponse.FromString,
        )
        self.DeleteNexusEndpoint = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNexusEndpoint",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNexusEndpointRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNexusEndpointResponse.FromString,
        )
        self.GetUserGroups = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUserGroups",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupsRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupsResponse.FromString,
        )
        self.GetUserGroup = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUserGroup",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupResponse.FromString,
        )
        self.CreateUserGroup = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateUserGroup",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserGroupRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserGroupResponse.FromString,
        )
        self.UpdateUserGroup = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateUserGroup",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserGroupRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserGroupResponse.FromString,
        )
        self.DeleteUserGroup = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteUserGroup",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserGroupRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserGroupResponse.FromString,
        )
        self.SetUserGroupNamespaceAccess = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/SetUserGroupNamespaceAccess",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserGroupNamespaceAccessRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserGroupNamespaceAccessResponse.FromString,
        )
        self.AddUserGroupMember = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/AddUserGroupMember",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddUserGroupMemberRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddUserGroupMemberResponse.FromString,
        )
        self.RemoveUserGroupMember = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/RemoveUserGroupMember",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RemoveUserGroupMemberRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RemoveUserGroupMemberResponse.FromString,
        )
        self.GetUserGroupMembers = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUserGroupMembers",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupMembersRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupMembersResponse.FromString,
        )
        self.CreateServiceAccount = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateServiceAccount",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateServiceAccountRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateServiceAccountResponse.FromString,
        )
        self.GetServiceAccount = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetServiceAccount",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountResponse.FromString,
        )
        self.GetServiceAccounts = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetServiceAccounts",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountsRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountsResponse.FromString,
        )
        self.UpdateServiceAccount = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateServiceAccount",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateServiceAccountRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateServiceAccountResponse.FromString,
        )
        self.DeleteServiceAccount = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteServiceAccount",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteServiceAccountRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteServiceAccountResponse.FromString,
        )
        self.GetUsage = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUsage",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsageRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsageResponse.FromString,
        )
        self.GetAccount = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetAccount",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAccountRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAccountResponse.FromString,
        )
        self.UpdateAccount = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateAccount",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateAccountRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateAccountResponse.FromString,
        )
        self.CreateNamespaceExportSink = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateNamespaceExportSink",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceExportSinkRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceExportSinkResponse.FromString,
        )
        self.GetNamespaceExportSink = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespaceExportSink",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinkRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinkResponse.FromString,
        )
        self.GetNamespaceExportSinks = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespaceExportSinks",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinksRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinksResponse.FromString,
        )
        self.UpdateNamespaceExportSink = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNamespaceExportSink",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceExportSinkRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceExportSinkResponse.FromString,
        )
        self.DeleteNamespaceExportSink = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNamespaceExportSink",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceExportSinkRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceExportSinkResponse.FromString,
        )
        self.ValidateNamespaceExportSink = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/ValidateNamespaceExportSink",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.ValidateNamespaceExportSinkRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.ValidateNamespaceExportSinkResponse.FromString,
        )
        self.UpdateNamespaceTags = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNamespaceTags",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceTagsRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceTagsResponse.FromString,
        )
        self.CreateConnectivityRule = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateConnectivityRule",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateConnectivityRuleRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateConnectivityRuleResponse.FromString,
        )
        self.GetConnectivityRule = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetConnectivityRule",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRuleRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRuleResponse.FromString,
        )
        self.GetConnectivityRules = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetConnectivityRules",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRulesRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRulesResponse.FromString,
        )
        self.DeleteConnectivityRule = channel.unary_unary(
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteConnectivityRule",
            request_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteConnectivityRuleRequest.SerializeToString,
            response_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteConnectivityRuleResponse.FromString,
        )


class CloudServiceServicer(object):
    """WARNING: This service is currently experimental and may change in
    incompatible ways.
    """

    def GetUsers(self, request, context):
        """Gets all known users"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetUser(self, request, context):
        """Get a user"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateUser(self, request, context):
        """Create a user"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateUser(self, request, context):
        """Update a user"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteUser(self, request, context):
        """Delete a user"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def SetUserNamespaceAccess(self, request, context):
        """Set a user's access to a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetAsyncOperation(self, request, context):
        """Get the latest information on an async operation"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateNamespace(self, request, context):
        """Create a new namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetNamespaces(self, request, context):
        """Get all namespaces"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetNamespace(self, request, context):
        """Get a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateNamespace(self, request, context):
        """Update a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def RenameCustomSearchAttribute(self, request, context):
        """Rename an existing customer search attribute"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteNamespace(self, request, context):
        """Delete a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def FailoverNamespaceRegion(self, request, context):
        """Failover a multi-region namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def AddNamespaceRegion(self, request, context):
        """Add a new region to a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteNamespaceRegion(self, request, context):
        """Delete a region from a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetRegions(self, request, context):
        """Get all regions"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetRegion(self, request, context):
        """Get a region"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetApiKeys(self, request, context):
        """Get all known API keys"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetApiKey(self, request, context):
        """Get an API key"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateApiKey(self, request, context):
        """Create an API key"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateApiKey(self, request, context):
        """Update an API key"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteApiKey(self, request, context):
        """Delete an API key"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetNexusEndpoints(self, request, context):
        """Gets nexus endpoints"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetNexusEndpoint(self, request, context):
        """Get a nexus endpoint"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateNexusEndpoint(self, request, context):
        """Create a nexus endpoint"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateNexusEndpoint(self, request, context):
        """Update a nexus endpoint"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteNexusEndpoint(self, request, context):
        """Delete a nexus endpoint"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetUserGroups(self, request, context):
        """Get all user groups"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetUserGroup(self, request, context):
        """Get a user group"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateUserGroup(self, request, context):
        """Create new a user group"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateUserGroup(self, request, context):
        """Update a user group"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteUserGroup(self, request, context):
        """Delete a user group"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def SetUserGroupNamespaceAccess(self, request, context):
        """Set a user group's access to a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def AddUserGroupMember(self, request, context):
        """Add a member to the group, can only be used with Cloud group types."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def RemoveUserGroupMember(self, request, context):
        """Remove a member from the group, can only be used with Cloud group types."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetUserGroupMembers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateServiceAccount(self, request, context):
        """Create a service account."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetServiceAccount(self, request, context):
        """Get a service account."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetServiceAccounts(self, request, context):
        """Get service accounts."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateServiceAccount(self, request, context):
        """Update a service account."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteServiceAccount(self, request, context):
        """Delete a service account."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetUsage(self, request, context):
        """WARNING: Pre-Release Feature
        Get usage data across namespaces
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetAccount(self, request, context):
        """Get account information."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateAccount(self, request, context):
        """Update account information."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateNamespaceExportSink(self, request, context):
        """Create an export sink"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetNamespaceExportSink(self, request, context):
        """Get an export sink"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetNamespaceExportSinks(self, request, context):
        """Get export sinks"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateNamespaceExportSink(self, request, context):
        """Update an export sink"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteNamespaceExportSink(self, request, context):
        """Delete an export sink"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def ValidateNamespaceExportSink(self, request, context):
        """Validates an export sink configuration by delivering an empty test file to the specified sink.
        This operation verifies that the sink is correctly configured, accessible, and ready for data export.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def UpdateNamespaceTags(self, request, context):
        """Update the tags for a namespace"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def CreateConnectivityRule(self, request, context):
        """Creates a connectivity rule"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetConnectivityRule(self, request, context):
        """Gets a connectivity rule by id"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def GetConnectivityRules(self, request, context):
        """Lists connectivity rules by account"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")

    def DeleteConnectivityRule(self, request, context):
        """Deletes a connectivity rule by id"""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details("Method not implemented!")
        raise NotImplementedError("Method not implemented!")


def add_CloudServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
        "GetUsers": grpc.unary_unary_rpc_method_handler(
            servicer.GetUsers,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsersRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsersResponse.SerializeToString,
        ),
        "GetUser": grpc.unary_unary_rpc_method_handler(
            servicer.GetUser,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserResponse.SerializeToString,
        ),
        "CreateUser": grpc.unary_unary_rpc_method_handler(
            servicer.CreateUser,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserResponse.SerializeToString,
        ),
        "UpdateUser": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateUser,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserResponse.SerializeToString,
        ),
        "DeleteUser": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteUser,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserResponse.SerializeToString,
        ),
        "SetUserNamespaceAccess": grpc.unary_unary_rpc_method_handler(
            servicer.SetUserNamespaceAccess,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserNamespaceAccessRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserNamespaceAccessResponse.SerializeToString,
        ),
        "GetAsyncOperation": grpc.unary_unary_rpc_method_handler(
            servicer.GetAsyncOperation,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAsyncOperationRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAsyncOperationResponse.SerializeToString,
        ),
        "CreateNamespace": grpc.unary_unary_rpc_method_handler(
            servicer.CreateNamespace,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceResponse.SerializeToString,
        ),
        "GetNamespaces": grpc.unary_unary_rpc_method_handler(
            servicer.GetNamespaces,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespacesRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespacesResponse.SerializeToString,
        ),
        "GetNamespace": grpc.unary_unary_rpc_method_handler(
            servicer.GetNamespace,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceResponse.SerializeToString,
        ),
        "UpdateNamespace": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateNamespace,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceResponse.SerializeToString,
        ),
        "RenameCustomSearchAttribute": grpc.unary_unary_rpc_method_handler(
            servicer.RenameCustomSearchAttribute,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RenameCustomSearchAttributeRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RenameCustomSearchAttributeResponse.SerializeToString,
        ),
        "DeleteNamespace": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteNamespace,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceResponse.SerializeToString,
        ),
        "FailoverNamespaceRegion": grpc.unary_unary_rpc_method_handler(
            servicer.FailoverNamespaceRegion,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.FailoverNamespaceRegionRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.FailoverNamespaceRegionResponse.SerializeToString,
        ),
        "AddNamespaceRegion": grpc.unary_unary_rpc_method_handler(
            servicer.AddNamespaceRegion,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddNamespaceRegionRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddNamespaceRegionResponse.SerializeToString,
        ),
        "DeleteNamespaceRegion": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteNamespaceRegion,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRegionRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRegionResponse.SerializeToString,
        ),
        "GetRegions": grpc.unary_unary_rpc_method_handler(
            servicer.GetRegions,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionsRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionsResponse.SerializeToString,
        ),
        "GetRegion": grpc.unary_unary_rpc_method_handler(
            servicer.GetRegion,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionResponse.SerializeToString,
        ),
        "GetApiKeys": grpc.unary_unary_rpc_method_handler(
            servicer.GetApiKeys,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeysRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeysResponse.SerializeToString,
        ),
        "GetApiKey": grpc.unary_unary_rpc_method_handler(
            servicer.GetApiKey,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeyRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeyResponse.SerializeToString,
        ),
        "CreateApiKey": grpc.unary_unary_rpc_method_handler(
            servicer.CreateApiKey,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateApiKeyRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateApiKeyResponse.SerializeToString,
        ),
        "UpdateApiKey": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateApiKey,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateApiKeyRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateApiKeyResponse.SerializeToString,
        ),
        "DeleteApiKey": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteApiKey,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteApiKeyRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteApiKeyResponse.SerializeToString,
        ),
        "GetNexusEndpoints": grpc.unary_unary_rpc_method_handler(
            servicer.GetNexusEndpoints,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointsRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointsResponse.SerializeToString,
        ),
        "GetNexusEndpoint": grpc.unary_unary_rpc_method_handler(
            servicer.GetNexusEndpoint,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointResponse.SerializeToString,
        ),
        "CreateNexusEndpoint": grpc.unary_unary_rpc_method_handler(
            servicer.CreateNexusEndpoint,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNexusEndpointRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNexusEndpointResponse.SerializeToString,
        ),
        "UpdateNexusEndpoint": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateNexusEndpoint,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNexusEndpointRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNexusEndpointResponse.SerializeToString,
        ),
        "DeleteNexusEndpoint": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteNexusEndpoint,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNexusEndpointRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNexusEndpointResponse.SerializeToString,
        ),
        "GetUserGroups": grpc.unary_unary_rpc_method_handler(
            servicer.GetUserGroups,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupsRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupsResponse.SerializeToString,
        ),
        "GetUserGroup": grpc.unary_unary_rpc_method_handler(
            servicer.GetUserGroup,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupResponse.SerializeToString,
        ),
        "CreateUserGroup": grpc.unary_unary_rpc_method_handler(
            servicer.CreateUserGroup,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserGroupRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserGroupResponse.SerializeToString,
        ),
        "UpdateUserGroup": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateUserGroup,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserGroupRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserGroupResponse.SerializeToString,
        ),
        "DeleteUserGroup": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteUserGroup,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserGroupRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserGroupResponse.SerializeToString,
        ),
        "SetUserGroupNamespaceAccess": grpc.unary_unary_rpc_method_handler(
            servicer.SetUserGroupNamespaceAccess,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserGroupNamespaceAccessRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserGroupNamespaceAccessResponse.SerializeToString,
        ),
        "AddUserGroupMember": grpc.unary_unary_rpc_method_handler(
            servicer.AddUserGroupMember,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddUserGroupMemberRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddUserGroupMemberResponse.SerializeToString,
        ),
        "RemoveUserGroupMember": grpc.unary_unary_rpc_method_handler(
            servicer.RemoveUserGroupMember,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RemoveUserGroupMemberRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RemoveUserGroupMemberResponse.SerializeToString,
        ),
        "GetUserGroupMembers": grpc.unary_unary_rpc_method_handler(
            servicer.GetUserGroupMembers,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupMembersRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupMembersResponse.SerializeToString,
        ),
        "CreateServiceAccount": grpc.unary_unary_rpc_method_handler(
            servicer.CreateServiceAccount,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateServiceAccountRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateServiceAccountResponse.SerializeToString,
        ),
        "GetServiceAccount": grpc.unary_unary_rpc_method_handler(
            servicer.GetServiceAccount,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountResponse.SerializeToString,
        ),
        "GetServiceAccounts": grpc.unary_unary_rpc_method_handler(
            servicer.GetServiceAccounts,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountsRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountsResponse.SerializeToString,
        ),
        "UpdateServiceAccount": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateServiceAccount,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateServiceAccountRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateServiceAccountResponse.SerializeToString,
        ),
        "DeleteServiceAccount": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteServiceAccount,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteServiceAccountRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteServiceAccountResponse.SerializeToString,
        ),
        "GetUsage": grpc.unary_unary_rpc_method_handler(
            servicer.GetUsage,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsageRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsageResponse.SerializeToString,
        ),
        "GetAccount": grpc.unary_unary_rpc_method_handler(
            servicer.GetAccount,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAccountRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAccountResponse.SerializeToString,
        ),
        "UpdateAccount": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateAccount,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateAccountRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateAccountResponse.SerializeToString,
        ),
        "CreateNamespaceExportSink": grpc.unary_unary_rpc_method_handler(
            servicer.CreateNamespaceExportSink,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceExportSinkRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceExportSinkResponse.SerializeToString,
        ),
        "GetNamespaceExportSink": grpc.unary_unary_rpc_method_handler(
            servicer.GetNamespaceExportSink,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinkRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinkResponse.SerializeToString,
        ),
        "GetNamespaceExportSinks": grpc.unary_unary_rpc_method_handler(
            servicer.GetNamespaceExportSinks,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinksRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinksResponse.SerializeToString,
        ),
        "UpdateNamespaceExportSink": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateNamespaceExportSink,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceExportSinkRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceExportSinkResponse.SerializeToString,
        ),
        "DeleteNamespaceExportSink": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteNamespaceExportSink,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceExportSinkRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceExportSinkResponse.SerializeToString,
        ),
        "ValidateNamespaceExportSink": grpc.unary_unary_rpc_method_handler(
            servicer.ValidateNamespaceExportSink,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.ValidateNamespaceExportSinkRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.ValidateNamespaceExportSinkResponse.SerializeToString,
        ),
        "UpdateNamespaceTags": grpc.unary_unary_rpc_method_handler(
            servicer.UpdateNamespaceTags,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceTagsRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceTagsResponse.SerializeToString,
        ),
        "CreateConnectivityRule": grpc.unary_unary_rpc_method_handler(
            servicer.CreateConnectivityRule,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateConnectivityRuleRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateConnectivityRuleResponse.SerializeToString,
        ),
        "GetConnectivityRule": grpc.unary_unary_rpc_method_handler(
            servicer.GetConnectivityRule,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRuleRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRuleResponse.SerializeToString,
        ),
        "GetConnectivityRules": grpc.unary_unary_rpc_method_handler(
            servicer.GetConnectivityRules,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRulesRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRulesResponse.SerializeToString,
        ),
        "DeleteConnectivityRule": grpc.unary_unary_rpc_method_handler(
            servicer.DeleteConnectivityRule,
            request_deserializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteConnectivityRuleRequest.FromString,
            response_serializer=temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteConnectivityRuleResponse.SerializeToString,
        ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
        "temporal.api.cloud.cloudservice.v1.CloudService", rpc_method_handlers
    )
    server.add_generic_rpc_handlers((generic_handler,))


# This class is part of an EXPERIMENTAL API.
class CloudService(object):
    """WARNING: This service is currently experimental and may change in
    incompatible ways.
    """

    @staticmethod
    def GetUsers(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUsers",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsersRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetUser(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUser",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateUser(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateUser",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateUser(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateUser",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteUser(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteUser",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def SetUserNamespaceAccess(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/SetUserNamespaceAccess",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserNamespaceAccessRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserNamespaceAccessResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetAsyncOperation(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetAsyncOperation",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAsyncOperationRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAsyncOperationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateNamespace(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateNamespace",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetNamespaces(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespaces",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespacesRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespacesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetNamespace(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespace",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateNamespace(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNamespace",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def RenameCustomSearchAttribute(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/RenameCustomSearchAttribute",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RenameCustomSearchAttributeRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RenameCustomSearchAttributeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteNamespace(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNamespace",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def FailoverNamespaceRegion(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/FailoverNamespaceRegion",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.FailoverNamespaceRegionRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.FailoverNamespaceRegionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def AddNamespaceRegion(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/AddNamespaceRegion",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddNamespaceRegionRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddNamespaceRegionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteNamespaceRegion(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNamespaceRegion",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRegionRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceRegionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetRegions(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetRegions",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionsRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetRegion(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetRegion",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetRegionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetApiKeys(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetApiKeys",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeysRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeysResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetApiKey(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetApiKey",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeyRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetApiKeyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateApiKey(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateApiKey",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateApiKeyRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateApiKeyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateApiKey(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateApiKey",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateApiKeyRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateApiKeyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteApiKey(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteApiKey",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteApiKeyRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteApiKeyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetNexusEndpoints(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNexusEndpoints",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointsRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetNexusEndpoint(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNexusEndpoint",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNexusEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateNexusEndpoint(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateNexusEndpoint",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNexusEndpointRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNexusEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateNexusEndpoint(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNexusEndpoint",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNexusEndpointRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNexusEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteNexusEndpoint(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNexusEndpoint",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNexusEndpointRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNexusEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetUserGroups(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUserGroups",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupsRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetUserGroup(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUserGroup",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateUserGroup(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateUserGroup",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserGroupRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateUserGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateUserGroup(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateUserGroup",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserGroupRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateUserGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteUserGroup(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteUserGroup",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserGroupRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteUserGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def SetUserGroupNamespaceAccess(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/SetUserGroupNamespaceAccess",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserGroupNamespaceAccessRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.SetUserGroupNamespaceAccessResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def AddUserGroupMember(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/AddUserGroupMember",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddUserGroupMemberRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.AddUserGroupMemberResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def RemoveUserGroupMember(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/RemoveUserGroupMember",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RemoveUserGroupMemberRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.RemoveUserGroupMemberResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetUserGroupMembers(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUserGroupMembers",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupMembersRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUserGroupMembersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateServiceAccount(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateServiceAccount",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateServiceAccountRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetServiceAccount(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetServiceAccount",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetServiceAccounts(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetServiceAccounts",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountsRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetServiceAccountsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateServiceAccount(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateServiceAccount",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateServiceAccountRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteServiceAccount(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteServiceAccount",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteServiceAccountRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetUsage(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetUsage",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsageRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetUsageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetAccount(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetAccount",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAccountRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateAccount(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateAccount",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateAccountRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateNamespaceExportSink(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateNamespaceExportSink",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceExportSinkRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateNamespaceExportSinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetNamespaceExportSink(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespaceExportSink",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinkRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetNamespaceExportSinks(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetNamespaceExportSinks",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinksRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetNamespaceExportSinksResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateNamespaceExportSink(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNamespaceExportSink",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceExportSinkRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceExportSinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteNamespaceExportSink(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteNamespaceExportSink",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceExportSinkRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteNamespaceExportSinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def ValidateNamespaceExportSink(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/ValidateNamespaceExportSink",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.ValidateNamespaceExportSinkRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.ValidateNamespaceExportSinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def UpdateNamespaceTags(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/UpdateNamespaceTags",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceTagsRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.UpdateNamespaceTagsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def CreateConnectivityRule(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/CreateConnectivityRule",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateConnectivityRuleRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.CreateConnectivityRuleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetConnectivityRule(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetConnectivityRule",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRuleRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRuleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def GetConnectivityRules(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/GetConnectivityRules",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRulesRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.GetConnectivityRulesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )

    @staticmethod
    def DeleteConnectivityRule(
        request,
        target,
        options=(),
        channel_credentials=None,
        call_credentials=None,
        insecure=False,
        compression=None,
        wait_for_ready=None,
        timeout=None,
        metadata=None,
    ):
        return grpc.experimental.unary_unary(
            request,
            target,
            "/temporal.api.cloud.cloudservice.v1.CloudService/DeleteConnectivityRule",
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteConnectivityRuleRequest.SerializeToString,
            temporal_dot_api_dot_cloud_dot_cloudservice_dot_v1_dot_request__response__pb2.DeleteConnectivityRuleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
        )
