from .request_response_pb2 import (
    AddNamespaceRegionRequest,
    AddNamespaceRegionResponse,
    AddUserGroupMemberRequest,
    AddUserGroupMemberResponse,
    CreateApiKeyRequest,
    CreateApiKeyResponse,
    CreateConnectivityRuleRequest,
    CreateConnectivityRuleResponse,
    CreateNamespaceExportSinkRequest,
    CreateNamespaceExportSinkResponse,
    CreateNamespaceRequest,
    CreateNamespaceResponse,
    CreateNexusEndpointRequest,
    CreateNexusEndpointResponse,
    CreateServiceAccountRequest,
    CreateServiceAccountResponse,
    CreateUserGroupRequest,
    CreateUserGroupResponse,
    CreateUserRequest,
    CreateUserResponse,
    DeleteApiKeyRequest,
    DeleteApiKeyResponse,
    DeleteConnectivityRuleRequest,
    DeleteConnectivityRuleResponse,
    DeleteNamespaceExportSinkRequest,
    DeleteNamespaceExportSinkResponse,
    DeleteNamespaceRegionRequest,
    DeleteNamespaceRegionResponse,
    DeleteNamespaceRequest,
    DeleteNamespaceResponse,
    DeleteNexusEndpointRequest,
    DeleteNexusEndpointResponse,
    DeleteServiceAccountRequest,
    DeleteServiceAccountResponse,
    DeleteUserGroupRequest,
    DeleteUserGroupResponse,
    DeleteUserRequest,
    DeleteUserResponse,
    FailoverNamespaceRegionRequest,
    FailoverNamespaceRegionResponse,
    GetAccountRequest,
    GetAccountResponse,
    GetApiKeyRequest,
    GetApiKeyResponse,
    GetApiKeysRequest,
    GetApiKeysResponse,
    GetAsyncOperationRequest,
    GetAsyncOperationResponse,
    GetConnectivityRuleRequest,
    GetConnectivityRuleResponse,
    GetConnectivityRulesRequest,
    GetConnectivityRulesResponse,
    GetNamespaceExportSinkRequest,
    GetNamespaceExportSinkResponse,
    GetNamespaceExportSinksRequest,
    GetNamespaceExportSinksResponse,
    GetNamespaceRequest,
    GetNamespaceResponse,
    GetNamespacesRequest,
    GetNamespacesResponse,
    GetNexusEndpointRequest,
    GetNexusEndpointResponse,
    GetNexusEndpointsRequest,
    GetNexusEndpointsResponse,
    GetRegionRequest,
    GetRegionResponse,
    GetRegionsRequest,
    GetRegionsResponse,
    GetServiceAccountRequest,
    GetServiceAccountResponse,
    GetServiceAccountsRequest,
    GetServiceAccountsResponse,
    GetUsageRequest,
    GetUsageResponse,
    GetUserGroupMembersRequest,
    GetUserGroupMembersResponse,
    GetUserGroupRequest,
    GetUserGroupResponse,
    GetUserGroupsRequest,
    GetUserGroupsResponse,
    GetUserRequest,
    GetUserResponse,
    GetUsersRequest,
    GetUsersResponse,
    RemoveUserGroupMemberRequest,
    RemoveUserGroupMemberResponse,
    RenameCustomSearchAttributeRequest,
    RenameCustomSearchAttributeResponse,
    SetUserGroupNamespaceAccessRequest,
    SetUserGroupNamespaceAccessResponse,
    SetUserNamespaceAccessRequest,
    SetUserNamespaceAccessResponse,
    UpdateAccountRequest,
    UpdateAccountResponse,
    UpdateApiKeyRequest,
    UpdateApiKeyResponse,
    UpdateNamespaceExportSinkRequest,
    UpdateNamespaceExportSinkResponse,
    UpdateNamespaceRequest,
    UpdateNamespaceResponse,
    UpdateNamespaceTagsRequest,
    UpdateNamespaceTagsResponse,
    UpdateNexusEndpointRequest,
    UpdateNexusEndpointResponse,
    UpdateServiceAccountRequest,
    UpdateServiceAccountResponse,
    UpdateUserGroupRequest,
    UpdateUserGroupResponse,
    UpdateUserRequest,
    UpdateUserResponse,
    ValidateNamespaceExportSinkRequest,
    ValidateNamespaceExportSinkResponse,
)

__all__ = [
    "AddNamespaceRegionRequest",
    "AddNamespaceRegionResponse",
    "AddUserGroupMemberRequest",
    "AddUserGroupMemberResponse",
    "CreateApiKeyRequest",
    "CreateApiKeyResponse",
    "CreateConnectivityRuleRequest",
    "CreateConnectivityRuleResponse",
    "CreateNamespaceExportSinkRequest",
    "CreateNamespaceExportSinkResponse",
    "CreateNamespaceRequest",
    "CreateNamespaceResponse",
    "CreateNexusEndpointRequest",
    "CreateNexusEndpointResponse",
    "CreateServiceAccountRequest",
    "CreateServiceAccountResponse",
    "CreateUserGroupRequest",
    "CreateUserGroupResponse",
    "CreateUserRequest",
    "CreateUserResponse",
    "DeleteApiKeyRequest",
    "DeleteApiKeyResponse",
    "DeleteConnectivityRuleRequest",
    "DeleteConnectivityRuleResponse",
    "DeleteNamespaceExportSinkRequest",
    "DeleteNamespaceExportSinkResponse",
    "DeleteNamespaceRegionRequest",
    "DeleteNamespaceRegionResponse",
    "DeleteNamespaceRequest",
    "DeleteNamespaceResponse",
    "DeleteNexusEndpointRequest",
    "DeleteNexusEndpointResponse",
    "DeleteServiceAccountRequest",
    "DeleteServiceAccountResponse",
    "DeleteUserGroupRequest",
    "DeleteUserGroupResponse",
    "DeleteUserRequest",
    "DeleteUserResponse",
    "FailoverNamespaceRegionRequest",
    "FailoverNamespaceRegionResponse",
    "GetAccountRequest",
    "GetAccountResponse",
    "GetApiKeyRequest",
    "GetApiKeyResponse",
    "GetApiKeysRequest",
    "GetApiKeysResponse",
    "GetAsyncOperationRequest",
    "GetAsyncOperationResponse",
    "GetConnectivityRuleRequest",
    "GetConnectivityRuleResponse",
    "GetConnectivityRulesRequest",
    "GetConnectivityRulesResponse",
    "GetNamespaceExportSinkRequest",
    "GetNamespaceExportSinkResponse",
    "GetNamespaceExportSinksRequest",
    "GetNamespaceExportSinksResponse",
    "GetNamespaceRequest",
    "GetNamespaceResponse",
    "GetNamespacesRequest",
    "GetNamespacesResponse",
    "GetNexusEndpointRequest",
    "GetNexusEndpointResponse",
    "GetNexusEndpointsRequest",
    "GetNexusEndpointsResponse",
    "GetRegionRequest",
    "GetRegionResponse",
    "GetRegionsRequest",
    "GetRegionsResponse",
    "GetServiceAccountRequest",
    "GetServiceAccountResponse",
    "GetServiceAccountsRequest",
    "GetServiceAccountsResponse",
    "GetUsageRequest",
    "GetUsageResponse",
    "GetUserGroupMembersRequest",
    "GetUserGroupMembersResponse",
    "GetUserGroupRequest",
    "GetUserGroupResponse",
    "GetUserGroupsRequest",
    "GetUserGroupsResponse",
    "GetUserRequest",
    "GetUserResponse",
    "GetUsersRequest",
    "GetUsersResponse",
    "RemoveUserGroupMemberRequest",
    "RemoveUserGroupMemberResponse",
    "RenameCustomSearchAttributeRequest",
    "RenameCustomSearchAttributeResponse",
    "SetUserGroupNamespaceAccessRequest",
    "SetUserGroupNamespaceAccessResponse",
    "SetUserNamespaceAccessRequest",
    "SetUserNamespaceAccessResponse",
    "UpdateAccountRequest",
    "UpdateAccountResponse",
    "UpdateApiKeyRequest",
    "UpdateApiKeyResponse",
    "UpdateNamespaceExportSinkRequest",
    "UpdateNamespaceExportSinkResponse",
    "UpdateNamespaceRequest",
    "UpdateNamespaceResponse",
    "UpdateNamespaceTagsRequest",
    "UpdateNamespaceTagsResponse",
    "UpdateNexusEndpointRequest",
    "UpdateNexusEndpointResponse",
    "UpdateServiceAccountRequest",
    "UpdateServiceAccountResponse",
    "UpdateUserGroupRequest",
    "UpdateUserGroupResponse",
    "UpdateUserRequest",
    "UpdateUserResponse",
    "ValidateNamespaceExportSinkRequest",
    "ValidateNamespaceExportSinkResponse",
]

# gRPC is optional
try:
    import grpc

    from .service_pb2_grpc import (
        CloudServiceServicer,
        CloudServiceStub,
        add_CloudServiceServicer_to_server,
    )

    __all__.extend(
        [
            "CloudServiceServicer",
            "CloudServiceStub",
            "add_CloudServiceServicer_to_server",
        ]
    )
except ImportError:
    pass
