"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys

import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import google.protobuf.timestamp_pb2

import temporalio.api.cloud.account.v1.message_pb2
import temporalio.api.cloud.connectivityrule.v1.message_pb2
import temporalio.api.cloud.identity.v1.message_pb2
import temporalio.api.cloud.namespace.v1.message_pb2
import temporalio.api.cloud.nexus.v1.message_pb2
import temporalio.api.cloud.operation.v1.message_pb2
import temporalio.api.cloud.region.v1.message_pb2
import temporalio.api.cloud.usage.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class GetUsersRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    EMAIL_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve - optional.
    Cannot exceed 1000. Defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    email: builtins.str
    """Filter users by email address - optional."""
    namespace: builtins.str
    """Filter users by the namespace they have access to - optional."""
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
        email: builtins.str = ...,
        namespace: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "email",
            b"email",
            "namespace",
            b"namespace",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
        ],
    ) -> None: ...

global___GetUsersRequest = GetUsersRequest

class GetUsersResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USERS_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def users(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.identity.v1.message_pb2.User
    ]:
        """The list of users in ascending ids order"""
    next_page_token: builtins.str
    """The next page's token"""
    def __init__(
        self,
        *,
        users: collections.abc.Iterable[
            temporalio.api.cloud.identity.v1.message_pb2.User
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "next_page_token", b"next_page_token", "users", b"users"
        ],
    ) -> None: ...

global___GetUsersResponse = GetUsersResponse

class GetUserRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_ID_FIELD_NUMBER: builtins.int
    user_id: builtins.str
    """The id of the user to get"""
    def __init__(
        self,
        *,
        user_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["user_id", b"user_id"]
    ) -> None: ...

global___GetUserRequest = GetUserRequest

class GetUserResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_FIELD_NUMBER: builtins.int
    @property
    def user(self) -> temporalio.api.cloud.identity.v1.message_pb2.User:
        """The user"""
    def __init__(
        self,
        *,
        user: temporalio.api.cloud.identity.v1.message_pb2.User | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["user", b"user"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["user", b"user"]
    ) -> None: ...

global___GetUserResponse = GetUserResponse

class CreateUserRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.UserSpec:
        """The spec for the user to invite"""
    async_operation_id: builtins.str
    """The id to use for this async operation - optional"""
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.identity.v1.message_pb2.UserSpec | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id", b"async_operation_id", "spec", b"spec"
        ],
    ) -> None: ...

global___CreateUserRequest = CreateUserRequest

class CreateUserResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_ID_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    user_id: builtins.str
    """The id of the user that was invited"""
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        user_id: builtins.str = ...,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation", b"async_operation", "user_id", b"user_id"
        ],
    ) -> None: ...

global___CreateUserResponse = CreateUserResponse

class UpdateUserRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    user_id: builtins.str
    """The id of the user to update"""
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.UserSpec:
        """The new user specification"""
    resource_version: builtins.str
    """The version of the user for which this update is intended for
    The latest version can be found in the GetUser operation response
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional"""
    def __init__(
        self,
        *,
        user_id: builtins.str = ...,
        spec: temporalio.api.cloud.identity.v1.message_pb2.UserSpec | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "user_id",
            b"user_id",
        ],
    ) -> None: ...

global___UpdateUserRequest = UpdateUserRequest

class UpdateUserResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateUserResponse = UpdateUserResponse

class DeleteUserRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    USER_ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    user_id: builtins.str
    """The id of the user to delete"""
    resource_version: builtins.str
    """The version of the user for which this delete is intended for
    The latest version can be found in the GetUser operation response
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional"""
    def __init__(
        self,
        *,
        user_id: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "resource_version",
            b"resource_version",
            "user_id",
            b"user_id",
        ],
    ) -> None: ...

global___DeleteUserRequest = DeleteUserRequest

class DeleteUserResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteUserResponse = DeleteUserResponse

class SetUserNamespaceAccessRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    USER_ID_FIELD_NUMBER: builtins.int
    ACCESS_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to set permissions for"""
    user_id: builtins.str
    """The id of the user to set permissions for"""
    @property
    def access(self) -> temporalio.api.cloud.identity.v1.message_pb2.NamespaceAccess:
        """The namespace access to assign the user"""
    resource_version: builtins.str
    """The version of the user for which this update is intended for
    The latest version can be found in the GetUser operation response
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional"""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        user_id: builtins.str = ...,
        access: temporalio.api.cloud.identity.v1.message_pb2.NamespaceAccess
        | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["access", b"access"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "access",
            b"access",
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "resource_version",
            b"resource_version",
            "user_id",
            b"user_id",
        ],
    ) -> None: ...

global___SetUserNamespaceAccessRequest = SetUserNamespaceAccessRequest

class SetUserNamespaceAccessResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___SetUserNamespaceAccessResponse = SetUserNamespaceAccessResponse

class GetAsyncOperationRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    async_operation_id: builtins.str
    """The id of the async operation to get"""
    def __init__(
        self,
        *,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id", b"async_operation_id"
        ],
    ) -> None: ...

global___GetAsyncOperationRequest = GetAsyncOperationRequest

class GetAsyncOperationResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___GetAsyncOperationResponse = GetAsyncOperationResponse

class CreateNamespaceRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class TagsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    TAGS_FIELD_NUMBER: builtins.int
    @property
    def spec(self) -> temporalio.api.cloud.namespace.v1.message_pb2.NamespaceSpec:
        """The namespace specification."""
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    @property
    def tags(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """The tags to add to the namespace.
        Note: This field can be set by global admins or account owners only.
        """
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.namespace.v1.message_pb2.NamespaceSpec | None = ...,
        async_operation_id: builtins.str = ...,
        tags: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "spec",
            b"spec",
            "tags",
            b"tags",
        ],
    ) -> None: ...

global___CreateNamespaceRequest = CreateNamespaceRequest

class CreateNamespaceResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace that was created."""
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation", b"async_operation", "namespace", b"namespace"
        ],
    ) -> None: ...

global___CreateNamespaceResponse = CreateNamespaceResponse

class GetNamespacesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve.
    Cannot exceed 1000. 
    Optional, defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response.
    Optional, defaults to empty.
    """
    name: builtins.str
    """Filter namespaces by their name.
    Optional, defaults to empty.
    """
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "name", b"name", "page_size", b"page_size", "page_token", b"page_token"
        ],
    ) -> None: ...

global___GetNamespacesRequest = GetNamespacesRequest

class GetNamespacesResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACES_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def namespaces(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.namespace.v1.message_pb2.Namespace
    ]:
        """The list of namespaces in ascending name order."""
    next_page_token: builtins.str
    """The next page's token."""
    def __init__(
        self,
        *,
        namespaces: collections.abc.Iterable[
            temporalio.api.cloud.namespace.v1.message_pb2.Namespace
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "namespaces", b"namespaces", "next_page_token", b"next_page_token"
        ],
    ) -> None: ...

global___GetNamespacesResponse = GetNamespacesResponse

class GetNamespaceRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to get."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["namespace", b"namespace"]
    ) -> None: ...

global___GetNamespaceRequest = GetNamespaceRequest

class GetNamespaceResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    @property
    def namespace(self) -> temporalio.api.cloud.namespace.v1.message_pb2.Namespace:
        """The namespace."""
    def __init__(
        self,
        *,
        namespace: temporalio.api.cloud.namespace.v1.message_pb2.Namespace | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["namespace", b"namespace"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["namespace", b"namespace"]
    ) -> None: ...

global___GetNamespaceResponse = GetNamespaceResponse

class UpdateNamespaceRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to update."""
    @property
    def spec(self) -> temporalio.api.cloud.namespace.v1.message_pb2.NamespaceSpec:
        """The new namespace specification."""
    resource_version: builtins.str
    """The version of the namespace for which this update is intended for.
    The latest version can be found in the namespace status.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        spec: temporalio.api.cloud.namespace.v1.message_pb2.NamespaceSpec | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___UpdateNamespaceRequest = UpdateNamespaceRequest

class UpdateNamespaceResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateNamespaceResponse = UpdateNamespaceResponse

class RenameCustomSearchAttributeRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    EXISTING_CUSTOM_SEARCH_ATTRIBUTE_NAME_FIELD_NUMBER: builtins.int
    NEW_CUSTOM_SEARCH_ATTRIBUTE_NAME_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to rename the custom search attribute for."""
    existing_custom_search_attribute_name: builtins.str
    """The existing name of the custom search attribute to be renamed."""
    new_custom_search_attribute_name: builtins.str
    """The new name of the custom search attribute."""
    resource_version: builtins.str
    """The version of the namespace for which this update is intended for.
    The latest version can be found in the namespace status.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        existing_custom_search_attribute_name: builtins.str = ...,
        new_custom_search_attribute_name: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "existing_custom_search_attribute_name",
            b"existing_custom_search_attribute_name",
            "namespace",
            b"namespace",
            "new_custom_search_attribute_name",
            b"new_custom_search_attribute_name",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___RenameCustomSearchAttributeRequest = RenameCustomSearchAttributeRequest

class RenameCustomSearchAttributeResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___RenameCustomSearchAttributeResponse = RenameCustomSearchAttributeResponse

class DeleteNamespaceRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to delete."""
    resource_version: builtins.str
    """The version of the namespace for which this delete is intended for.
    The latest version can be found in the namespace status.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___DeleteNamespaceRequest = DeleteNamespaceRequest

class DeleteNamespaceResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteNamespaceResponse = DeleteNamespaceResponse

class FailoverNamespaceRegionRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    REGION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to failover."""
    region: builtins.str
    """The id of the region to failover to.
    Must be a region that the namespace is currently available in.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        region: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "region",
            b"region",
        ],
    ) -> None: ...

global___FailoverNamespaceRegionRequest = FailoverNamespaceRegionRequest

class FailoverNamespaceRegionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___FailoverNamespaceRegionResponse = FailoverNamespaceRegionResponse

class AddNamespaceRegionRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    REGION_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to add the region to."""
    region: builtins.str
    """The id of the standby region to add to the namespace.
    The GetRegions API can be used to get the list of valid region ids.
    Example: "aws-us-west-2".
    """
    resource_version: builtins.str
    """The version of the namespace for which this add region operation is intended for.
    The latest version can be found in the GetNamespace operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        region: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "region",
            b"region",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___AddNamespaceRegionRequest = AddNamespaceRegionRequest

class AddNamespaceRegionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___AddNamespaceRegionResponse = AddNamespaceRegionResponse

class DeleteNamespaceRegionRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    REGION_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to delete a region."""
    region: builtins.str
    """The id of the standby region to be deleted.
    The GetRegions API can be used to get the list of valid region ids.
    Example: "aws-us-west-2".
    """
    resource_version: builtins.str
    """The version of the namespace for which this delete region operation is intended for.
    The latest version can be found in the GetNamespace operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        region: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "region",
            b"region",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___DeleteNamespaceRegionRequest = DeleteNamespaceRegionRequest

class DeleteNamespaceRegionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteNamespaceRegionResponse = DeleteNamespaceRegionResponse

class GetRegionsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___GetRegionsRequest = GetRegionsRequest

class GetRegionsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REGIONS_FIELD_NUMBER: builtins.int
    @property
    def regions(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.region.v1.message_pb2.Region
    ]:
        """The temporal cloud regions."""
    def __init__(
        self,
        *,
        regions: collections.abc.Iterable[
            temporalio.api.cloud.region.v1.message_pb2.Region
        ]
        | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["regions", b"regions"]
    ) -> None: ...

global___GetRegionsResponse = GetRegionsResponse

class GetRegionRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REGION_FIELD_NUMBER: builtins.int
    region: builtins.str
    """The id of the region to get."""
    def __init__(
        self,
        *,
        region: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["region", b"region"]
    ) -> None: ...

global___GetRegionRequest = GetRegionRequest

class GetRegionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REGION_FIELD_NUMBER: builtins.int
    @property
    def region(self) -> temporalio.api.cloud.region.v1.message_pb2.Region:
        """The temporal cloud region."""
    def __init__(
        self,
        *,
        region: temporalio.api.cloud.region.v1.message_pb2.Region | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["region", b"region"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["region", b"region"]
    ) -> None: ...

global___GetRegionResponse = GetRegionResponse

class GetApiKeysRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    OWNER_ID_FIELD_NUMBER: builtins.int
    OWNER_TYPE_DEPRECATED_FIELD_NUMBER: builtins.int
    OWNER_TYPE_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve - optional.
    Cannot exceed 1000. Defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    owner_id: builtins.str
    """Filter api keys by owner id - optional."""
    owner_type_deprecated: builtins.str
    """Filter api keys by owner type - optional.
    Possible values: user, service-account
    """
    owner_type: temporalio.api.cloud.identity.v1.message_pb2.OwnerType.ValueType
    """Filter api keys by owner type - optional.
    temporal:enums:replaces=owner_type_deprecated
    """
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
        owner_id: builtins.str = ...,
        owner_type_deprecated: builtins.str = ...,
        owner_type: temporalio.api.cloud.identity.v1.message_pb2.OwnerType.ValueType = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "owner_id",
            b"owner_id",
            "owner_type",
            b"owner_type",
            "owner_type_deprecated",
            b"owner_type_deprecated",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
        ],
    ) -> None: ...

global___GetApiKeysRequest = GetApiKeysRequest

class GetApiKeysResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    API_KEYS_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def api_keys(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.identity.v1.message_pb2.ApiKey
    ]:
        """The list of api keys in ascending id order."""
    next_page_token: builtins.str
    """The next page's token."""
    def __init__(
        self,
        *,
        api_keys: collections.abc.Iterable[
            temporalio.api.cloud.identity.v1.message_pb2.ApiKey
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "api_keys", b"api_keys", "next_page_token", b"next_page_token"
        ],
    ) -> None: ...

global___GetApiKeysResponse = GetApiKeysResponse

class GetApiKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_ID_FIELD_NUMBER: builtins.int
    key_id: builtins.str
    """The id of the api key to get."""
    def __init__(
        self,
        *,
        key_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["key_id", b"key_id"]
    ) -> None: ...

global___GetApiKeyRequest = GetApiKeyRequest

class GetApiKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    API_KEY_FIELD_NUMBER: builtins.int
    @property
    def api_key(self) -> temporalio.api.cloud.identity.v1.message_pb2.ApiKey:
        """The api key."""
    def __init__(
        self,
        *,
        api_key: temporalio.api.cloud.identity.v1.message_pb2.ApiKey | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["api_key", b"api_key"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["api_key", b"api_key"]
    ) -> None: ...

global___GetApiKeyResponse = GetApiKeyResponse

class CreateApiKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.ApiKeySpec:
        """The spec for the api key to create.
        Create api key only supports service-account owner type for now.
        """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.identity.v1.message_pb2.ApiKeySpec | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id", b"async_operation_id", "spec", b"spec"
        ],
    ) -> None: ...

global___CreateApiKeyRequest = CreateApiKeyRequest

class CreateApiKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_ID_FIELD_NUMBER: builtins.int
    TOKEN_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    key_id: builtins.str
    """The id of the api key created."""
    token: builtins.str
    """The token of the api key created.
    This is a secret and should be stored securely.
    It will not be retrievable after this response.
    """
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        key_id: builtins.str = ...,
        token: builtins.str = ...,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation",
            b"async_operation",
            "key_id",
            b"key_id",
            "token",
            b"token",
        ],
    ) -> None: ...

global___CreateApiKeyResponse = CreateApiKeyResponse

class UpdateApiKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    key_id: builtins.str
    """The id of the api key to update."""
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.ApiKeySpec:
        """The new api key specification."""
    resource_version: builtins.str
    """The version of the api key for which this update is intended for.
    The latest version can be found in the GetApiKey operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        key_id: builtins.str = ...,
        spec: temporalio.api.cloud.identity.v1.message_pb2.ApiKeySpec | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "key_id",
            b"key_id",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___UpdateApiKeyRequest = UpdateApiKeyRequest

class UpdateApiKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateApiKeyResponse = UpdateApiKeyResponse

class DeleteApiKeyRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    key_id: builtins.str
    """The id of the api key to delete."""
    resource_version: builtins.str
    """The version of the api key for which this delete is intended for.
    The latest version can be found in the GetApiKey operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        key_id: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "key_id",
            b"key_id",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___DeleteApiKeyRequest = DeleteApiKeyRequest

class DeleteApiKeyResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteApiKeyResponse = DeleteApiKeyResponse

class GetNexusEndpointsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    TARGET_NAMESPACE_ID_FIELD_NUMBER: builtins.int
    TARGET_TASK_QUEUE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve - optional.
    Cannot exceed 1000. Defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    target_namespace_id: builtins.str
    """optional, treated as an AND if specified"""
    target_task_queue: builtins.str
    """optional, treated as an AND if specified"""
    name: builtins.str
    """Filter endpoints by their name - optional, treated as an AND if specified. Specifying this will result in zero or one results."""
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
        target_namespace_id: builtins.str = ...,
        target_task_queue: builtins.str = ...,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "name",
            b"name",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
            "target_namespace_id",
            b"target_namespace_id",
            "target_task_queue",
            b"target_task_queue",
        ],
    ) -> None: ...

global___GetNexusEndpointsRequest = GetNexusEndpointsRequest

class GetNexusEndpointsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENDPOINTS_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def endpoints(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.nexus.v1.message_pb2.Endpoint
    ]:
        """The list of endpoints in ascending id order."""
    next_page_token: builtins.str
    """The next page's token."""
    def __init__(
        self,
        *,
        endpoints: collections.abc.Iterable[
            temporalio.api.cloud.nexus.v1.message_pb2.Endpoint
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "endpoints", b"endpoints", "next_page_token", b"next_page_token"
        ],
    ) -> None: ...

global___GetNexusEndpointsResponse = GetNexusEndpointsResponse

class GetNexusEndpointRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENDPOINT_ID_FIELD_NUMBER: builtins.int
    endpoint_id: builtins.str
    """The id of the nexus endpoint to get."""
    def __init__(
        self,
        *,
        endpoint_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["endpoint_id", b"endpoint_id"]
    ) -> None: ...

global___GetNexusEndpointRequest = GetNexusEndpointRequest

class GetNexusEndpointResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENDPOINT_FIELD_NUMBER: builtins.int
    @property
    def endpoint(self) -> temporalio.api.cloud.nexus.v1.message_pb2.Endpoint:
        """The nexus endpoint."""
    def __init__(
        self,
        *,
        endpoint: temporalio.api.cloud.nexus.v1.message_pb2.Endpoint | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["endpoint", b"endpoint"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["endpoint", b"endpoint"]
    ) -> None: ...

global___GetNexusEndpointResponse = GetNexusEndpointResponse

class CreateNexusEndpointRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    @property
    def spec(self) -> temporalio.api.cloud.nexus.v1.message_pb2.EndpointSpec:
        """The spec for the nexus endpoint."""
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.nexus.v1.message_pb2.EndpointSpec | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id", b"async_operation_id", "spec", b"spec"
        ],
    ) -> None: ...

global___CreateNexusEndpointRequest = CreateNexusEndpointRequest

class CreateNexusEndpointResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENDPOINT_ID_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    endpoint_id: builtins.str
    """The id of the endpoint that was created."""
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        endpoint_id: builtins.str = ...,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation", b"async_operation", "endpoint_id", b"endpoint_id"
        ],
    ) -> None: ...

global___CreateNexusEndpointResponse = CreateNexusEndpointResponse

class UpdateNexusEndpointRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENDPOINT_ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    endpoint_id: builtins.str
    """The id of the nexus endpoint to update."""
    @property
    def spec(self) -> temporalio.api.cloud.nexus.v1.message_pb2.EndpointSpec:
        """The updated nexus endpoint specification."""
    resource_version: builtins.str
    """The version of the nexus endpoint for which this update is intended for.
    The latest version can be found in the GetNexusEndpoint operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        endpoint_id: builtins.str = ...,
        spec: temporalio.api.cloud.nexus.v1.message_pb2.EndpointSpec | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "endpoint_id",
            b"endpoint_id",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___UpdateNexusEndpointRequest = UpdateNexusEndpointRequest

class UpdateNexusEndpointResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateNexusEndpointResponse = UpdateNexusEndpointResponse

class DeleteNexusEndpointRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ENDPOINT_ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    endpoint_id: builtins.str
    """The id of the nexus endpoint to delete."""
    resource_version: builtins.str
    """The version of the endpoint for which this delete is intended for.
    The latest version can be found in the GetNexusEndpoint operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        endpoint_id: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "endpoint_id",
            b"endpoint_id",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___DeleteNexusEndpointRequest = DeleteNexusEndpointRequest

class DeleteNexusEndpointResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteNexusEndpointResponse = DeleteNexusEndpointResponse

class GetUserGroupsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class GoogleGroupFilter(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        EMAIL_ADDRESS_FIELD_NUMBER: builtins.int
        email_address: builtins.str
        """Filter groups by the google group email - optional."""
        def __init__(
            self,
            *,
            email_address: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["email_address", b"email_address"],
        ) -> None: ...

    class SCIMGroupFilter(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        IDP_ID_FIELD_NUMBER: builtins.int
        idp_id: builtins.str
        """Filter groups by the SCIM IDP id - optional."""
        def __init__(
            self,
            *,
            idp_id: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self, field_name: typing_extensions.Literal["idp_id", b"idp_id"]
        ) -> None: ...

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    DISPLAY_NAME_FIELD_NUMBER: builtins.int
    GOOGLE_GROUP_FIELD_NUMBER: builtins.int
    SCIM_GROUP_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve - optional.
    Cannot exceed 1000. Defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    namespace: builtins.str
    """Filter groups by the namespace they have access to - optional."""
    display_name: builtins.str
    """Filter groups by the display name - optional."""
    @property
    def google_group(self) -> global___GetUserGroupsRequest.GoogleGroupFilter:
        """Filter groups by the google group specification - optional."""
    @property
    def scim_group(self) -> global___GetUserGroupsRequest.SCIMGroupFilter:
        """Filter groups by the SCIM group specification - optional."""
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
        namespace: builtins.str = ...,
        display_name: builtins.str = ...,
        google_group: global___GetUserGroupsRequest.GoogleGroupFilter | None = ...,
        scim_group: global___GetUserGroupsRequest.SCIMGroupFilter | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "google_group", b"google_group", "scim_group", b"scim_group"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "display_name",
            b"display_name",
            "google_group",
            b"google_group",
            "namespace",
            b"namespace",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
            "scim_group",
            b"scim_group",
        ],
    ) -> None: ...

global___GetUserGroupsRequest = GetUserGroupsRequest

class GetUserGroupsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUPS_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def groups(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.identity.v1.message_pb2.UserGroup
    ]:
        """The list of groups in ascending name order."""
    next_page_token: builtins.str
    """The next page's token."""
    def __init__(
        self,
        *,
        groups: collections.abc.Iterable[
            temporalio.api.cloud.identity.v1.message_pb2.UserGroup
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "groups", b"groups", "next_page_token", b"next_page_token"
        ],
    ) -> None: ...

global___GetUserGroupsResponse = GetUserGroupsResponse

class GetUserGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_ID_FIELD_NUMBER: builtins.int
    group_id: builtins.str
    """The id of the group to get."""
    def __init__(
        self,
        *,
        group_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["group_id", b"group_id"]
    ) -> None: ...

global___GetUserGroupRequest = GetUserGroupRequest

class GetUserGroupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_FIELD_NUMBER: builtins.int
    @property
    def group(self) -> temporalio.api.cloud.identity.v1.message_pb2.UserGroup:
        """The group."""
    def __init__(
        self,
        *,
        group: temporalio.api.cloud.identity.v1.message_pb2.UserGroup | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["group", b"group"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["group", b"group"]
    ) -> None: ...

global___GetUserGroupResponse = GetUserGroupResponse

class CreateUserGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.UserGroupSpec:
        """The spec for the group to create."""
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.identity.v1.message_pb2.UserGroupSpec | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id", b"async_operation_id", "spec", b"spec"
        ],
    ) -> None: ...

global___CreateUserGroupRequest = CreateUserGroupRequest

class CreateUserGroupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_ID_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    group_id: builtins.str
    """The id of the group that was created."""
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        group_id: builtins.str = ...,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation", b"async_operation", "group_id", b"group_id"
        ],
    ) -> None: ...

global___CreateUserGroupResponse = CreateUserGroupResponse

class UpdateUserGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    group_id: builtins.str
    """The id of the group to update."""
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.UserGroupSpec:
        """The new group specification."""
    resource_version: builtins.str
    """The version of the group for which this update is intended for.
    The latest version can be found in the GetGroup operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        group_id: builtins.str = ...,
        spec: temporalio.api.cloud.identity.v1.message_pb2.UserGroupSpec | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "group_id",
            b"group_id",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___UpdateUserGroupRequest = UpdateUserGroupRequest

class UpdateUserGroupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateUserGroupResponse = UpdateUserGroupResponse

class DeleteUserGroupRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    group_id: builtins.str
    """The id of the group to delete."""
    resource_version: builtins.str
    """The version of the group for which this delete is intended for.
    The latest version can be found in the GetGroup operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        group_id: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "group_id",
            b"group_id",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___DeleteUserGroupRequest = DeleteUserGroupRequest

class DeleteUserGroupResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteUserGroupResponse = DeleteUserGroupResponse

class SetUserGroupNamespaceAccessRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    GROUP_ID_FIELD_NUMBER: builtins.int
    ACCESS_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to set permissions for."""
    group_id: builtins.str
    """The id of the group to set permissions for."""
    @property
    def access(self) -> temporalio.api.cloud.identity.v1.message_pb2.NamespaceAccess:
        """The namespace access to assign the group. If left empty, the group will be removed from the namespace access."""
    resource_version: builtins.str
    """The version of the group for which this update is intended for.
    The latest version can be found in the GetGroup operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        group_id: builtins.str = ...,
        access: temporalio.api.cloud.identity.v1.message_pb2.NamespaceAccess
        | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["access", b"access"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "access",
            b"access",
            "async_operation_id",
            b"async_operation_id",
            "group_id",
            b"group_id",
            "namespace",
            b"namespace",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___SetUserGroupNamespaceAccessRequest = SetUserGroupNamespaceAccessRequest

class SetUserGroupNamespaceAccessResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___SetUserGroupNamespaceAccessResponse = SetUserGroupNamespaceAccessResponse

class AddUserGroupMemberRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_ID_FIELD_NUMBER: builtins.int
    MEMBER_ID_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    group_id: builtins.str
    """The id of the group to add the member for."""
    @property
    def member_id(
        self,
    ) -> temporalio.api.cloud.identity.v1.message_pb2.UserGroupMemberId:
        """The member id to add to the group."""
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        group_id: builtins.str = ...,
        member_id: temporalio.api.cloud.identity.v1.message_pb2.UserGroupMemberId
        | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["member_id", b"member_id"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "group_id",
            b"group_id",
            "member_id",
            b"member_id",
        ],
    ) -> None: ...

global___AddUserGroupMemberRequest = AddUserGroupMemberRequest

class AddUserGroupMemberResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___AddUserGroupMemberResponse = AddUserGroupMemberResponse

class RemoveUserGroupMemberRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GROUP_ID_FIELD_NUMBER: builtins.int
    MEMBER_ID_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    group_id: builtins.str
    """The id of the group to add the member for."""
    @property
    def member_id(
        self,
    ) -> temporalio.api.cloud.identity.v1.message_pb2.UserGroupMemberId:
        """The member id to add to the group."""
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        group_id: builtins.str = ...,
        member_id: temporalio.api.cloud.identity.v1.message_pb2.UserGroupMemberId
        | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["member_id", b"member_id"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "group_id",
            b"group_id",
            "member_id",
            b"member_id",
        ],
    ) -> None: ...

global___RemoveUserGroupMemberRequest = RemoveUserGroupMemberRequest

class RemoveUserGroupMemberResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___RemoveUserGroupMemberResponse = RemoveUserGroupMemberResponse

class GetUserGroupMembersRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    GROUP_ID_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve - optional.
    Cannot exceed 1000. Defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    group_id: builtins.str
    """The group id to list members of."""
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
        group_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "group_id",
            b"group_id",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
        ],
    ) -> None: ...

global___GetUserGroupMembersRequest = GetUserGroupMembersRequest

class GetUserGroupMembersResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MEMBERS_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def members(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.identity.v1.message_pb2.UserGroupMember
    ]:
        """The list of group members"""
    next_page_token: builtins.str
    """The next page's token."""
    def __init__(
        self,
        *,
        members: collections.abc.Iterable[
            temporalio.api.cloud.identity.v1.message_pb2.UserGroupMember
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "members", b"members", "next_page_token", b"next_page_token"
        ],
    ) -> None: ...

global___GetUserGroupMembersResponse = GetUserGroupMembersResponse

class CreateServiceAccountRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.ServiceAccountSpec:
        """The spec of the service account to create."""
    async_operation_id: builtins.str
    """The ID to use for this async operation - optional."""
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.identity.v1.message_pb2.ServiceAccountSpec
        | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id", b"async_operation_id", "spec", b"spec"
        ],
    ) -> None: ...

global___CreateServiceAccountRequest = CreateServiceAccountRequest

class CreateServiceAccountResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SERVICE_ACCOUNT_ID_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    service_account_id: builtins.str
    """The ID of the created service account."""
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        service_account_id: builtins.str = ...,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation",
            b"async_operation",
            "service_account_id",
            b"service_account_id",
        ],
    ) -> None: ...

global___CreateServiceAccountResponse = CreateServiceAccountResponse

class GetServiceAccountRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SERVICE_ACCOUNT_ID_FIELD_NUMBER: builtins.int
    service_account_id: builtins.str
    """ID of the service account to retrieve."""
    def __init__(
        self,
        *,
        service_account_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "service_account_id", b"service_account_id"
        ],
    ) -> None: ...

global___GetServiceAccountRequest = GetServiceAccountRequest

class GetServiceAccountResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SERVICE_ACCOUNT_FIELD_NUMBER: builtins.int
    @property
    def service_account(
        self,
    ) -> temporalio.api.cloud.identity.v1.message_pb2.ServiceAccount:
        """The service account retrieved."""
    def __init__(
        self,
        *,
        service_account: temporalio.api.cloud.identity.v1.message_pb2.ServiceAccount
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["service_account", b"service_account"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["service_account", b"service_account"],
    ) -> None: ...

global___GetServiceAccountResponse = GetServiceAccountResponse

class GetServiceAccountsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve - optional.
    Cannot exceed 1000. Defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "page_size", b"page_size", "page_token", b"page_token"
        ],
    ) -> None: ...

global___GetServiceAccountsRequest = GetServiceAccountsRequest

class GetServiceAccountsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SERVICE_ACCOUNT_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def service_account(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.identity.v1.message_pb2.ServiceAccount
    ]:
        """The list of service accounts in ascending ID order."""
    next_page_token: builtins.str
    """The next page token, set if there is another page."""
    def __init__(
        self,
        *,
        service_account: collections.abc.Iterable[
            temporalio.api.cloud.identity.v1.message_pb2.ServiceAccount
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "next_page_token", b"next_page_token", "service_account", b"service_account"
        ],
    ) -> None: ...

global___GetServiceAccountsResponse = GetServiceAccountsResponse

class UpdateServiceAccountRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SERVICE_ACCOUNT_ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    service_account_id: builtins.str
    """The ID of the service account to update."""
    @property
    def spec(self) -> temporalio.api.cloud.identity.v1.message_pb2.ServiceAccountSpec:
        """The new service account specification."""
    resource_version: builtins.str
    """The version of the service account for which this update is intended for.
    The latest version can be found in the GetServiceAccount response.
    """
    async_operation_id: builtins.str
    """The ID to use for this async operation - optional."""
    def __init__(
        self,
        *,
        service_account_id: builtins.str = ...,
        spec: temporalio.api.cloud.identity.v1.message_pb2.ServiceAccountSpec
        | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "resource_version",
            b"resource_version",
            "service_account_id",
            b"service_account_id",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___UpdateServiceAccountRequest = UpdateServiceAccountRequest

class UpdateServiceAccountResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateServiceAccountResponse = UpdateServiceAccountResponse

class DeleteServiceAccountRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SERVICE_ACCOUNT_ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    service_account_id: builtins.str
    """The ID of the service account to delete;"""
    resource_version: builtins.str
    """The version of the service account for which this update is intended for.
    The latest version can be found in the GetServiceAccount response.
    """
    async_operation_id: builtins.str
    """The ID to use for this async operation - optional."""
    def __init__(
        self,
        *,
        service_account_id: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "resource_version",
            b"resource_version",
            "service_account_id",
            b"service_account_id",
        ],
    ) -> None: ...

global___DeleteServiceAccountRequest = DeleteServiceAccountRequest

class DeleteServiceAccountResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteServiceAccountResponse = DeleteServiceAccountResponse

class GetUsageRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    START_TIME_INCLUSIVE_FIELD_NUMBER: builtins.int
    END_TIME_EXCLUSIVE_FIELD_NUMBER: builtins.int
    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def start_time_inclusive(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """Filter for UTC time >= - optional.
        Defaults to: start of the current month.
        Must be: within the last 90 days from the current date.
        Must be: midnight UTC time.
        """
    @property
    def end_time_exclusive(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """Filter for UTC time < - optional.
        Defaults to: start of the next UTC day.
        Must be: within the last 90 days from the current date.
        Must be: midnight UTC time.
        """
    page_size: builtins.int
    """The requested size of the page to retrieve - optional.
    Each count corresponds to a single object - per day per namespace
    Cannot exceed 1000. Defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    def __init__(
        self,
        *,
        start_time_inclusive: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        end_time_exclusive: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "end_time_exclusive",
            b"end_time_exclusive",
            "start_time_inclusive",
            b"start_time_inclusive",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "end_time_exclusive",
            b"end_time_exclusive",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
            "start_time_inclusive",
            b"start_time_inclusive",
        ],
    ) -> None: ...

global___GetUsageRequest = GetUsageRequest

class GetUsageResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SUMMARIES_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def summaries(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.usage.v1.message_pb2.Summary
    ]:
        """The list of data based on granularity (per Day for now)
        Ordered by: time range in ascending order
        """
    next_page_token: builtins.str
    """The next page's token."""
    def __init__(
        self,
        *,
        summaries: collections.abc.Iterable[
            temporalio.api.cloud.usage.v1.message_pb2.Summary
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "next_page_token", b"next_page_token", "summaries", b"summaries"
        ],
    ) -> None: ...

global___GetUsageResponse = GetUsageResponse

class GetAccountRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___GetAccountRequest = GetAccountRequest

class GetAccountResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACCOUNT_FIELD_NUMBER: builtins.int
    @property
    def account(self) -> temporalio.api.cloud.account.v1.message_pb2.Account:
        """The account."""
    def __init__(
        self,
        *,
        account: temporalio.api.cloud.account.v1.message_pb2.Account | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["account", b"account"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["account", b"account"]
    ) -> None: ...

global___GetAccountResponse = GetAccountResponse

class UpdateAccountRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    @property
    def spec(self) -> temporalio.api.cloud.account.v1.message_pb2.AccountSpec:
        """The updated account specification to apply."""
    resource_version: builtins.str
    """The version of the account for which this update is intended for.
    The latest version can be found in the GetAccount operation response.
    """
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.account.v1.message_pb2.AccountSpec | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___UpdateAccountRequest = UpdateAccountRequest

class UpdateAccountResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateAccountResponse = UpdateAccountResponse

class CreateNamespaceExportSinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace under which the sink is configured."""
    @property
    def spec(self) -> temporalio.api.cloud.namespace.v1.message_pb2.ExportSinkSpec:
        """The specification for the export sink."""
    async_operation_id: builtins.str
    """Optional. The ID to use for this async operation."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        spec: temporalio.api.cloud.namespace.v1.message_pb2.ExportSinkSpec | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___CreateNamespaceExportSinkRequest = CreateNamespaceExportSinkRequest

class CreateNamespaceExportSinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___CreateNamespaceExportSinkResponse = CreateNamespaceExportSinkResponse

class GetNamespaceExportSinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to which the sink belongs."""
    name: builtins.str
    """The name of the sink to retrieve."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        name: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "name", b"name", "namespace", b"namespace"
        ],
    ) -> None: ...

global___GetNamespaceExportSinkRequest = GetNamespaceExportSinkRequest

class GetNamespaceExportSinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SINK_FIELD_NUMBER: builtins.int
    @property
    def sink(self) -> temporalio.api.cloud.namespace.v1.message_pb2.ExportSink:
        """The export sink retrieved."""
    def __init__(
        self,
        *,
        sink: temporalio.api.cloud.namespace.v1.message_pb2.ExportSink | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["sink", b"sink"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["sink", b"sink"]
    ) -> None: ...

global___GetNamespaceExportSinkResponse = GetNamespaceExportSinkResponse

class GetNamespaceExportSinksRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to which the sinks belong."""
    page_size: builtins.int
    """The requested size of the page to retrieve. Cannot exceed 1000.
    Defaults to 100 if not specified.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "namespace",
            b"namespace",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
        ],
    ) -> None: ...

global___GetNamespaceExportSinksRequest = GetNamespaceExportSinksRequest

class GetNamespaceExportSinksResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SINKS_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def sinks(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.namespace.v1.message_pb2.ExportSink
    ]:
        """The list of export sinks retrieved."""
    next_page_token: builtins.str
    """The next page token, set if there is another page."""
    def __init__(
        self,
        *,
        sinks: collections.abc.Iterable[
            temporalio.api.cloud.namespace.v1.message_pb2.ExportSink
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "next_page_token", b"next_page_token", "sinks", b"sinks"
        ],
    ) -> None: ...

global___GetNamespaceExportSinksResponse = GetNamespaceExportSinksResponse

class UpdateNamespaceExportSinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to which the sink belongs."""
    @property
    def spec(self) -> temporalio.api.cloud.namespace.v1.message_pb2.ExportSinkSpec:
        """The updated export sink specification."""
    resource_version: builtins.str
    """The version of the sink to update. The latest version can be
    retrieved using the GetNamespaceExportSink call.
    """
    async_operation_id: builtins.str
    """The ID to use for this async operation - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        spec: temporalio.api.cloud.namespace.v1.message_pb2.ExportSinkSpec | None = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
        ],
    ) -> None: ...

global___UpdateNamespaceExportSinkRequest = UpdateNamespaceExportSinkRequest

class UpdateNamespaceExportSinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateNamespaceExportSinkResponse = UpdateNamespaceExportSinkResponse

class DeleteNamespaceExportSinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to which the sink belongs."""
    name: builtins.str
    """The name of the sink to delete."""
    resource_version: builtins.str
    """The version of the sink to delete. The latest version can be
    retrieved using the GetNamespaceExportSink call.
    """
    async_operation_id: builtins.str
    """The ID to use for this async operation - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        name: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "name",
            b"name",
            "namespace",
            b"namespace",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___DeleteNamespaceExportSinkRequest = DeleteNamespaceExportSinkRequest

class DeleteNamespaceExportSinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteNamespaceExportSinkResponse = DeleteNamespaceExportSinkResponse

class ValidateNamespaceExportSinkRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAMESPACE_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to which the sink belongs."""
    @property
    def spec(self) -> temporalio.api.cloud.namespace.v1.message_pb2.ExportSinkSpec:
        """The export sink specification to validate."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        spec: temporalio.api.cloud.namespace.v1.message_pb2.ExportSinkSpec | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "namespace", b"namespace", "spec", b"spec"
        ],
    ) -> None: ...

global___ValidateNamespaceExportSinkRequest = ValidateNamespaceExportSinkRequest

class ValidateNamespaceExportSinkResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___ValidateNamespaceExportSinkResponse = ValidateNamespaceExportSinkResponse

class UpdateNamespaceTagsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class TagsToUpsertEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(
            self,
            field_name: typing_extensions.Literal["key", b"key", "value", b"value"],
        ) -> None: ...

    NAMESPACE_FIELD_NUMBER: builtins.int
    TAGS_TO_UPSERT_FIELD_NUMBER: builtins.int
    TAGS_TO_REMOVE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    namespace: builtins.str
    """The namespace to set tags for."""
    @property
    def tags_to_upsert(
        self,
    ) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]:
        """A list of tags to add or update.
        If a key of an existing tag is added, the tag's value is updated.
        At least one of tags_to_upsert or tags_to_remove must be specified.
        """
    @property
    def tags_to_remove(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """A list of tag keys to remove.
        If a tag key doesn't exist, it is silently ignored.
        At least one of tags_to_upsert or tags_to_remove must be specified.
        """
    async_operation_id: builtins.str
    """The id to use for this async operation - optional."""
    def __init__(
        self,
        *,
        namespace: builtins.str = ...,
        tags_to_upsert: collections.abc.Mapping[builtins.str, builtins.str]
        | None = ...,
        tags_to_remove: collections.abc.Iterable[builtins.str] | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "namespace",
            b"namespace",
            "tags_to_remove",
            b"tags_to_remove",
            "tags_to_upsert",
            b"tags_to_upsert",
        ],
    ) -> None: ...

global___UpdateNamespaceTagsRequest = UpdateNamespaceTagsRequest

class UpdateNamespaceTagsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation."""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___UpdateNamespaceTagsResponse = UpdateNamespaceTagsResponse

class CreateConnectivityRuleRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    @property
    def spec(
        self,
    ) -> temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRuleSpec:
        """The connectivity rule specification."""
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        spec: temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRuleSpec
        | None = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["spec", b"spec"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id", b"async_operation_id", "spec", b"spec"
        ],
    ) -> None: ...

global___CreateConnectivityRuleRequest = CreateConnectivityRuleRequest

class CreateConnectivityRuleResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONNECTIVITY_RULE_ID_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    connectivity_rule_id: builtins.str
    """The id of the connectivity rule that was created."""
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        connectivity_rule_id: builtins.str = ...,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation",
            b"async_operation",
            "connectivity_rule_id",
            b"connectivity_rule_id",
        ],
    ) -> None: ...

global___CreateConnectivityRuleResponse = CreateConnectivityRuleResponse

class GetConnectivityRuleRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONNECTIVITY_RULE_ID_FIELD_NUMBER: builtins.int
    connectivity_rule_id: builtins.str
    """The id of the connectivity rule to get."""
    def __init__(
        self,
        *,
        connectivity_rule_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "connectivity_rule_id", b"connectivity_rule_id"
        ],
    ) -> None: ...

global___GetConnectivityRuleRequest = GetConnectivityRuleRequest

class GetConnectivityRuleResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONNECTIVITY_RULE_FIELD_NUMBER: builtins.int
    @property
    def connectivity_rule(
        self,
    ) -> temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRule: ...
    def __init__(
        self,
        *,
        connectivity_rule: temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRule
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "connectivity_rule", b"connectivity_rule"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "connectivity_rule", b"connectivity_rule"
        ],
    ) -> None: ...

global___GetConnectivityRuleResponse = GetConnectivityRuleResponse

class GetConnectivityRulesRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PAGE_SIZE_FIELD_NUMBER: builtins.int
    PAGE_TOKEN_FIELD_NUMBER: builtins.int
    NAMESPACE_FIELD_NUMBER: builtins.int
    page_size: builtins.int
    """The requested size of the page to retrieve.
    Optional, defaults to 100.
    """
    page_token: builtins.str
    """The page token if this is continuing from another response.
    Optional, defaults to empty.
    """
    namespace: builtins.str
    """Filter connectivity rule by the namespace id."""
    def __init__(
        self,
        *,
        page_size: builtins.int = ...,
        page_token: builtins.str = ...,
        namespace: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "namespace",
            b"namespace",
            "page_size",
            b"page_size",
            "page_token",
            b"page_token",
        ],
    ) -> None: ...

global___GetConnectivityRulesRequest = GetConnectivityRulesRequest

class GetConnectivityRulesResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONNECTIVITY_RULES_FIELD_NUMBER: builtins.int
    NEXT_PAGE_TOKEN_FIELD_NUMBER: builtins.int
    @property
    def connectivity_rules(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRule
    ]:
        """connectivity_rules returned"""
    next_page_token: builtins.str
    """The next page token"""
    def __init__(
        self,
        *,
        connectivity_rules: collections.abc.Iterable[
            temporalio.api.cloud.connectivityrule.v1.message_pb2.ConnectivityRule
        ]
        | None = ...,
        next_page_token: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "connectivity_rules",
            b"connectivity_rules",
            "next_page_token",
            b"next_page_token",
        ],
    ) -> None: ...

global___GetConnectivityRulesResponse = GetConnectivityRulesResponse

class DeleteConnectivityRuleRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONNECTIVITY_RULE_ID_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    connectivity_rule_id: builtins.str
    """The ID of the connectivity rule that need be deleted, required."""
    resource_version: builtins.str
    """The resource version which should be the same from the the db, required
    The latest version can be found in the GetConnectivityRule operation response
    """
    async_operation_id: builtins.str
    """The id to use for this async operation.
    Optional, if not provided a random id will be generated.
    """
    def __init__(
        self,
        *,
        connectivity_rule_id: builtins.str = ...,
        resource_version: builtins.str = ...,
        async_operation_id: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "connectivity_rule_id",
            b"connectivity_rule_id",
            "resource_version",
            b"resource_version",
        ],
    ) -> None: ...

global___DeleteConnectivityRuleRequest = DeleteConnectivityRuleRequest

class DeleteConnectivityRuleResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ASYNC_OPERATION_FIELD_NUMBER: builtins.int
    @property
    def async_operation(
        self,
    ) -> temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation:
        """The async operation"""
    def __init__(
        self,
        *,
        async_operation: temporalio.api.cloud.operation.v1.message_pb2.AsyncOperation
        | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal["async_operation", b"async_operation"],
    ) -> None: ...

global___DeleteConnectivityRuleResponse = DeleteConnectivityRuleResponse
