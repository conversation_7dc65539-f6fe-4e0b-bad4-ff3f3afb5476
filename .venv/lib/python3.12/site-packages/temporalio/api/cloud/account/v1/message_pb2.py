# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/cloud/account/v1/message.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from temporalio.api.cloud.resource.v1 import (
    message_pb2 as temporal_dot_api_dot_cloud_dot_resource_dot_v1_dot_message__pb2,
)

DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n+temporal/api/cloud/account/v1/message.proto\x12\x1dtemporal.api.cloud.account.v1\x1a,temporal/api/cloud/resource/v1/message.proto")\n\x0bMetricsSpec\x12\x1a\n\x12\x61\x63\x63\x65pted_client_ca\x18\x02 \x01(\x0c"J\n\x0b\x41\x63\x63ountSpec\x12;\n\x07metrics\x18\x01 \x01(\x0b\x32*.temporal.api.cloud.account.v1.MetricsSpec"\x16\n\x07Metrics\x12\x0b\n\x03uri\x18\x01 \x01(\t"\xfc\x01\n\x07\x41\x63\x63ount\x12\n\n\x02id\x18\x01 \x01(\t\x12\x38\n\x04spec\x18\x02 \x01(\x0b\x32*.temporal.api.cloud.account.v1.AccountSpec\x12\x18\n\x10resource_version\x18\x03 \x01(\t\x12<\n\x05state\x18\x04 \x01(\x0e\x32-.temporal.api.cloud.resource.v1.ResourceState\x12\x1a\n\x12\x61sync_operation_id\x18\x05 \x01(\t\x12\x37\n\x07metrics\x18\x06 \x01(\x0b\x32&.temporal.api.cloud.account.v1.MetricsB\xa7\x01\n io.temporal.api.cloud.account.v1B\x0cMessageProtoP\x01Z+go.temporal.io/api/cloud/account/v1;account\xaa\x02\x1fTemporalio.Api.Cloud.Account.V1\xea\x02#Temporalio::Api::Cloud::Account::V1b\x06proto3'
)


_METRICSSPEC = DESCRIPTOR.message_types_by_name["MetricsSpec"]
_ACCOUNTSPEC = DESCRIPTOR.message_types_by_name["AccountSpec"]
_METRICS = DESCRIPTOR.message_types_by_name["Metrics"]
_ACCOUNT = DESCRIPTOR.message_types_by_name["Account"]
MetricsSpec = _reflection.GeneratedProtocolMessageType(
    "MetricsSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _METRICSSPEC,
        "__module__": "temporal.api.cloud.account.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.account.v1.MetricsSpec)
    },
)
_sym_db.RegisterMessage(MetricsSpec)

AccountSpec = _reflection.GeneratedProtocolMessageType(
    "AccountSpec",
    (_message.Message,),
    {
        "DESCRIPTOR": _ACCOUNTSPEC,
        "__module__": "temporal.api.cloud.account.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.account.v1.AccountSpec)
    },
)
_sym_db.RegisterMessage(AccountSpec)

Metrics = _reflection.GeneratedProtocolMessageType(
    "Metrics",
    (_message.Message,),
    {
        "DESCRIPTOR": _METRICS,
        "__module__": "temporal.api.cloud.account.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.account.v1.Metrics)
    },
)
_sym_db.RegisterMessage(Metrics)

Account = _reflection.GeneratedProtocolMessageType(
    "Account",
    (_message.Message,),
    {
        "DESCRIPTOR": _ACCOUNT,
        "__module__": "temporal.api.cloud.account.v1.message_pb2",
        # @@protoc_insertion_point(class_scope:temporal.api.cloud.account.v1.Account)
    },
)
_sym_db.RegisterMessage(Account)

if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n io.temporal.api.cloud.account.v1B\014MessageProtoP\001Z+go.temporal.io/api/cloud/account/v1;account\252\002\037Temporalio.Api.Cloud.Account.V1\352\002#Temporalio::Api::Cloud::Account::V1"
    _METRICSSPEC._serialized_start = 124
    _METRICSSPEC._serialized_end = 165
    _ACCOUNTSPEC._serialized_start = 167
    _ACCOUNTSPEC._serialized_end = 241
    _METRICS._serialized_start = 243
    _METRICS._serialized_end = 265
    _ACCOUNT._serialized_start = 268
    _ACCOUNT._serialized_end = 520
# @@protoc_insertion_point(module_scope)
