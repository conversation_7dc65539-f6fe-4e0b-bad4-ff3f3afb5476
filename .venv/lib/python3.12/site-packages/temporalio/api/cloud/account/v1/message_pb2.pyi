"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys

import google.protobuf.descriptor
import google.protobuf.message

import temporalio.api.cloud.resource.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class MetricsSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ACCEPTED_CLIENT_CA_FIELD_NUMBER: builtins.int
    accepted_client_ca: builtins.bytes
    """The ca cert(s) in PEM format that clients connecting to the metrics endpoint can use for authentication.
    This must only be one value, but the CA can have a chain.
    """
    def __init__(
        self,
        *,
        accepted_client_ca: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "accepted_client_ca", b"accepted_client_ca"
        ],
    ) -> None: ...

global___MetricsSpec = MetricsSpec

class AccountSpec(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    METRICS_FIELD_NUMBER: builtins.int
    @property
    def metrics(self) -> global___MetricsSpec:
        """The metrics specification for this account.
        If not specified, metrics will not be enabled.
        """
    def __init__(
        self,
        *,
        metrics: global___MetricsSpec | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["metrics", b"metrics"]
    ) -> builtins.bool: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["metrics", b"metrics"]
    ) -> None: ...

global___AccountSpec = AccountSpec

class Metrics(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    URI_FIELD_NUMBER: builtins.int
    uri: builtins.str
    """The prometheus metrics endpoint uri.
    This is only populated when the metrics is enabled in the metrics specification.
    """
    def __init__(
        self,
        *,
        uri: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["uri", b"uri"]
    ) -> None: ...

global___Metrics = Metrics

class Account(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    RESOURCE_VERSION_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    ASYNC_OPERATION_ID_FIELD_NUMBER: builtins.int
    METRICS_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The id of the account."""
    @property
    def spec(self) -> global___AccountSpec:
        """The account specification."""
    resource_version: builtins.str
    """The current version of the account specification.
    The next update operation will have to include this version.
    """
    state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType
    """The current state of the account."""
    async_operation_id: builtins.str
    """The id of the async operation that is updating the account, if any."""
    @property
    def metrics(self) -> global___Metrics:
        """Information related to metrics."""
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        spec: global___AccountSpec | None = ...,
        resource_version: builtins.str = ...,
        state: temporalio.api.cloud.resource.v1.message_pb2.ResourceState.ValueType = ...,
        async_operation_id: builtins.str = ...,
        metrics: global___Metrics | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["metrics", b"metrics", "spec", b"spec"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "async_operation_id",
            b"async_operation_id",
            "id",
            b"id",
            "metrics",
            b"metrics",
            "resource_version",
            b"resource_version",
            "spec",
            b"spec",
            "state",
            b"state",
        ],
    ) -> None: ...

global___Account = Account
