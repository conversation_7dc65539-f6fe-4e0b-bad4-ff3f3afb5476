"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _ResourceState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ResourceStateEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _ResourceState.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RESOURCE_STATE_UNSPECIFIED: _ResourceState.ValueType  # 0
    RESOURCE_STATE_ACTIVATING: _ResourceState.ValueType  # 1
    """The resource is being activated."""
    RESOURCE_STATE_ACTIVATION_FAILED: _ResourceState.ValueType  # 2
    """The resource failed to activate. This is an error state. Reach out to support for remediation."""
    RESOURCE_STATE_ACTIVE: _ResourceState.ValueType  # 3
    """The resource is active and ready to use."""
    RESOURCE_STATE_UPDATING: _ResourceState.ValueType  # 4
    """The resource is being updated."""
    RESOURCE_STATE_UPDATE_FAILED: _ResourceState.ValueType  # 5
    """The resource failed to update. This is an error state. Reach out to support for remediation."""
    RESOURCE_STATE_DELETING: _ResourceState.ValueType  # 6
    """The resource is being deleted."""
    RESOURCE_STATE_DELETE_FAILED: _ResourceState.ValueType  # 7
    """The resource failed to delete. This is an error state. Reach out to support for remediation."""
    RESOURCE_STATE_DELETED: _ResourceState.ValueType  # 8
    """The resource has been deleted."""
    RESOURCE_STATE_SUSPENDED: _ResourceState.ValueType  # 9
    """The resource is suspended and not available for use. Reach out to support for remediation."""
    RESOURCE_STATE_EXPIRED: _ResourceState.ValueType  # 10
    """The resource has expired and is no longer available for use."""

class ResourceState(_ResourceState, metaclass=_ResourceStateEnumTypeWrapper): ...

RESOURCE_STATE_UNSPECIFIED: ResourceState.ValueType  # 0
RESOURCE_STATE_ACTIVATING: ResourceState.ValueType  # 1
"""The resource is being activated."""
RESOURCE_STATE_ACTIVATION_FAILED: ResourceState.ValueType  # 2
"""The resource failed to activate. This is an error state. Reach out to support for remediation."""
RESOURCE_STATE_ACTIVE: ResourceState.ValueType  # 3
"""The resource is active and ready to use."""
RESOURCE_STATE_UPDATING: ResourceState.ValueType  # 4
"""The resource is being updated."""
RESOURCE_STATE_UPDATE_FAILED: ResourceState.ValueType  # 5
"""The resource failed to update. This is an error state. Reach out to support for remediation."""
RESOURCE_STATE_DELETING: ResourceState.ValueType  # 6
"""The resource is being deleted."""
RESOURCE_STATE_DELETE_FAILED: ResourceState.ValueType  # 7
"""The resource failed to delete. This is an error state. Reach out to support for remediation."""
RESOURCE_STATE_DELETED: ResourceState.ValueType  # 8
"""The resource has been deleted."""
RESOURCE_STATE_SUSPENDED: ResourceState.ValueType  # 9
"""The resource is suspended and not available for use. Reach out to support for remediation."""
RESOURCE_STATE_EXPIRED: ResourceState.ValueType  # 10
"""The resource has expired and is no longer available for use."""
global___ResourceState = ResourceState
