"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.any_pb2
import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import google.protobuf.timestamp_pb2

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class AsyncOperation(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _State:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _StateEnumTypeWrapper(
        google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
            AsyncOperation._State.ValueType
        ],
        builtins.type,
    ):  # noqa: F821
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        STATE_UNSPECIFIED: AsyncOperation._State.ValueType  # 0
        STATE_PENDING: AsyncOperation._State.ValueType  # 1
        """The operation is pending."""
        STATE_IN_PROGRESS: AsyncOperation._State.ValueType  # 2
        """The operation is in progress."""
        STATE_FAILED: AsyncOperation._State.ValueType  # 3
        """The operation failed, check failure_reason for more details."""
        STATE_CANCELLED: AsyncOperation._State.ValueType  # 4
        """The operation was cancelled."""
        STATE_FULFILLED: AsyncOperation._State.ValueType  # 5
        """The operation was fulfilled."""
        STATE_REJECTED: AsyncOperation._State.ValueType  # 6
        """The operation was rejected."""

    class State(_State, metaclass=_StateEnumTypeWrapper): ...
    STATE_UNSPECIFIED: AsyncOperation.State.ValueType  # 0
    STATE_PENDING: AsyncOperation.State.ValueType  # 1
    """The operation is pending."""
    STATE_IN_PROGRESS: AsyncOperation.State.ValueType  # 2
    """The operation is in progress."""
    STATE_FAILED: AsyncOperation.State.ValueType  # 3
    """The operation failed, check failure_reason for more details."""
    STATE_CANCELLED: AsyncOperation.State.ValueType  # 4
    """The operation was cancelled."""
    STATE_FULFILLED: AsyncOperation.State.ValueType  # 5
    """The operation was fulfilled."""
    STATE_REJECTED: AsyncOperation.State.ValueType  # 6
    """The operation was rejected."""

    ID_FIELD_NUMBER: builtins.int
    STATE_DEPRECATED_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    CHECK_DURATION_FIELD_NUMBER: builtins.int
    OPERATION_TYPE_FIELD_NUMBER: builtins.int
    OPERATION_INPUT_FIELD_NUMBER: builtins.int
    FAILURE_REASON_FIELD_NUMBER: builtins.int
    STARTED_TIME_FIELD_NUMBER: builtins.int
    FINISHED_TIME_FIELD_NUMBER: builtins.int
    id: builtins.str
    """The operation id."""
    state_deprecated: builtins.str
    """The current state of this operation.
    Possible values are: pending, in_progress, failed, cancelled, fulfilled.
    Deprecated: Not supported after v0.3.0 api version. Use state instead.
    temporal:versioning:max_version=v0.3.0
    """
    state: global___AsyncOperation.State.ValueType
    """The current state of this operation.
    temporal:versioning:min_version=v0.3.0
    temporal:enums:replaces=state_deprecated
    """
    @property
    def check_duration(self) -> google.protobuf.duration_pb2.Duration:
        """The recommended duration to check back for an update in the operation's state."""
    operation_type: builtins.str
    """The type of operation being performed."""
    @property
    def operation_input(self) -> google.protobuf.any_pb2.Any:
        """The input to the operation being performed.

        (-- api-linter: core::0146::any=disabled --)
        """
    failure_reason: builtins.str
    """If the operation failed, the reason for the failure."""
    @property
    def started_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the operation initiated."""
    @property
    def finished_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """The date and time when the operation completed."""
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        state_deprecated: builtins.str = ...,
        state: global___AsyncOperation.State.ValueType = ...,
        check_duration: google.protobuf.duration_pb2.Duration | None = ...,
        operation_type: builtins.str = ...,
        operation_input: google.protobuf.any_pb2.Any | None = ...,
        failure_reason: builtins.str = ...,
        started_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        finished_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "check_duration",
            b"check_duration",
            "finished_time",
            b"finished_time",
            "operation_input",
            b"operation_input",
            "started_time",
            b"started_time",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "check_duration",
            b"check_duration",
            "failure_reason",
            b"failure_reason",
            "finished_time",
            b"finished_time",
            "id",
            b"id",
            "operation_input",
            b"operation_input",
            "operation_type",
            b"operation_type",
            "started_time",
            b"started_time",
            "state",
            b"state",
            "state_deprecated",
            b"state_deprecated",
        ],
    ) -> None: ...

global___AsyncOperation = AsyncOperation
