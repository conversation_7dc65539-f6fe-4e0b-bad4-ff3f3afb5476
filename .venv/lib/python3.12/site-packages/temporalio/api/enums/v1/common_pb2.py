# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/common.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n\"temporal/api/enums/v1/common.proto\x12\x15temporal.api.enums.v1*_\n\x0c\x45ncodingType\x12\x1d\n\x19\x45NCODING_TYPE_UNSPECIFIED\x10\x00\x12\x18\n\x14\x45NCODING_TYPE_PROTO3\x10\x01\x12\x16\n\x12\x45NCODING_TYPE_JSON\x10\x02*\x91\x02\n\x10IndexedValueType\x12\"\n\x1eINDEXED_VALUE_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n\x17INDEXED_VALUE_TYPE_TEXT\x10\x01\x12\x1e\n\x1aINDEXED_VALUE_TYPE_KEYWORD\x10\x02\x12\x1a\n\x16INDEXED_VALUE_TYPE_INT\x10\x03\x12\x1d\n\x19INDEXED_VALUE_TYPE_DOUBLE\x10\x04\x12\x1b\n\x17INDEXED_VALUE_TYPE_BOOL\x10\x05\x12\x1f\n\x1bINDEXED_VALUE_TYPE_DATETIME\x10\x06\x12#\n\x1fINDEXED_VALUE_TYPE_KEYWORD_LIST\x10\x07*^\n\x08Severity\x12\x18\n\x14SEVERITY_UNSPECIFIED\x10\x00\x12\x11\n\rSEVERITY_HIGH\x10\x01\x12\x13\n\x0fSEVERITY_MEDIUM\x10\x02\x12\x10\n\x0cSEVERITY_LOW\x10\x03*\xde\x01\n\rCallbackState\x12\x1e\n\x1a\x43\x41LLBACK_STATE_UNSPECIFIED\x10\x00\x12\x1a\n\x16\x43\x41LLBACK_STATE_STANDBY\x10\x01\x12\x1c\n\x18\x43\x41LLBACK_STATE_SCHEDULED\x10\x02\x12\x1e\n\x1a\x43\x41LLBACK_STATE_BACKING_OFF\x10\x03\x12\x19\n\x15\x43\x41LLBACK_STATE_FAILED\x10\x04\x12\x1c\n\x18\x43\x41LLBACK_STATE_SUCCEEDED\x10\x05\x12\x1a\n\x16\x43\x41LLBACK_STATE_BLOCKED\x10\x06*\xfd\x01\n\x1aPendingNexusOperationState\x12-\n)PENDING_NEXUS_OPERATION_STATE_UNSPECIFIED\x10\x00\x12+\n'PENDING_NEXUS_OPERATION_STATE_SCHEDULED\x10\x01\x12-\n)PENDING_NEXUS_OPERATION_STATE_BACKING_OFF\x10\x02\x12)\n%PENDING_NEXUS_OPERATION_STATE_STARTED\x10\x03\x12)\n%PENDING_NEXUS_OPERATION_STATE_BLOCKED\x10\x04*\xfe\x02\n\x1fNexusOperationCancellationState\x12\x32\n.NEXUS_OPERATION_CANCELLATION_STATE_UNSPECIFIED\x10\x00\x12\x30\n,NEXUS_OPERATION_CANCELLATION_STATE_SCHEDULED\x10\x01\x12\x32\n.NEXUS_OPERATION_CANCELLATION_STATE_BACKING_OFF\x10\x02\x12\x30\n,NEXUS_OPERATION_CANCELLATION_STATE_SUCCEEDED\x10\x03\x12-\n)NEXUS_OPERATION_CANCELLATION_STATE_FAILED\x10\x04\x12\x30\n,NEXUS_OPERATION_CANCELLATION_STATE_TIMED_OUT\x10\x05\x12.\n*NEXUS_OPERATION_CANCELLATION_STATE_BLOCKED\x10\x06*\x97\x01\n\x17WorkflowRuleActionScope\x12*\n&WORKFLOW_RULE_ACTION_SCOPE_UNSPECIFIED\x10\x00\x12'\n#WORKFLOW_RULE_ACTION_SCOPE_WORKFLOW\x10\x01\x12'\n#WORKFLOW_RULE_ACTION_SCOPE_ACTIVITY\x10\x02*m\n\x18\x41pplicationErrorCategory\x12*\n&APPLICATION_ERROR_CATEGORY_UNSPECIFIED\x10\x00\x12%\n!APPLICATION_ERROR_CATEGORY_BENIGN\x10\x01*\x85\x01\n\x0cWorkerStatus\x12\x1d\n\x19WORKER_STATUS_UNSPECIFIED\x10\x00\x12\x19\n\x15WORKER_STATUS_RUNNING\x10\x01\x12\x1f\n\x1bWORKER_STATUS_SHUTTING_DOWN\x10\x02\x12\x1a\n\x16WORKER_STATUS_SHUTDOWN\x10\x03\x42\x83\x01\n\x18io.temporal.api.enums.v1B\x0b\x43ommonProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3"
)

_ENCODINGTYPE = DESCRIPTOR.enum_types_by_name["EncodingType"]
EncodingType = enum_type_wrapper.EnumTypeWrapper(_ENCODINGTYPE)
_INDEXEDVALUETYPE = DESCRIPTOR.enum_types_by_name["IndexedValueType"]
IndexedValueType = enum_type_wrapper.EnumTypeWrapper(_INDEXEDVALUETYPE)
_SEVERITY = DESCRIPTOR.enum_types_by_name["Severity"]
Severity = enum_type_wrapper.EnumTypeWrapper(_SEVERITY)
_CALLBACKSTATE = DESCRIPTOR.enum_types_by_name["CallbackState"]
CallbackState = enum_type_wrapper.EnumTypeWrapper(_CALLBACKSTATE)
_PENDINGNEXUSOPERATIONSTATE = DESCRIPTOR.enum_types_by_name[
    "PendingNexusOperationState"
]
PendingNexusOperationState = enum_type_wrapper.EnumTypeWrapper(
    _PENDINGNEXUSOPERATIONSTATE
)
_NEXUSOPERATIONCANCELLATIONSTATE = DESCRIPTOR.enum_types_by_name[
    "NexusOperationCancellationState"
]
NexusOperationCancellationState = enum_type_wrapper.EnumTypeWrapper(
    _NEXUSOPERATIONCANCELLATIONSTATE
)
_WORKFLOWRULEACTIONSCOPE = DESCRIPTOR.enum_types_by_name["WorkflowRuleActionScope"]
WorkflowRuleActionScope = enum_type_wrapper.EnumTypeWrapper(_WORKFLOWRULEACTIONSCOPE)
_APPLICATIONERRORCATEGORY = DESCRIPTOR.enum_types_by_name["ApplicationErrorCategory"]
ApplicationErrorCategory = enum_type_wrapper.EnumTypeWrapper(_APPLICATIONERRORCATEGORY)
_WORKERSTATUS = DESCRIPTOR.enum_types_by_name["WorkerStatus"]
WorkerStatus = enum_type_wrapper.EnumTypeWrapper(_WORKERSTATUS)
ENCODING_TYPE_UNSPECIFIED = 0
ENCODING_TYPE_PROTO3 = 1
ENCODING_TYPE_JSON = 2
INDEXED_VALUE_TYPE_UNSPECIFIED = 0
INDEXED_VALUE_TYPE_TEXT = 1
INDEXED_VALUE_TYPE_KEYWORD = 2
INDEXED_VALUE_TYPE_INT = 3
INDEXED_VALUE_TYPE_DOUBLE = 4
INDEXED_VALUE_TYPE_BOOL = 5
INDEXED_VALUE_TYPE_DATETIME = 6
INDEXED_VALUE_TYPE_KEYWORD_LIST = 7
SEVERITY_UNSPECIFIED = 0
SEVERITY_HIGH = 1
SEVERITY_MEDIUM = 2
SEVERITY_LOW = 3
CALLBACK_STATE_UNSPECIFIED = 0
CALLBACK_STATE_STANDBY = 1
CALLBACK_STATE_SCHEDULED = 2
CALLBACK_STATE_BACKING_OFF = 3
CALLBACK_STATE_FAILED = 4
CALLBACK_STATE_SUCCEEDED = 5
CALLBACK_STATE_BLOCKED = 6
PENDING_NEXUS_OPERATION_STATE_UNSPECIFIED = 0
PENDING_NEXUS_OPERATION_STATE_SCHEDULED = 1
PENDING_NEXUS_OPERATION_STATE_BACKING_OFF = 2
PENDING_NEXUS_OPERATION_STATE_STARTED = 3
PENDING_NEXUS_OPERATION_STATE_BLOCKED = 4
NEXUS_OPERATION_CANCELLATION_STATE_UNSPECIFIED = 0
NEXUS_OPERATION_CANCELLATION_STATE_SCHEDULED = 1
NEXUS_OPERATION_CANCELLATION_STATE_BACKING_OFF = 2
NEXUS_OPERATION_CANCELLATION_STATE_SUCCEEDED = 3
NEXUS_OPERATION_CANCELLATION_STATE_FAILED = 4
NEXUS_OPERATION_CANCELLATION_STATE_TIMED_OUT = 5
NEXUS_OPERATION_CANCELLATION_STATE_BLOCKED = 6
WORKFLOW_RULE_ACTION_SCOPE_UNSPECIFIED = 0
WORKFLOW_RULE_ACTION_SCOPE_WORKFLOW = 1
WORKFLOW_RULE_ACTION_SCOPE_ACTIVITY = 2
APPLICATION_ERROR_CATEGORY_UNSPECIFIED = 0
APPLICATION_ERROR_CATEGORY_BENIGN = 1
WORKER_STATUS_UNSPECIFIED = 0
WORKER_STATUS_RUNNING = 1
WORKER_STATUS_SHUTTING_DOWN = 2
WORKER_STATUS_SHUTDOWN = 3


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\013CommonProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _ENCODINGTYPE._serialized_start = 61
    _ENCODINGTYPE._serialized_end = 156
    _INDEXEDVALUETYPE._serialized_start = 159
    _INDEXEDVALUETYPE._serialized_end = 432
    _SEVERITY._serialized_start = 434
    _SEVERITY._serialized_end = 528
    _CALLBACKSTATE._serialized_start = 531
    _CALLBACKSTATE._serialized_end = 753
    _PENDINGNEXUSOPERATIONSTATE._serialized_start = 756
    _PENDINGNEXUSOPERATIONSTATE._serialized_end = 1009
    _NEXUSOPERATIONCANCELLATIONSTATE._serialized_start = 1012
    _NEXUSOPERATIONCANCELLATIONSTATE._serialized_end = 1394
    _WORKFLOWRULEACTIONSCOPE._serialized_start = 1397
    _WORKFLOWRULEACTIONSCOPE._serialized_end = 1548
    _APPLICATIONERRORCATEGORY._serialized_start = 1550
    _APPLICATIONERRORCATEGORY._serialized_end = 1659
    _WORKERSTATUS._serialized_start = 1662
    _WORKERSTATUS._serialized_end = 1795
# @@protoc_insertion_point(module_scope)
