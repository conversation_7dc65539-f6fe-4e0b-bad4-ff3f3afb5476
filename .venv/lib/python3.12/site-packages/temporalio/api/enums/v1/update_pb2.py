# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/update.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n"temporal/api/enums/v1/update.proto\x12\x15temporal.api.enums.v1*\x8b\x02\n%UpdateWorkflowExecutionLifecycleStage\x12\x39\n5UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_UNSPECIFIED\x10\x00\x12\x36\n2UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ADMITTED\x10\x01\x12\x36\n2UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ACCEPTED\x10\x02\x12\x37\n3UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_COMPLETED\x10\x03*s\n\x19UpdateAdmittedEventOrigin\x12,\n(UPDATE_ADMITTED_EVENT_ORIGIN_UNSPECIFIED\x10\x00\x12(\n$UPDATE_ADMITTED_EVENT_ORIGIN_REAPPLY\x10\x01\x42\x83\x01\n\x18io.temporal.api.enums.v1B\x0bUpdateProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3'
)

_UPDATEWORKFLOWEXECUTIONLIFECYCLESTAGE = DESCRIPTOR.enum_types_by_name[
    "UpdateWorkflowExecutionLifecycleStage"
]
UpdateWorkflowExecutionLifecycleStage = enum_type_wrapper.EnumTypeWrapper(
    _UPDATEWORKFLOWEXECUTIONLIFECYCLESTAGE
)
_UPDATEADMITTEDEVENTORIGIN = DESCRIPTOR.enum_types_by_name["UpdateAdmittedEventOrigin"]
UpdateAdmittedEventOrigin = enum_type_wrapper.EnumTypeWrapper(
    _UPDATEADMITTEDEVENTORIGIN
)
UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_UNSPECIFIED = 0
UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ADMITTED = 1
UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ACCEPTED = 2
UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_COMPLETED = 3
UPDATE_ADMITTED_EVENT_ORIGIN_UNSPECIFIED = 0
UPDATE_ADMITTED_EVENT_ORIGIN_REAPPLY = 1


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\013UpdateProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _UPDATEWORKFLOWEXECUTIONLIFECYCLESTAGE._serialized_start = 62
    _UPDATEWORKFLOWEXECUTIONLIFECYCLESTAGE._serialized_end = 329
    _UPDATEADMITTEDEVENTORIGIN._serialized_start = 331
    _UPDATEADMITTEDEVENTORIGIN._serialized_end = 446
# @@protoc_insertion_point(module_scope)
