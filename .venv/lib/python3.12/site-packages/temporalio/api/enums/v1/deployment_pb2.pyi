"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _DeploymentReachability:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DeploymentReachabilityEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _DeploymentReachability.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DEPLOYMENT_REACHABILITY_UNSPECIFIED: _DeploymentReachability.ValueType  # 0
    """Reachability level is not specified."""
    DEPLOYMENT_REACHABILITY_REACHABLE: _DeploymentReachability.ValueType  # 1
    """The deployment is reachable by new and/or open workflows. The deployment cannot be
    decommissioned safely.
    """
    DEPLOYMENT_REACHABILITY_CLOSED_WORKFLOWS_ONLY: (
        _DeploymentReachability.ValueType
    )  # 2
    """The deployment is not reachable by new or open workflows, but might be still needed by
    Queries sent to closed workflows. The deployment can be decommissioned safely if user does
    not query closed workflows.
    """
    DEPLOYMENT_REACHABILITY_UNREACHABLE: _DeploymentReachability.ValueType  # 3
    """The deployment is not reachable by any workflow because all the workflows who needed this
    deployment went out of retention period. The deployment can be decommissioned safely.
    """

class DeploymentReachability(
    _DeploymentReachability, metaclass=_DeploymentReachabilityEnumTypeWrapper
):
    """Specify the reachability level for a deployment so users can decide if it is time to
    decommission the deployment.
    """

DEPLOYMENT_REACHABILITY_UNSPECIFIED: DeploymentReachability.ValueType  # 0
"""Reachability level is not specified."""
DEPLOYMENT_REACHABILITY_REACHABLE: DeploymentReachability.ValueType  # 1
"""The deployment is reachable by new and/or open workflows. The deployment cannot be
decommissioned safely.
"""
DEPLOYMENT_REACHABILITY_CLOSED_WORKFLOWS_ONLY: DeploymentReachability.ValueType  # 2
"""The deployment is not reachable by new or open workflows, but might be still needed by
Queries sent to closed workflows. The deployment can be decommissioned safely if user does
not query closed workflows.
"""
DEPLOYMENT_REACHABILITY_UNREACHABLE: DeploymentReachability.ValueType  # 3
"""The deployment is not reachable by any workflow because all the workflows who needed this
deployment went out of retention period. The deployment can be decommissioned safely.
"""
global___DeploymentReachability = DeploymentReachability

class _VersionDrainageStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _VersionDrainageStatusEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _VersionDrainageStatus.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    VERSION_DRAINAGE_STATUS_UNSPECIFIED: _VersionDrainageStatus.ValueType  # 0
    """Drainage Status is not specified."""
    VERSION_DRAINAGE_STATUS_DRAINING: _VersionDrainageStatus.ValueType  # 1
    """The Worker Deployment Version is not used by new workflows but is still used by
    open pinned workflows. The version cannot be decommissioned safely.
    """
    VERSION_DRAINAGE_STATUS_DRAINED: _VersionDrainageStatus.ValueType  # 2
    """The Worker Deployment Version is not used by new or open workflows, but might be still needed by
    Queries sent to closed workflows. The version can be decommissioned safely if user does
    not query closed workflows. If the user does query closed workflows for some time x after
    workflows are closed, they should decommission the version after it has been drained for that duration.
    """

class VersionDrainageStatus(
    _VersionDrainageStatus, metaclass=_VersionDrainageStatusEnumTypeWrapper
):
    """(-- api-linter: core::0216::synonyms=disabled
        aip.dev/not-precedent: Call this status because it is . --)
    Specify the drainage status for a Worker Deployment Version so users can decide whether they
    can safely decommission the version.
    Experimental. Worker Deployments are experimental and might significantly change in the future.
    """

VERSION_DRAINAGE_STATUS_UNSPECIFIED: VersionDrainageStatus.ValueType  # 0
"""Drainage Status is not specified."""
VERSION_DRAINAGE_STATUS_DRAINING: VersionDrainageStatus.ValueType  # 1
"""The Worker Deployment Version is not used by new workflows but is still used by
open pinned workflows. The version cannot be decommissioned safely.
"""
VERSION_DRAINAGE_STATUS_DRAINED: VersionDrainageStatus.ValueType  # 2
"""The Worker Deployment Version is not used by new or open workflows, but might be still needed by
Queries sent to closed workflows. The version can be decommissioned safely if user does
not query closed workflows. If the user does query closed workflows for some time x after
workflows are closed, they should decommission the version after it has been drained for that duration.
"""
global___VersionDrainageStatus = VersionDrainageStatus

class _WorkerVersioningMode:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _WorkerVersioningModeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _WorkerVersioningMode.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    WORKER_VERSIONING_MODE_UNSPECIFIED: _WorkerVersioningMode.ValueType  # 0
    WORKER_VERSIONING_MODE_UNVERSIONED: _WorkerVersioningMode.ValueType  # 1
    """Workers with this mode are not distinguished from each other for task routing, even if they
    have different Build IDs.
    Workflows processed by this worker will be unversioned and user needs to use Patching to keep
    the new code compatible with prior versions.
    This mode is recommended to be used along with Rolling Upgrade deployment strategies.
    Workers with this mode are represented by the special string `__unversioned__` in the APIs.
    """
    WORKER_VERSIONING_MODE_VERSIONED: _WorkerVersioningMode.ValueType  # 2
    """Workers with this mode are part of a Worker Deployment Version which is identified as
    "<deployment_name>.<build_id>". Such workers are called "versioned" as opposed to
    "unversioned".
    Each Deployment Version is distinguished from other Versions for task routing and users can
    configure Temporal Server to send tasks to a particular Version (see
    `WorkerDeploymentInfo.routing_config`). This mode is the best option for Blue/Green and
    Rainbow strategies (but typically not suitable for Rolling upgrades.)
    Workflow Versioning Behaviors are enabled in this mode: each workflow type must choose
    between the Pinned and AutoUpgrade behaviors. Depending on the chosen behavior, the user may
    or may not need to use Patching to keep the new code compatible with prior versions. (see
    VersioningBehavior enum.)
    """

class WorkerVersioningMode(
    _WorkerVersioningMode, metaclass=_WorkerVersioningModeEnumTypeWrapper
):
    """Versioning Mode of a worker is set by the app developer in the worker code, and specifies the
    behavior of the system in the following related aspects:
    - Whether or not Temporal Server considers this worker's version (Build ID) when dispatching
      tasks to it.
    - Whether or not the workflows processed by this worker are versioned using the worker's version.
    Experimental. Worker Deployments are experimental and might significantly change in the future.
    """

WORKER_VERSIONING_MODE_UNSPECIFIED: WorkerVersioningMode.ValueType  # 0
WORKER_VERSIONING_MODE_UNVERSIONED: WorkerVersioningMode.ValueType  # 1
"""Workers with this mode are not distinguished from each other for task routing, even if they
have different Build IDs.
Workflows processed by this worker will be unversioned and user needs to use Patching to keep
the new code compatible with prior versions.
This mode is recommended to be used along with Rolling Upgrade deployment strategies.
Workers with this mode are represented by the special string `__unversioned__` in the APIs.
"""
WORKER_VERSIONING_MODE_VERSIONED: WorkerVersioningMode.ValueType  # 2
"""Workers with this mode are part of a Worker Deployment Version which is identified as
"<deployment_name>.<build_id>". Such workers are called "versioned" as opposed to
"unversioned".
Each Deployment Version is distinguished from other Versions for task routing and users can
configure Temporal Server to send tasks to a particular Version (see
`WorkerDeploymentInfo.routing_config`). This mode is the best option for Blue/Green and
Rainbow strategies (but typically not suitable for Rolling upgrades.)
Workflow Versioning Behaviors are enabled in this mode: each workflow type must choose
between the Pinned and AutoUpgrade behaviors. Depending on the chosen behavior, the user may
or may not need to use Patching to keep the new code compatible with prior versions. (see
VersioningBehavior enum.)
"""
global___WorkerVersioningMode = WorkerVersioningMode

class _WorkerDeploymentVersionStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _WorkerDeploymentVersionStatusEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _WorkerDeploymentVersionStatus.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    WORKER_DEPLOYMENT_VERSION_STATUS_UNSPECIFIED: (
        _WorkerDeploymentVersionStatus.ValueType
    )  # 0
    WORKER_DEPLOYMENT_VERSION_STATUS_INACTIVE: (
        _WorkerDeploymentVersionStatus.ValueType
    )  # 1
    """The Worker Deployment Version has been created inside the Worker Deployment but is not used by any
    workflow executions. These Versions can still have workflows if they have an explicit Versioning Override targeting
    this Version. Such Versioning Override could be set at workflow start time, or at a later time via `UpdateWorkflowExecutionOptions`.
    """
    WORKER_DEPLOYMENT_VERSION_STATUS_CURRENT: (
        _WorkerDeploymentVersionStatus.ValueType
    )  # 2
    """The Worker Deployment Version is the current version of the Worker Deployment. All new workflow executions 
    and tasks of existing unversioned or AutoUpgrade workflows are routed to this version.
    """
    WORKER_DEPLOYMENT_VERSION_STATUS_RAMPING: (
        _WorkerDeploymentVersionStatus.ValueType
    )  # 3
    """The Worker Deployment Version is the ramping version of the Worker Deployment. A subset of new Pinned workflow executions are 
    routed to this version. Moreover, a portion of existing unversioned or AutoUpgrade workflow executions are also routed to this version.
    """
    WORKER_DEPLOYMENT_VERSION_STATUS_DRAINING: (
        _WorkerDeploymentVersionStatus.ValueType
    )  # 4
    """The Worker Deployment Version is not used by new workflows but is still used by
    open pinned workflows. The version cannot be decommissioned safely.
    """
    WORKER_DEPLOYMENT_VERSION_STATUS_DRAINED: (
        _WorkerDeploymentVersionStatus.ValueType
    )  # 5
    """The Worker Deployment Version is not used by new or open workflows, but might be still needed by
    Queries sent to closed workflows. The version can be decommissioned safely if user does
    not query closed workflows. If the user does query closed workflows for some time x after
    workflows are closed, they should decommission the version after it has been drained for that duration.
    """

class WorkerDeploymentVersionStatus(
    _WorkerDeploymentVersionStatus,
    metaclass=_WorkerDeploymentVersionStatusEnumTypeWrapper,
):
    """(-- api-linter: core::0216::synonyms=disabled
        aip.dev/not-precedent: Call this status because it is . --)
    Specify the status of a Worker Deployment Version.
    Experimental. Worker Deployments are experimental and might significantly change in the future.
    """

WORKER_DEPLOYMENT_VERSION_STATUS_UNSPECIFIED: (
    WorkerDeploymentVersionStatus.ValueType
)  # 0
WORKER_DEPLOYMENT_VERSION_STATUS_INACTIVE: WorkerDeploymentVersionStatus.ValueType  # 1
"""The Worker Deployment Version has been created inside the Worker Deployment but is not used by any
workflow executions. These Versions can still have workflows if they have an explicit Versioning Override targeting
this Version. Such Versioning Override could be set at workflow start time, or at a later time via `UpdateWorkflowExecutionOptions`.
"""
WORKER_DEPLOYMENT_VERSION_STATUS_CURRENT: WorkerDeploymentVersionStatus.ValueType  # 2
"""The Worker Deployment Version is the current version of the Worker Deployment. All new workflow executions 
and tasks of existing unversioned or AutoUpgrade workflows are routed to this version.
"""
WORKER_DEPLOYMENT_VERSION_STATUS_RAMPING: WorkerDeploymentVersionStatus.ValueType  # 3
"""The Worker Deployment Version is the ramping version of the Worker Deployment. A subset of new Pinned workflow executions are 
routed to this version. Moreover, a portion of existing unversioned or AutoUpgrade workflow executions are also routed to this version.
"""
WORKER_DEPLOYMENT_VERSION_STATUS_DRAINING: WorkerDeploymentVersionStatus.ValueType  # 4
"""The Worker Deployment Version is not used by new workflows but is still used by
open pinned workflows. The version cannot be decommissioned safely.
"""
WORKER_DEPLOYMENT_VERSION_STATUS_DRAINED: WorkerDeploymentVersionStatus.ValueType  # 5
"""The Worker Deployment Version is not used by new or open workflows, but might be still needed by
Queries sent to closed workflows. The version can be decommissioned safely if user does
not query closed workflows. If the user does query closed workflows for some time x after
workflows are closed, they should decommission the version after it has been drained for that duration.
"""
global___WorkerDeploymentVersionStatus = WorkerDeploymentVersionStatus
