# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/deployment.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n&temporal/api/enums/v1/deployment.proto\x12\x15temporal.api.enums.v1*\xc4\x01\n\x16\x44\x65ploymentReachability\x12'\n#DEPLOYMENT_REACHABILITY_UNSPECIFIED\x10\x00\x12%\n!DEPLOYMENT_REACHABILITY_REACHABLE\x10\x01\x12\x31\n-DEPLOYMENT_REACHABILITY_CLOSED_WORKFLOWS_ONLY\x10\x02\x12'\n#DEPLOYMENT_REACHABILITY_UNREACHABLE\x10\x03*\x8b\x01\n\x15VersionDrainageStatus\x12'\n#VERSION_DRAINAGE_STATUS_UNSPECIFIED\x10\x00\x12$\n VERSION_DRAINAGE_STATUS_DRAINING\x10\x01\x12#\n\x1fVERSION_DRAINAGE_STATUS_DRAINED\x10\x02*\x8c\x01\n\x14WorkerVersioningMode\x12&\n\"WORKER_VERSIONING_MODE_UNSPECIFIED\x10\x00\x12&\n\"WORKER_VERSIONING_MODE_UNVERSIONED\x10\x01\x12$\n WORKER_VERSIONING_MODE_VERSIONED\x10\x02*\xb9\x02\n\x1dWorkerDeploymentVersionStatus\x12\x30\n,WORKER_DEPLOYMENT_VERSION_STATUS_UNSPECIFIED\x10\x00\x12-\n)WORKER_DEPLOYMENT_VERSION_STATUS_INACTIVE\x10\x01\x12,\n(WORKER_DEPLOYMENT_VERSION_STATUS_CURRENT\x10\x02\x12,\n(WORKER_DEPLOYMENT_VERSION_STATUS_RAMPING\x10\x03\x12-\n)WORKER_DEPLOYMENT_VERSION_STATUS_DRAINING\x10\x04\x12,\n(WORKER_DEPLOYMENT_VERSION_STATUS_DRAINED\x10\x05\x42\x87\x01\n\x18io.temporal.api.enums.v1B\x0f\x44\x65ploymentProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3"
)

_DEPLOYMENTREACHABILITY = DESCRIPTOR.enum_types_by_name["DeploymentReachability"]
DeploymentReachability = enum_type_wrapper.EnumTypeWrapper(_DEPLOYMENTREACHABILITY)
_VERSIONDRAINAGESTATUS = DESCRIPTOR.enum_types_by_name["VersionDrainageStatus"]
VersionDrainageStatus = enum_type_wrapper.EnumTypeWrapper(_VERSIONDRAINAGESTATUS)
_WORKERVERSIONINGMODE = DESCRIPTOR.enum_types_by_name["WorkerVersioningMode"]
WorkerVersioningMode = enum_type_wrapper.EnumTypeWrapper(_WORKERVERSIONINGMODE)
_WORKERDEPLOYMENTVERSIONSTATUS = DESCRIPTOR.enum_types_by_name[
    "WorkerDeploymentVersionStatus"
]
WorkerDeploymentVersionStatus = enum_type_wrapper.EnumTypeWrapper(
    _WORKERDEPLOYMENTVERSIONSTATUS
)
DEPLOYMENT_REACHABILITY_UNSPECIFIED = 0
DEPLOYMENT_REACHABILITY_REACHABLE = 1
DEPLOYMENT_REACHABILITY_CLOSED_WORKFLOWS_ONLY = 2
DEPLOYMENT_REACHABILITY_UNREACHABLE = 3
VERSION_DRAINAGE_STATUS_UNSPECIFIED = 0
VERSION_DRAINAGE_STATUS_DRAINING = 1
VERSION_DRAINAGE_STATUS_DRAINED = 2
WORKER_VERSIONING_MODE_UNSPECIFIED = 0
WORKER_VERSIONING_MODE_UNVERSIONED = 1
WORKER_VERSIONING_MODE_VERSIONED = 2
WORKER_DEPLOYMENT_VERSION_STATUS_UNSPECIFIED = 0
WORKER_DEPLOYMENT_VERSION_STATUS_INACTIVE = 1
WORKER_DEPLOYMENT_VERSION_STATUS_CURRENT = 2
WORKER_DEPLOYMENT_VERSION_STATUS_RAMPING = 3
WORKER_DEPLOYMENT_VERSION_STATUS_DRAINING = 4
WORKER_DEPLOYMENT_VERSION_STATUS_DRAINED = 5


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\017DeploymentProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _DEPLOYMENTREACHABILITY._serialized_start = 66
    _DEPLOYMENTREACHABILITY._serialized_end = 262
    _VERSIONDRAINAGESTATUS._serialized_start = 265
    _VERSIONDRAINAGESTATUS._serialized_end = 404
    _WORKERVERSIONINGMODE._serialized_start = 407
    _WORKERVERSIONINGMODE._serialized_end = 547
    _WORKERDEPLOYMENTVERSIONSTATUS._serialized_start = 550
    _WORKERDEPLOYMENTVERSIONSTATUS._serialized_end = 863
# @@protoc_insertion_point(module_scope)
