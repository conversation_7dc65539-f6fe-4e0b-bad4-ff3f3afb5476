"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _TaskQueueKind:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _TaskQueueKindEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _TaskQueueKind.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TASK_QUEUE_KIND_UNSPECIFIED: _TaskQueueKind.ValueType  # 0
    TASK_QUEUE_KIND_NORMAL: _TaskQueueKind.ValueType  # 1
    """Tasks from a normal workflow task queue always include complete workflow history

    The task queue specified by the user is always a normal task queue. There can be as many
    workers as desired for a single normal task queue. All those workers may pick up tasks from
    that queue.
    """
    TASK_QUEUE_KIND_STICKY: _TaskQueueKind.ValueType  # 2
    """A sticky queue only includes new history since the last workflow task, and they are
    per-worker.

    Sticky queues are created dynamically by each worker during their start up. They only exist
    for the lifetime of the worker process. Tasks in a sticky task queue are only available to
    the worker that created the sticky queue.

    Sticky queues are only for workflow tasks. There are no sticky task queues for activities.
    """

class TaskQueueKind(_TaskQueueKind, metaclass=_TaskQueueKindEnumTypeWrapper): ...

TASK_QUEUE_KIND_UNSPECIFIED: TaskQueueKind.ValueType  # 0
TASK_QUEUE_KIND_NORMAL: TaskQueueKind.ValueType  # 1
"""Tasks from a normal workflow task queue always include complete workflow history

The task queue specified by the user is always a normal task queue. There can be as many
workers as desired for a single normal task queue. All those workers may pick up tasks from
that queue.
"""
TASK_QUEUE_KIND_STICKY: TaskQueueKind.ValueType  # 2
"""A sticky queue only includes new history since the last workflow task, and they are
per-worker.

Sticky queues are created dynamically by each worker during their start up. They only exist
for the lifetime of the worker process. Tasks in a sticky task queue are only available to
the worker that created the sticky queue.

Sticky queues are only for workflow tasks. There are no sticky task queues for activities.
"""
global___TaskQueueKind = TaskQueueKind

class _TaskQueueType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _TaskQueueTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _TaskQueueType.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TASK_QUEUE_TYPE_UNSPECIFIED: _TaskQueueType.ValueType  # 0
    TASK_QUEUE_TYPE_WORKFLOW: _TaskQueueType.ValueType  # 1
    """Workflow type of task queue."""
    TASK_QUEUE_TYPE_ACTIVITY: _TaskQueueType.ValueType  # 2
    """Activity type of task queue."""
    TASK_QUEUE_TYPE_NEXUS: _TaskQueueType.ValueType  # 3
    """Task queue type for dispatching Nexus requests."""

class TaskQueueType(_TaskQueueType, metaclass=_TaskQueueTypeEnumTypeWrapper): ...

TASK_QUEUE_TYPE_UNSPECIFIED: TaskQueueType.ValueType  # 0
TASK_QUEUE_TYPE_WORKFLOW: TaskQueueType.ValueType  # 1
"""Workflow type of task queue."""
TASK_QUEUE_TYPE_ACTIVITY: TaskQueueType.ValueType  # 2
"""Activity type of task queue."""
TASK_QUEUE_TYPE_NEXUS: TaskQueueType.ValueType  # 3
"""Task queue type for dispatching Nexus requests."""
global___TaskQueueType = TaskQueueType

class _TaskReachability:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _TaskReachabilityEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _TaskReachability.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TASK_REACHABILITY_UNSPECIFIED: _TaskReachability.ValueType  # 0
    TASK_REACHABILITY_NEW_WORKFLOWS: _TaskReachability.ValueType  # 1
    """There's a possiblity for a worker to receive new workflow tasks. Workers should *not* be retired."""
    TASK_REACHABILITY_EXISTING_WORKFLOWS: _TaskReachability.ValueType  # 2
    """There's a possiblity for a worker to receive existing workflow and activity tasks from existing workflows. Workers
    should *not* be retired.
    This enum value does not distinguish between open and closed workflows.
    """
    TASK_REACHABILITY_OPEN_WORKFLOWS: _TaskReachability.ValueType  # 3
    """There's a possiblity for a worker to receive existing workflow and activity tasks from open workflows. Workers
    should *not* be retired.
    """
    TASK_REACHABILITY_CLOSED_WORKFLOWS: _TaskReachability.ValueType  # 4
    """There's a possiblity for a worker to receive existing workflow tasks from closed workflows. Workers may be
    retired dependending on application requirements. For example, if there's no need to query closed workflows.
    """

class TaskReachability(_TaskReachability, metaclass=_TaskReachabilityEnumTypeWrapper):
    """Specifies which category of tasks may reach a worker on a versioned task queue.
    Used both in a reachability query and its response.
    Deprecated.
    """

TASK_REACHABILITY_UNSPECIFIED: TaskReachability.ValueType  # 0
TASK_REACHABILITY_NEW_WORKFLOWS: TaskReachability.ValueType  # 1
"""There's a possiblity for a worker to receive new workflow tasks. Workers should *not* be retired."""
TASK_REACHABILITY_EXISTING_WORKFLOWS: TaskReachability.ValueType  # 2
"""There's a possiblity for a worker to receive existing workflow and activity tasks from existing workflows. Workers
should *not* be retired.
This enum value does not distinguish between open and closed workflows.
"""
TASK_REACHABILITY_OPEN_WORKFLOWS: TaskReachability.ValueType  # 3
"""There's a possiblity for a worker to receive existing workflow and activity tasks from open workflows. Workers
should *not* be retired.
"""
TASK_REACHABILITY_CLOSED_WORKFLOWS: TaskReachability.ValueType  # 4
"""There's a possiblity for a worker to receive existing workflow tasks from closed workflows. Workers may be
retired dependending on application requirements. For example, if there's no need to query closed workflows.
"""
global___TaskReachability = TaskReachability

class _BuildIdTaskReachability:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _BuildIdTaskReachabilityEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _BuildIdTaskReachability.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    BUILD_ID_TASK_REACHABILITY_UNSPECIFIED: _BuildIdTaskReachability.ValueType  # 0
    """Task reachability is not reported"""
    BUILD_ID_TASK_REACHABILITY_REACHABLE: _BuildIdTaskReachability.ValueType  # 1
    """Build ID may be used by new workflows or activities (base on versioning rules), or there MAY
    be open workflows or backlogged activities assigned to it.
    """
    BUILD_ID_TASK_REACHABILITY_CLOSED_WORKFLOWS_ONLY: (
        _BuildIdTaskReachability.ValueType
    )  # 2
    """Build ID does not have open workflows and is not reachable by new workflows,
    but MAY have closed workflows within the namespace retention period.
    Not applicable to activity-only task queues.
    """
    BUILD_ID_TASK_REACHABILITY_UNREACHABLE: _BuildIdTaskReachability.ValueType  # 3
    """Build ID is not used for new executions, nor it has been used by any existing execution
    within the retention period.
    """

class BuildIdTaskReachability(
    _BuildIdTaskReachability, metaclass=_BuildIdTaskReachabilityEnumTypeWrapper
):
    """Specifies which category of tasks may reach a versioned worker of a certain Build ID.

    Task Reachability is eventually consistent; there may be a delay (up to few minutes) until it
    converges to the most accurate value but it is designed in a way to take the more conservative
    side until it converges. For example REACHABLE is more conservative than CLOSED_WORKFLOWS_ONLY.

    Note: future activities who inherit their workflow's Build ID but not its Task Queue will not be
    accounted for reachability as server cannot know if they'll happen as they do not use
    assignment rules of their Task Queue. Same goes for Child Workflows or Continue-As-New Workflows
    who inherit the parent/previous workflow's Build ID but not its Task Queue. In those cases, make
    sure to query reachability for the parent/previous workflow's Task Queue as well.
    """

BUILD_ID_TASK_REACHABILITY_UNSPECIFIED: BuildIdTaskReachability.ValueType  # 0
"""Task reachability is not reported"""
BUILD_ID_TASK_REACHABILITY_REACHABLE: BuildIdTaskReachability.ValueType  # 1
"""Build ID may be used by new workflows or activities (base on versioning rules), or there MAY
be open workflows or backlogged activities assigned to it.
"""
BUILD_ID_TASK_REACHABILITY_CLOSED_WORKFLOWS_ONLY: BuildIdTaskReachability.ValueType  # 2
"""Build ID does not have open workflows and is not reachable by new workflows,
but MAY have closed workflows within the namespace retention period.
Not applicable to activity-only task queues.
"""
BUILD_ID_TASK_REACHABILITY_UNREACHABLE: BuildIdTaskReachability.ValueType  # 3
"""Build ID is not used for new executions, nor it has been used by any existing execution
within the retention period.
"""
global___BuildIdTaskReachability = BuildIdTaskReachability

class _DescribeTaskQueueMode:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _DescribeTaskQueueModeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _DescribeTaskQueueMode.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    DESCRIBE_TASK_QUEUE_MODE_UNSPECIFIED: _DescribeTaskQueueMode.ValueType  # 0
    """Unspecified means legacy behavior."""
    DESCRIBE_TASK_QUEUE_MODE_ENHANCED: _DescribeTaskQueueMode.ValueType  # 1
    """Enhanced mode reports aggregated results for all partitions, supports Build IDs, and reports richer info."""

class DescribeTaskQueueMode(
    _DescribeTaskQueueMode, metaclass=_DescribeTaskQueueModeEnumTypeWrapper
): ...

DESCRIBE_TASK_QUEUE_MODE_UNSPECIFIED: DescribeTaskQueueMode.ValueType  # 0
"""Unspecified means legacy behavior."""
DESCRIBE_TASK_QUEUE_MODE_ENHANCED: DescribeTaskQueueMode.ValueType  # 1
"""Enhanced mode reports aggregated results for all partitions, supports Build IDs, and reports richer info."""
global___DescribeTaskQueueMode = DescribeTaskQueueMode

class _RateLimitSource:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _RateLimitSourceEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _RateLimitSource.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RATE_LIMIT_SOURCE_UNSPECIFIED: _RateLimitSource.ValueType  # 0
    RATE_LIMIT_SOURCE_API: _RateLimitSource.ValueType  # 1
    """The value was set by the API."""
    RATE_LIMIT_SOURCE_WORKER: _RateLimitSource.ValueType  # 2
    """The value was set by a worker."""
    RATE_LIMIT_SOURCE_SYSTEM: _RateLimitSource.ValueType  # 3
    """The value was set as the system default."""

class RateLimitSource(_RateLimitSource, metaclass=_RateLimitSourceEnumTypeWrapper):
    """Source for the effective rate limit."""

RATE_LIMIT_SOURCE_UNSPECIFIED: RateLimitSource.ValueType  # 0
RATE_LIMIT_SOURCE_API: RateLimitSource.ValueType  # 1
"""The value was set by the API."""
RATE_LIMIT_SOURCE_WORKER: RateLimitSource.ValueType  # 2
"""The value was set by a worker."""
RATE_LIMIT_SOURCE_SYSTEM: RateLimitSource.ValueType  # 3
"""The value was set as the system default."""
global___RateLimitSource = RateLimitSource
