"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _UpdateWorkflowExecutionLifecycleStage:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _UpdateWorkflowExecutionLifecycleStageEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _UpdateWorkflowExecutionLifecycleStage.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_UNSPECIFIED: (
        _UpdateWorkflowExecutionLifecycleStage.ValueType
    )  # 0
    """An unspecified value for this enum."""
    UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ADMITTED: (
        _UpdateWorkflowExecutionLifecycleStage.ValueType
    )  # 1
    """The API call will not return until the Update request has been admitted
    by the server - it may be the case that due to a considerations like load
    or resource limits that an Update is made to wait before the server will
    indicate that it has been received and will be processed. This value
    does not wait for any sort of acknowledgement from a worker.
    """
    UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ACCEPTED: (
        _UpdateWorkflowExecutionLifecycleStage.ValueType
    )  # 2
    """The API call will not return until the Update has passed validation on a worker."""
    UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_COMPLETED: (
        _UpdateWorkflowExecutionLifecycleStage.ValueType
    )  # 3
    """The API call will not return until the Update has executed to completion
    on a worker and has either been rejected or returned a value or an error.
    """

class UpdateWorkflowExecutionLifecycleStage(
    _UpdateWorkflowExecutionLifecycleStage,
    metaclass=_UpdateWorkflowExecutionLifecycleStageEnumTypeWrapper,
):
    """UpdateWorkflowExecutionLifecycleStage is specified by clients invoking
    Workflow Updates and used to indicate to the server how long the
    client wishes to wait for a return value from the API. If any value other
    than UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_COMPLETED is sent by the
    client then the API will complete before the Update is finished and will
    return a handle to the running Update so that it can later be polled for
    completion.
    If specified stage wasn't reached before server timeout, server returns
    actual stage reached.
    """

UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_UNSPECIFIED: (
    UpdateWorkflowExecutionLifecycleStage.ValueType
)  # 0
"""An unspecified value for this enum."""
UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ADMITTED: (
    UpdateWorkflowExecutionLifecycleStage.ValueType
)  # 1
"""The API call will not return until the Update request has been admitted
by the server - it may be the case that due to a considerations like load
or resource limits that an Update is made to wait before the server will
indicate that it has been received and will be processed. This value
does not wait for any sort of acknowledgement from a worker.
"""
UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_ACCEPTED: (
    UpdateWorkflowExecutionLifecycleStage.ValueType
)  # 2
"""The API call will not return until the Update has passed validation on a worker."""
UPDATE_WORKFLOW_EXECUTION_LIFECYCLE_STAGE_COMPLETED: (
    UpdateWorkflowExecutionLifecycleStage.ValueType
)  # 3
"""The API call will not return until the Update has executed to completion
on a worker and has either been rejected or returned a value or an error.
"""
global___UpdateWorkflowExecutionLifecycleStage = UpdateWorkflowExecutionLifecycleStage

class _UpdateAdmittedEventOrigin:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _UpdateAdmittedEventOriginEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _UpdateAdmittedEventOrigin.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    UPDATE_ADMITTED_EVENT_ORIGIN_UNSPECIFIED: _UpdateAdmittedEventOrigin.ValueType  # 0
    UPDATE_ADMITTED_EVENT_ORIGIN_REAPPLY: _UpdateAdmittedEventOrigin.ValueType  # 1
    """The UpdateAdmitted event was created when reapplying events during reset
    or replication. I.e. an accepted Update on one branch of Workflow history
    was converted into an admitted Update on a different branch.
    """

class UpdateAdmittedEventOrigin(
    _UpdateAdmittedEventOrigin, metaclass=_UpdateAdmittedEventOriginEnumTypeWrapper
):
    """Records why a WorkflowExecutionUpdateAdmittedEvent was written to history.
    Note that not all admitted Updates result in this event.
    """

UPDATE_ADMITTED_EVENT_ORIGIN_UNSPECIFIED: UpdateAdmittedEventOrigin.ValueType  # 0
UPDATE_ADMITTED_EVENT_ORIGIN_REAPPLY: UpdateAdmittedEventOrigin.ValueType  # 1
"""The UpdateAdmitted event was created when reapplying events during reset
or replication. I.e. an accepted Update on one branch of Workflow history
was converted into an admitted Update on a different branch.
"""
global___UpdateAdmittedEventOrigin = UpdateAdmittedEventOrigin
