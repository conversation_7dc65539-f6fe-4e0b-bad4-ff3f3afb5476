"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _NexusHandlerErrorRetryBehavior:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _NexusHandlerErrorRetryBehaviorEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _NexusHandlerErrorRetryBehavior.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_UNSPECIFIED: (
        _NexusHandlerErrorRetryBehavior.ValueType
    )  # 0
    NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_RETRYABLE: (
        _NexusHandlerErrorRetryBehavior.ValueType
    )  # 1
    """A handler error is explicitly marked as retryable."""
    NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_NON_RETRYABLE: (
        _NexusHandlerErrorRetryBehavior.ValueType
    )  # 2
    """A handler error is explicitly marked as non-retryable."""

class NexusHandlerErrorRetryBehavior(
    _NexusHandlerErrorRetryBehavior,
    metaclass=_NexusHandlerErrorRetryBehaviorEnumTypeWrapper,
):
    """NexusHandlerErrorRetryBehavior allows nexus handlers to explicity set the retry behavior of a HandlerError. If not
    specified, retry behavior is determined from the error type. For example internal errors are not retryable by default
    unless specified otherwise.
    """

NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_UNSPECIFIED: (
    NexusHandlerErrorRetryBehavior.ValueType
)  # 0
NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_RETRYABLE: (
    NexusHandlerErrorRetryBehavior.ValueType
)  # 1
"""A handler error is explicitly marked as retryable."""
NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_NON_RETRYABLE: (
    NexusHandlerErrorRetryBehavior.ValueType
)  # 2
"""A handler error is explicitly marked as non-retryable."""
global___NexusHandlerErrorRetryBehavior = NexusHandlerErrorRetryBehavior
