# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/workflow.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n$temporal/api/enums/v1/workflow.proto\x12\x15temporal.api.enums.v1*\x8b\x02\n\x15WorkflowIdReusePolicy\x12(\n$WORKFLOW_ID_REUSE_POLICY_UNSPECIFIED\x10\x00\x12,\n(WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE\x10\x01\x12\x38\n4WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY\x10\x02\x12-\n)WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE\x10\x03\x12\x31\n-WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING\x10\x04*\xcf\x01\n\x18WorkflowIdConflictPolicy\x12+\n'WORKFLOW_ID_CONFLICT_POLICY_UNSPECIFIED\x10\x00\x12$\n WORKFLOW_ID_CONFLICT_POLICY_FAIL\x10\x01\x12,\n(WORKFLOW_ID_CONFLICT_POLICY_USE_EXISTING\x10\x02\x12\x32\n.WORKFLOW_ID_CONFLICT_POLICY_TERMINATE_EXISTING\x10\x03*\xa4\x01\n\x11ParentClosePolicy\x12#\n\x1fPARENT_CLOSE_POLICY_UNSPECIFIED\x10\x00\x12!\n\x1dPARENT_CLOSE_POLICY_TERMINATE\x10\x01\x12\x1f\n\x1bPARENT_CLOSE_POLICY_ABANDON\x10\x02\x12&\n\"PARENT_CLOSE_POLICY_REQUEST_CANCEL\x10\x03*\xbd\x01\n\x16\x43ontinueAsNewInitiator\x12)\n%CONTINUE_AS_NEW_INITIATOR_UNSPECIFIED\x10\x00\x12&\n\"CONTINUE_AS_NEW_INITIATOR_WORKFLOW\x10\x01\x12#\n\x1f\x43ONTINUE_AS_NEW_INITIATOR_RETRY\x10\x02\x12+\n'CONTINUE_AS_NEW_INITIATOR_CRON_SCHEDULE\x10\x03*\xe5\x02\n\x17WorkflowExecutionStatus\x12)\n%WORKFLOW_EXECUTION_STATUS_UNSPECIFIED\x10\x00\x12%\n!WORKFLOW_EXECUTION_STATUS_RUNNING\x10\x01\x12'\n#WORKFLOW_EXECUTION_STATUS_COMPLETED\x10\x02\x12$\n WORKFLOW_EXECUTION_STATUS_FAILED\x10\x03\x12&\n\"WORKFLOW_EXECUTION_STATUS_CANCELED\x10\x04\x12(\n$WORKFLOW_EXECUTION_STATUS_TERMINATED\x10\x05\x12.\n*WORKFLOW_EXECUTION_STATUS_CONTINUED_AS_NEW\x10\x06\x12'\n#WORKFLOW_EXECUTION_STATUS_TIMED_OUT\x10\x07*\x84\x02\n\x14PendingActivityState\x12&\n\"PENDING_ACTIVITY_STATE_UNSPECIFIED\x10\x00\x12$\n PENDING_ACTIVITY_STATE_SCHEDULED\x10\x01\x12\"\n\x1ePENDING_ACTIVITY_STATE_STARTED\x10\x02\x12+\n'PENDING_ACTIVITY_STATE_CANCEL_REQUESTED\x10\x03\x12!\n\x1dPENDING_ACTIVITY_STATE_PAUSED\x10\x04\x12*\n&PENDING_ACTIVITY_STATE_PAUSE_REQUESTED\x10\x05*\x9b\x01\n\x18PendingWorkflowTaskState\x12+\n'PENDING_WORKFLOW_TASK_STATE_UNSPECIFIED\x10\x00\x12)\n%PENDING_WORKFLOW_TASK_STATE_SCHEDULED\x10\x01\x12'\n#PENDING_WORKFLOW_TASK_STATE_STARTED\x10\x02*\x97\x01\n\x16HistoryEventFilterType\x12)\n%HISTORY_EVENT_FILTER_TYPE_UNSPECIFIED\x10\x00\x12'\n#HISTORY_EVENT_FILTER_TYPE_ALL_EVENT\x10\x01\x12)\n%HISTORY_EVENT_FILTER_TYPE_CLOSE_EVENT\x10\x02*\x9f\x02\n\nRetryState\x12\x1b\n\x17RETRY_STATE_UNSPECIFIED\x10\x00\x12\x1b\n\x17RETRY_STATE_IN_PROGRESS\x10\x01\x12%\n!RETRY_STATE_NON_RETRYABLE_FAILURE\x10\x02\x12\x17\n\x13RETRY_STATE_TIMEOUT\x10\x03\x12(\n$RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED\x10\x04\x12$\n RETRY_STATE_RETRY_POLICY_NOT_SET\x10\x05\x12%\n!RETRY_STATE_INTERNAL_SERVER_ERROR\x10\x06\x12 \n\x1cRETRY_STATE_CANCEL_REQUESTED\x10\x07*\xb0\x01\n\x0bTimeoutType\x12\x1c\n\x18TIMEOUT_TYPE_UNSPECIFIED\x10\x00\x12\x1f\n\x1bTIMEOUT_TYPE_START_TO_CLOSE\x10\x01\x12\"\n\x1eTIMEOUT_TYPE_SCHEDULE_TO_START\x10\x02\x12\"\n\x1eTIMEOUT_TYPE_SCHEDULE_TO_CLOSE\x10\x03\x12\x1a\n\x16TIMEOUT_TYPE_HEARTBEAT\x10\x04*\x7f\n\x12VersioningBehavior\x12#\n\x1fVERSIONING_BEHAVIOR_UNSPECIFIED\x10\x00\x12\x1e\n\x1aVERSIONING_BEHAVIOR_PINNED\x10\x01\x12$\n VERSIONING_BEHAVIOR_AUTO_UPGRADE\x10\x02\x42\x85\x01\n\x18io.temporal.api.enums.v1B\rWorkflowProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3"
)

_WORKFLOWIDREUSEPOLICY = DESCRIPTOR.enum_types_by_name["WorkflowIdReusePolicy"]
WorkflowIdReusePolicy = enum_type_wrapper.EnumTypeWrapper(_WORKFLOWIDREUSEPOLICY)
_WORKFLOWIDCONFLICTPOLICY = DESCRIPTOR.enum_types_by_name["WorkflowIdConflictPolicy"]
WorkflowIdConflictPolicy = enum_type_wrapper.EnumTypeWrapper(_WORKFLOWIDCONFLICTPOLICY)
_PARENTCLOSEPOLICY = DESCRIPTOR.enum_types_by_name["ParentClosePolicy"]
ParentClosePolicy = enum_type_wrapper.EnumTypeWrapper(_PARENTCLOSEPOLICY)
_CONTINUEASNEWINITIATOR = DESCRIPTOR.enum_types_by_name["ContinueAsNewInitiator"]
ContinueAsNewInitiator = enum_type_wrapper.EnumTypeWrapper(_CONTINUEASNEWINITIATOR)
_WORKFLOWEXECUTIONSTATUS = DESCRIPTOR.enum_types_by_name["WorkflowExecutionStatus"]
WorkflowExecutionStatus = enum_type_wrapper.EnumTypeWrapper(_WORKFLOWEXECUTIONSTATUS)
_PENDINGACTIVITYSTATE = DESCRIPTOR.enum_types_by_name["PendingActivityState"]
PendingActivityState = enum_type_wrapper.EnumTypeWrapper(_PENDINGACTIVITYSTATE)
_PENDINGWORKFLOWTASKSTATE = DESCRIPTOR.enum_types_by_name["PendingWorkflowTaskState"]
PendingWorkflowTaskState = enum_type_wrapper.EnumTypeWrapper(_PENDINGWORKFLOWTASKSTATE)
_HISTORYEVENTFILTERTYPE = DESCRIPTOR.enum_types_by_name["HistoryEventFilterType"]
HistoryEventFilterType = enum_type_wrapper.EnumTypeWrapper(_HISTORYEVENTFILTERTYPE)
_RETRYSTATE = DESCRIPTOR.enum_types_by_name["RetryState"]
RetryState = enum_type_wrapper.EnumTypeWrapper(_RETRYSTATE)
_TIMEOUTTYPE = DESCRIPTOR.enum_types_by_name["TimeoutType"]
TimeoutType = enum_type_wrapper.EnumTypeWrapper(_TIMEOUTTYPE)
_VERSIONINGBEHAVIOR = DESCRIPTOR.enum_types_by_name["VersioningBehavior"]
VersioningBehavior = enum_type_wrapper.EnumTypeWrapper(_VERSIONINGBEHAVIOR)
WORKFLOW_ID_REUSE_POLICY_UNSPECIFIED = 0
WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE = 1
WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY = 2
WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE = 3
WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING = 4
WORKFLOW_ID_CONFLICT_POLICY_UNSPECIFIED = 0
WORKFLOW_ID_CONFLICT_POLICY_FAIL = 1
WORKFLOW_ID_CONFLICT_POLICY_USE_EXISTING = 2
WORKFLOW_ID_CONFLICT_POLICY_TERMINATE_EXISTING = 3
PARENT_CLOSE_POLICY_UNSPECIFIED = 0
PARENT_CLOSE_POLICY_TERMINATE = 1
PARENT_CLOSE_POLICY_ABANDON = 2
PARENT_CLOSE_POLICY_REQUEST_CANCEL = 3
CONTINUE_AS_NEW_INITIATOR_UNSPECIFIED = 0
CONTINUE_AS_NEW_INITIATOR_WORKFLOW = 1
CONTINUE_AS_NEW_INITIATOR_RETRY = 2
CONTINUE_AS_NEW_INITIATOR_CRON_SCHEDULE = 3
WORKFLOW_EXECUTION_STATUS_UNSPECIFIED = 0
WORKFLOW_EXECUTION_STATUS_RUNNING = 1
WORKFLOW_EXECUTION_STATUS_COMPLETED = 2
WORKFLOW_EXECUTION_STATUS_FAILED = 3
WORKFLOW_EXECUTION_STATUS_CANCELED = 4
WORKFLOW_EXECUTION_STATUS_TERMINATED = 5
WORKFLOW_EXECUTION_STATUS_CONTINUED_AS_NEW = 6
WORKFLOW_EXECUTION_STATUS_TIMED_OUT = 7
PENDING_ACTIVITY_STATE_UNSPECIFIED = 0
PENDING_ACTIVITY_STATE_SCHEDULED = 1
PENDING_ACTIVITY_STATE_STARTED = 2
PENDING_ACTIVITY_STATE_CANCEL_REQUESTED = 3
PENDING_ACTIVITY_STATE_PAUSED = 4
PENDING_ACTIVITY_STATE_PAUSE_REQUESTED = 5
PENDING_WORKFLOW_TASK_STATE_UNSPECIFIED = 0
PENDING_WORKFLOW_TASK_STATE_SCHEDULED = 1
PENDING_WORKFLOW_TASK_STATE_STARTED = 2
HISTORY_EVENT_FILTER_TYPE_UNSPECIFIED = 0
HISTORY_EVENT_FILTER_TYPE_ALL_EVENT = 1
HISTORY_EVENT_FILTER_TYPE_CLOSE_EVENT = 2
RETRY_STATE_UNSPECIFIED = 0
RETRY_STATE_IN_PROGRESS = 1
RETRY_STATE_NON_RETRYABLE_FAILURE = 2
RETRY_STATE_TIMEOUT = 3
RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED = 4
RETRY_STATE_RETRY_POLICY_NOT_SET = 5
RETRY_STATE_INTERNAL_SERVER_ERROR = 6
RETRY_STATE_CANCEL_REQUESTED = 7
TIMEOUT_TYPE_UNSPECIFIED = 0
TIMEOUT_TYPE_START_TO_CLOSE = 1
TIMEOUT_TYPE_SCHEDULE_TO_START = 2
TIMEOUT_TYPE_SCHEDULE_TO_CLOSE = 3
TIMEOUT_TYPE_HEARTBEAT = 4
VERSIONING_BEHAVIOR_UNSPECIFIED = 0
VERSIONING_BEHAVIOR_PINNED = 1
VERSIONING_BEHAVIOR_AUTO_UPGRADE = 2


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\rWorkflowProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _WORKFLOWIDREUSEPOLICY._serialized_start = 64
    _WORKFLOWIDREUSEPOLICY._serialized_end = 331
    _WORKFLOWIDCONFLICTPOLICY._serialized_start = 334
    _WORKFLOWIDCONFLICTPOLICY._serialized_end = 541
    _PARENTCLOSEPOLICY._serialized_start = 544
    _PARENTCLOSEPOLICY._serialized_end = 708
    _CONTINUEASNEWINITIATOR._serialized_start = 711
    _CONTINUEASNEWINITIATOR._serialized_end = 900
    _WORKFLOWEXECUTIONSTATUS._serialized_start = 903
    _WORKFLOWEXECUTIONSTATUS._serialized_end = 1260
    _PENDINGACTIVITYSTATE._serialized_start = 1263
    _PENDINGACTIVITYSTATE._serialized_end = 1523
    _PENDINGWORKFLOWTASKSTATE._serialized_start = 1526
    _PENDINGWORKFLOWTASKSTATE._serialized_end = 1681
    _HISTORYEVENTFILTERTYPE._serialized_start = 1684
    _HISTORYEVENTFILTERTYPE._serialized_end = 1835
    _RETRYSTATE._serialized_start = 1838
    _RETRYSTATE._serialized_end = 2125
    _TIMEOUTTYPE._serialized_start = 2128
    _TIMEOUTTYPE._serialized_end = 2304
    _VERSIONINGBEHAVIOR._serialized_start = 2306
    _VERSIONINGBEHAVIOR._serialized_end = 2433
# @@protoc_insertion_point(module_scope)
