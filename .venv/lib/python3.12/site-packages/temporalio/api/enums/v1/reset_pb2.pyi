"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _ResetReapplyExcludeType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ResetReapplyExcludeTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _ResetReapplyExcludeType.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RESET_REAPPLY_EXCLUDE_TYPE_UNSPECIFIED: _ResetReapplyExcludeType.ValueType  # 0
    RESET_REAPPLY_EXCLUDE_TYPE_SIGNAL: _ResetReapplyExcludeType.ValueType  # 1
    """Exclude signals when reapplying events beyond the reset point."""
    RESET_REAPPLY_EXCLUDE_TYPE_UPDATE: _ResetReapplyExcludeType.ValueType  # 2
    """Exclude updates when reapplying events beyond the reset point."""
    RESET_REAPPLY_EXCLUDE_TYPE_NEXUS: _ResetReapplyExcludeType.ValueType  # 3
    """Exclude nexus events when reapplying events beyond the reset point."""
    RESET_REAPPLY_EXCLUDE_TYPE_CANCEL_REQUEST: _ResetReapplyExcludeType.ValueType  # 4
    """Deprecated, unimplemented option."""

class ResetReapplyExcludeType(
    _ResetReapplyExcludeType, metaclass=_ResetReapplyExcludeTypeEnumTypeWrapper
):
    """Event types to exclude when reapplying events beyond the reset point."""

RESET_REAPPLY_EXCLUDE_TYPE_UNSPECIFIED: ResetReapplyExcludeType.ValueType  # 0
RESET_REAPPLY_EXCLUDE_TYPE_SIGNAL: ResetReapplyExcludeType.ValueType  # 1
"""Exclude signals when reapplying events beyond the reset point."""
RESET_REAPPLY_EXCLUDE_TYPE_UPDATE: ResetReapplyExcludeType.ValueType  # 2
"""Exclude updates when reapplying events beyond the reset point."""
RESET_REAPPLY_EXCLUDE_TYPE_NEXUS: ResetReapplyExcludeType.ValueType  # 3
"""Exclude nexus events when reapplying events beyond the reset point."""
RESET_REAPPLY_EXCLUDE_TYPE_CANCEL_REQUEST: ResetReapplyExcludeType.ValueType  # 4
"""Deprecated, unimplemented option."""
global___ResetReapplyExcludeType = ResetReapplyExcludeType

class _ResetReapplyType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ResetReapplyTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _ResetReapplyType.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RESET_REAPPLY_TYPE_UNSPECIFIED: _ResetReapplyType.ValueType  # 0
    RESET_REAPPLY_TYPE_SIGNAL: _ResetReapplyType.ValueType  # 1
    """Signals are reapplied when workflow is reset."""
    RESET_REAPPLY_TYPE_NONE: _ResetReapplyType.ValueType  # 2
    """No events are reapplied when workflow is reset."""
    RESET_REAPPLY_TYPE_ALL_ELIGIBLE: _ResetReapplyType.ValueType  # 3
    """All eligible events are reapplied when workflow is reset."""

class ResetReapplyType(_ResetReapplyType, metaclass=_ResetReapplyTypeEnumTypeWrapper):
    """Deprecated: applications should use ResetReapplyExcludeType to specify
    exclusions from this set, and new event types should be added to ResetReapplyExcludeType
    instead of here.
    """

RESET_REAPPLY_TYPE_UNSPECIFIED: ResetReapplyType.ValueType  # 0
RESET_REAPPLY_TYPE_SIGNAL: ResetReapplyType.ValueType  # 1
"""Signals are reapplied when workflow is reset."""
RESET_REAPPLY_TYPE_NONE: ResetReapplyType.ValueType  # 2
"""No events are reapplied when workflow is reset."""
RESET_REAPPLY_TYPE_ALL_ELIGIBLE: ResetReapplyType.ValueType  # 3
"""All eligible events are reapplied when workflow is reset."""
global___ResetReapplyType = ResetReapplyType

class _ResetType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ResetTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_ResetType.ValueType],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RESET_TYPE_UNSPECIFIED: _ResetType.ValueType  # 0
    RESET_TYPE_FIRST_WORKFLOW_TASK: _ResetType.ValueType  # 1
    """Resets to event of the first workflow task completed, or if it does not exist, the event after task scheduled."""
    RESET_TYPE_LAST_WORKFLOW_TASK: _ResetType.ValueType  # 2
    """Resets to event of the last workflow task completed, or if it does not exist, the event after task scheduled."""

class ResetType(_ResetType, metaclass=_ResetTypeEnumTypeWrapper):
    """Deprecated, see temporalio.api.common.v1.ResetOptions."""

RESET_TYPE_UNSPECIFIED: ResetType.ValueType  # 0
RESET_TYPE_FIRST_WORKFLOW_TASK: ResetType.ValueType  # 1
"""Resets to event of the first workflow task completed, or if it does not exist, the event after task scheduled."""
RESET_TYPE_LAST_WORKFLOW_TASK: ResetType.ValueType  # 2
"""Resets to event of the last workflow task completed, or if it does not exist, the event after task scheduled."""
global___ResetType = ResetType
