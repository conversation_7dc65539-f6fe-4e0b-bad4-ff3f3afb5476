# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/failed_cause.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n(temporal/api/enums/v1/failed_cause.proto\x12\x15temporal.api.enums.v1*\xa5\x12\n\x17WorkflowTaskFailedCause\x12*\n&WORKFLOW_TASK_FAILED_CAUSE_UNSPECIFIED\x10\x00\x12\x30\n,WORKFLOW_TASK_FAILED_CAUSE_UNHANDLED_COMMAND\x10\x01\x12?\n;WORKFLOW_TASK_FAILED_CAUSE_BAD_SCHEDULE_ACTIVITY_ATTRIBUTES\x10\x02\x12\x45\nAWORKFLOW_TASK_FAILED_CAUSE_BAD_REQUEST_CANCEL_ACTIVITY_ATTRIBUTES\x10\x03\x12\x39\n5WORKFLOW_TASK_FAILED_CAUSE_BAD_START_TIMER_ATTRIBUTES\x10\x04\x12:\n6WORKFLOW_TASK_FAILED_CAUSE_BAD_CANCEL_TIMER_ATTRIBUTES\x10\x05\x12;\n7WORKFLOW_TASK_FAILED_CAUSE_BAD_RECORD_MARKER_ATTRIBUTES\x10\x06\x12I\nEWORKFLOW_TASK_FAILED_CAUSE_BAD_COMPLETE_WORKFLOW_EXECUTION_ATTRIBUTES\x10\x07\x12\x45\nAWORKFLOW_TASK_FAILED_CAUSE_BAD_FAIL_WORKFLOW_EXECUTION_ATTRIBUTES\x10\x08\x12G\nCWORKFLOW_TASK_FAILED_CAUSE_BAD_CANCEL_WORKFLOW_EXECUTION_ATTRIBUTES\x10\t\x12X\nTWORKFLOW_TASK_FAILED_CAUSE_BAD_REQUEST_CANCEL_EXTERNAL_WORKFLOW_EXECUTION_ATTRIBUTES\x10\n\x12=\n9WORKFLOW_TASK_FAILED_CAUSE_BAD_CONTINUE_AS_NEW_ATTRIBUTES\x10\x0b\x12\x37\n3WORKFLOW_TASK_FAILED_CAUSE_START_TIMER_DUPLICATE_ID\x10\x0c\x12\x36\n2WORKFLOW_TASK_FAILED_CAUSE_RESET_STICKY_TASK_QUEUE\x10\r\x12@\n<WORKFLOW_TASK_FAILED_CAUSE_WORKFLOW_WORKER_UNHANDLED_FAILURE\x10\x0e\x12G\nCWORKFLOW_TASK_FAILED_CAUSE_BAD_SIGNAL_WORKFLOW_EXECUTION_ATTRIBUTES\x10\x0f\x12\x43\n?WORKFLOW_TASK_FAILED_CAUSE_BAD_START_CHILD_EXECUTION_ATTRIBUTES\x10\x10\x12\x32\n.WORKFLOW_TASK_FAILED_CAUSE_FORCE_CLOSE_COMMAND\x10\x11\x12\x35\n1WORKFLOW_TASK_FAILED_CAUSE_FAILOVER_CLOSE_COMMAND\x10\x12\x12\x34\n0WORKFLOW_TASK_FAILED_CAUSE_BAD_SIGNAL_INPUT_SIZE\x10\x13\x12-\n)WORKFLOW_TASK_FAILED_CAUSE_RESET_WORKFLOW\x10\x14\x12)\n%WORKFLOW_TASK_FAILED_CAUSE_BAD_BINARY\x10\x15\x12=\n9WORKFLOW_TASK_FAILED_CAUSE_SCHEDULE_ACTIVITY_DUPLICATE_ID\x10\x16\x12\x34\n0WORKFLOW_TASK_FAILED_CAUSE_BAD_SEARCH_ATTRIBUTES\x10\x17\x12\x36\n2WORKFLOW_TASK_FAILED_CAUSE_NON_DETERMINISTIC_ERROR\x10\x18\x12H\nDWORKFLOW_TASK_FAILED_CAUSE_BAD_MODIFY_WORKFLOW_PROPERTIES_ATTRIBUTES\x10\x19\x12\x45\nAWORKFLOW_TASK_FAILED_CAUSE_PENDING_CHILD_WORKFLOWS_LIMIT_EXCEEDED\x10\x1a\x12@\n<WORKFLOW_TASK_FAILED_CAUSE_PENDING_ACTIVITIES_LIMIT_EXCEEDED\x10\x1b\x12=\n9WORKFLOW_TASK_FAILED_CAUSE_PENDING_SIGNALS_LIMIT_EXCEEDED\x10\x1c\x12\x44\n@WORKFLOW_TASK_FAILED_CAUSE_PENDING_REQUEST_CANCEL_LIMIT_EXCEEDED\x10\x1d\x12\x44\n@WORKFLOW_TASK_FAILED_CAUSE_BAD_UPDATE_WORKFLOW_EXECUTION_MESSAGE\x10\x1e\x12/\n+WORKFLOW_TASK_FAILED_CAUSE_UNHANDLED_UPDATE\x10\x1f\x12\x46\nBWORKFLOW_TASK_FAILED_CAUSE_BAD_SCHEDULE_NEXUS_OPERATION_ATTRIBUTES\x10 \x12\x46\nBWORKFLOW_TASK_FAILED_CAUSE_PENDING_NEXUS_OPERATIONS_LIMIT_EXCEEDED\x10!\x12L\nHWORKFLOW_TASK_FAILED_CAUSE_BAD_REQUEST_CANCEL_NEXUS_OPERATION_ATTRIBUTES\x10"\x12/\n+WORKFLOW_TASK_FAILED_CAUSE_FEATURE_DISABLED\x10#\x12\x35\n1WORKFLOW_TASK_FAILED_CAUSE_GRPC_MESSAGE_TOO_LARGE\x10$*\xf3\x01\n&StartChildWorkflowExecutionFailedCause\x12;\n7START_CHILD_WORKFLOW_EXECUTION_FAILED_CAUSE_UNSPECIFIED\x10\x00\x12G\nCSTART_CHILD_WORKFLOW_EXECUTION_FAILED_CAUSE_WORKFLOW_ALREADY_EXISTS\x10\x01\x12\x43\n?START_CHILD_WORKFLOW_EXECUTION_FAILED_CAUSE_NAMESPACE_NOT_FOUND\x10\x02*\x91\x02\n*CancelExternalWorkflowExecutionFailedCause\x12?\n;CANCEL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_UNSPECIFIED\x10\x00\x12Y\nUCANCEL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_EXTERNAL_WORKFLOW_EXECUTION_NOT_FOUND\x10\x01\x12G\nCCANCEL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_NAMESPACE_NOT_FOUND\x10\x02*\xe2\x02\n*SignalExternalWorkflowExecutionFailedCause\x12?\n;SIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_UNSPECIFIED\x10\x00\x12Y\nUSIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_EXTERNAL_WORKFLOW_EXECUTION_NOT_FOUND\x10\x01\x12G\nCSIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_NAMESPACE_NOT_FOUND\x10\x02\x12O\nKSIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_SIGNAL_COUNT_LIMIT_EXCEEDED\x10\x03*\xe0\x03\n\x16ResourceExhaustedCause\x12(\n$RESOURCE_EXHAUSTED_CAUSE_UNSPECIFIED\x10\x00\x12&\n"RESOURCE_EXHAUSTED_CAUSE_RPS_LIMIT\x10\x01\x12-\n)RESOURCE_EXHAUSTED_CAUSE_CONCURRENT_LIMIT\x10\x02\x12.\n*RESOURCE_EXHAUSTED_CAUSE_SYSTEM_OVERLOADED\x10\x03\x12.\n*RESOURCE_EXHAUSTED_CAUSE_PERSISTENCE_LIMIT\x10\x04\x12*\n&RESOURCE_EXHAUSTED_CAUSE_BUSY_WORKFLOW\x10\x05\x12&\n"RESOURCE_EXHAUSTED_CAUSE_APS_LIMIT\x10\x06\x12\x36\n2RESOURCE_EXHAUSTED_CAUSE_PERSISTENCE_STORAGE_LIMIT\x10\x07\x12\x31\n-RESOURCE_EXHAUSTED_CAUSE_CIRCUIT_BREAKER_OPEN\x10\x08\x12&\n"RESOURCE_EXHAUSTED_CAUSE_OPS_LIMIT\x10\t*\x8f\x01\n\x16ResourceExhaustedScope\x12(\n$RESOURCE_EXHAUSTED_SCOPE_UNSPECIFIED\x10\x00\x12&\n"RESOURCE_EXHAUSTED_SCOPE_NAMESPACE\x10\x01\x12#\n\x1fRESOURCE_EXHAUSTED_SCOPE_SYSTEM\x10\x02\x42\x88\x01\n\x18io.temporal.api.enums.v1B\x10\x46\x61iledCauseProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3'
)

_WORKFLOWTASKFAILEDCAUSE = DESCRIPTOR.enum_types_by_name["WorkflowTaskFailedCause"]
WorkflowTaskFailedCause = enum_type_wrapper.EnumTypeWrapper(_WORKFLOWTASKFAILEDCAUSE)
_STARTCHILDWORKFLOWEXECUTIONFAILEDCAUSE = DESCRIPTOR.enum_types_by_name[
    "StartChildWorkflowExecutionFailedCause"
]
StartChildWorkflowExecutionFailedCause = enum_type_wrapper.EnumTypeWrapper(
    _STARTCHILDWORKFLOWEXECUTIONFAILEDCAUSE
)
_CANCELEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE = DESCRIPTOR.enum_types_by_name[
    "CancelExternalWorkflowExecutionFailedCause"
]
CancelExternalWorkflowExecutionFailedCause = enum_type_wrapper.EnumTypeWrapper(
    _CANCELEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE
)
_SIGNALEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE = DESCRIPTOR.enum_types_by_name[
    "SignalExternalWorkflowExecutionFailedCause"
]
SignalExternalWorkflowExecutionFailedCause = enum_type_wrapper.EnumTypeWrapper(
    _SIGNALEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE
)
_RESOURCEEXHAUSTEDCAUSE = DESCRIPTOR.enum_types_by_name["ResourceExhaustedCause"]
ResourceExhaustedCause = enum_type_wrapper.EnumTypeWrapper(_RESOURCEEXHAUSTEDCAUSE)
_RESOURCEEXHAUSTEDSCOPE = DESCRIPTOR.enum_types_by_name["ResourceExhaustedScope"]
ResourceExhaustedScope = enum_type_wrapper.EnumTypeWrapper(_RESOURCEEXHAUSTEDSCOPE)
WORKFLOW_TASK_FAILED_CAUSE_UNSPECIFIED = 0
WORKFLOW_TASK_FAILED_CAUSE_UNHANDLED_COMMAND = 1
WORKFLOW_TASK_FAILED_CAUSE_BAD_SCHEDULE_ACTIVITY_ATTRIBUTES = 2
WORKFLOW_TASK_FAILED_CAUSE_BAD_REQUEST_CANCEL_ACTIVITY_ATTRIBUTES = 3
WORKFLOW_TASK_FAILED_CAUSE_BAD_START_TIMER_ATTRIBUTES = 4
WORKFLOW_TASK_FAILED_CAUSE_BAD_CANCEL_TIMER_ATTRIBUTES = 5
WORKFLOW_TASK_FAILED_CAUSE_BAD_RECORD_MARKER_ATTRIBUTES = 6
WORKFLOW_TASK_FAILED_CAUSE_BAD_COMPLETE_WORKFLOW_EXECUTION_ATTRIBUTES = 7
WORKFLOW_TASK_FAILED_CAUSE_BAD_FAIL_WORKFLOW_EXECUTION_ATTRIBUTES = 8
WORKFLOW_TASK_FAILED_CAUSE_BAD_CANCEL_WORKFLOW_EXECUTION_ATTRIBUTES = 9
WORKFLOW_TASK_FAILED_CAUSE_BAD_REQUEST_CANCEL_EXTERNAL_WORKFLOW_EXECUTION_ATTRIBUTES = (
    10
)
WORKFLOW_TASK_FAILED_CAUSE_BAD_CONTINUE_AS_NEW_ATTRIBUTES = 11
WORKFLOW_TASK_FAILED_CAUSE_START_TIMER_DUPLICATE_ID = 12
WORKFLOW_TASK_FAILED_CAUSE_RESET_STICKY_TASK_QUEUE = 13
WORKFLOW_TASK_FAILED_CAUSE_WORKFLOW_WORKER_UNHANDLED_FAILURE = 14
WORKFLOW_TASK_FAILED_CAUSE_BAD_SIGNAL_WORKFLOW_EXECUTION_ATTRIBUTES = 15
WORKFLOW_TASK_FAILED_CAUSE_BAD_START_CHILD_EXECUTION_ATTRIBUTES = 16
WORKFLOW_TASK_FAILED_CAUSE_FORCE_CLOSE_COMMAND = 17
WORKFLOW_TASK_FAILED_CAUSE_FAILOVER_CLOSE_COMMAND = 18
WORKFLOW_TASK_FAILED_CAUSE_BAD_SIGNAL_INPUT_SIZE = 19
WORKFLOW_TASK_FAILED_CAUSE_RESET_WORKFLOW = 20
WORKFLOW_TASK_FAILED_CAUSE_BAD_BINARY = 21
WORKFLOW_TASK_FAILED_CAUSE_SCHEDULE_ACTIVITY_DUPLICATE_ID = 22
WORKFLOW_TASK_FAILED_CAUSE_BAD_SEARCH_ATTRIBUTES = 23
WORKFLOW_TASK_FAILED_CAUSE_NON_DETERMINISTIC_ERROR = 24
WORKFLOW_TASK_FAILED_CAUSE_BAD_MODIFY_WORKFLOW_PROPERTIES_ATTRIBUTES = 25
WORKFLOW_TASK_FAILED_CAUSE_PENDING_CHILD_WORKFLOWS_LIMIT_EXCEEDED = 26
WORKFLOW_TASK_FAILED_CAUSE_PENDING_ACTIVITIES_LIMIT_EXCEEDED = 27
WORKFLOW_TASK_FAILED_CAUSE_PENDING_SIGNALS_LIMIT_EXCEEDED = 28
WORKFLOW_TASK_FAILED_CAUSE_PENDING_REQUEST_CANCEL_LIMIT_EXCEEDED = 29
WORKFLOW_TASK_FAILED_CAUSE_BAD_UPDATE_WORKFLOW_EXECUTION_MESSAGE = 30
WORKFLOW_TASK_FAILED_CAUSE_UNHANDLED_UPDATE = 31
WORKFLOW_TASK_FAILED_CAUSE_BAD_SCHEDULE_NEXUS_OPERATION_ATTRIBUTES = 32
WORKFLOW_TASK_FAILED_CAUSE_PENDING_NEXUS_OPERATIONS_LIMIT_EXCEEDED = 33
WORKFLOW_TASK_FAILED_CAUSE_BAD_REQUEST_CANCEL_NEXUS_OPERATION_ATTRIBUTES = 34
WORKFLOW_TASK_FAILED_CAUSE_FEATURE_DISABLED = 35
WORKFLOW_TASK_FAILED_CAUSE_GRPC_MESSAGE_TOO_LARGE = 36
START_CHILD_WORKFLOW_EXECUTION_FAILED_CAUSE_UNSPECIFIED = 0
START_CHILD_WORKFLOW_EXECUTION_FAILED_CAUSE_WORKFLOW_ALREADY_EXISTS = 1
START_CHILD_WORKFLOW_EXECUTION_FAILED_CAUSE_NAMESPACE_NOT_FOUND = 2
CANCEL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_UNSPECIFIED = 0
CANCEL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_EXTERNAL_WORKFLOW_EXECUTION_NOT_FOUND = 1
CANCEL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_NAMESPACE_NOT_FOUND = 2
SIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_UNSPECIFIED = 0
SIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_EXTERNAL_WORKFLOW_EXECUTION_NOT_FOUND = 1
SIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_NAMESPACE_NOT_FOUND = 2
SIGNAL_EXTERNAL_WORKFLOW_EXECUTION_FAILED_CAUSE_SIGNAL_COUNT_LIMIT_EXCEEDED = 3
RESOURCE_EXHAUSTED_CAUSE_UNSPECIFIED = 0
RESOURCE_EXHAUSTED_CAUSE_RPS_LIMIT = 1
RESOURCE_EXHAUSTED_CAUSE_CONCURRENT_LIMIT = 2
RESOURCE_EXHAUSTED_CAUSE_SYSTEM_OVERLOADED = 3
RESOURCE_EXHAUSTED_CAUSE_PERSISTENCE_LIMIT = 4
RESOURCE_EXHAUSTED_CAUSE_BUSY_WORKFLOW = 5
RESOURCE_EXHAUSTED_CAUSE_APS_LIMIT = 6
RESOURCE_EXHAUSTED_CAUSE_PERSISTENCE_STORAGE_LIMIT = 7
RESOURCE_EXHAUSTED_CAUSE_CIRCUIT_BREAKER_OPEN = 8
RESOURCE_EXHAUSTED_CAUSE_OPS_LIMIT = 9
RESOURCE_EXHAUSTED_SCOPE_UNSPECIFIED = 0
RESOURCE_EXHAUSTED_SCOPE_NAMESPACE = 1
RESOURCE_EXHAUSTED_SCOPE_SYSTEM = 2


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\020FailedCauseProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _WORKFLOWTASKFAILEDCAUSE._serialized_start = 68
    _WORKFLOWTASKFAILEDCAUSE._serialized_end = 2409
    _STARTCHILDWORKFLOWEXECUTIONFAILEDCAUSE._serialized_start = 2412
    _STARTCHILDWORKFLOWEXECUTIONFAILEDCAUSE._serialized_end = 2655
    _CANCELEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE._serialized_start = 2658
    _CANCELEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE._serialized_end = 2931
    _SIGNALEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE._serialized_start = 2934
    _SIGNALEXTERNALWORKFLOWEXECUTIONFAILEDCAUSE._serialized_end = 3288
    _RESOURCEEXHAUSTEDCAUSE._serialized_start = 3291
    _RESOURCEEXHAUSTEDCAUSE._serialized_end = 3771
    _RESOURCEEXHAUSTEDSCOPE._serialized_start = 3774
    _RESOURCEEXHAUSTEDSCOPE._serialized_end = 3917
# @@protoc_insertion_point(module_scope)
