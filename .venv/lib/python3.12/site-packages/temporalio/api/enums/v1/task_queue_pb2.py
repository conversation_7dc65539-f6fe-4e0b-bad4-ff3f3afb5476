# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/task_queue.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b'\n&temporal/api/enums/v1/task_queue.proto\x12\x15temporal.api.enums.v1*h\n\rTaskQueueKind\x12\x1f\n\x1bTASK_QUEUE_KIND_UNSPECIFIED\x10\x00\x12\x1a\n\x16TASK_QUEUE_KIND_NORMAL\x10\x01\x12\x1a\n\x16TASK_QUEUE_KIND_STICKY\x10\x02*\x87\x01\n\rTaskQueueType\x12\x1f\n\x1bTASK_QUEUE_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n\x18TASK_QUEUE_TYPE_WORKFLOW\x10\x01\x12\x1c\n\x18TASK_QUEUE_TYPE_ACTIVITY\x10\x02\x12\x19\n\x15TASK_QUEUE_TYPE_NEXUS\x10\x03*\xd2\x01\n\x10TaskReachability\x12!\n\x1dTASK_REACHABILITY_UNSPECIFIED\x10\x00\x12#\n\x1fTASK_REACHABILITY_NEW_WORKFLOWS\x10\x01\x12(\n$TASK_REACHABILITY_EXISTING_WORKFLOWS\x10\x02\x12$\n TASK_REACHABILITY_OPEN_WORKFLOWS\x10\x03\x12&\n"TASK_REACHABILITY_CLOSED_WORKFLOWS\x10\x04*\xd1\x01\n\x17\x42uildIdTaskReachability\x12*\n&BUILD_ID_TASK_REACHABILITY_UNSPECIFIED\x10\x00\x12(\n$BUILD_ID_TASK_REACHABILITY_REACHABLE\x10\x01\x12\x34\n0BUILD_ID_TASK_REACHABILITY_CLOSED_WORKFLOWS_ONLY\x10\x02\x12*\n&BUILD_ID_TASK_REACHABILITY_UNREACHABLE\x10\x03*h\n\x15\x44\x65scribeTaskQueueMode\x12(\n$DESCRIBE_TASK_QUEUE_MODE_UNSPECIFIED\x10\x00\x12%\n!DESCRIBE_TASK_QUEUE_MODE_ENHANCED\x10\x01*\x8b\x01\n\x0fRateLimitSource\x12!\n\x1dRATE_LIMIT_SOURCE_UNSPECIFIED\x10\x00\x12\x19\n\x15RATE_LIMIT_SOURCE_API\x10\x01\x12\x1c\n\x18RATE_LIMIT_SOURCE_WORKER\x10\x02\x12\x1c\n\x18RATE_LIMIT_SOURCE_SYSTEM\x10\x03\x42\x86\x01\n\x18io.temporal.api.enums.v1B\x0eTaskQueueProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3'
)

_TASKQUEUEKIND = DESCRIPTOR.enum_types_by_name["TaskQueueKind"]
TaskQueueKind = enum_type_wrapper.EnumTypeWrapper(_TASKQUEUEKIND)
_TASKQUEUETYPE = DESCRIPTOR.enum_types_by_name["TaskQueueType"]
TaskQueueType = enum_type_wrapper.EnumTypeWrapper(_TASKQUEUETYPE)
_TASKREACHABILITY = DESCRIPTOR.enum_types_by_name["TaskReachability"]
TaskReachability = enum_type_wrapper.EnumTypeWrapper(_TASKREACHABILITY)
_BUILDIDTASKREACHABILITY = DESCRIPTOR.enum_types_by_name["BuildIdTaskReachability"]
BuildIdTaskReachability = enum_type_wrapper.EnumTypeWrapper(_BUILDIDTASKREACHABILITY)
_DESCRIBETASKQUEUEMODE = DESCRIPTOR.enum_types_by_name["DescribeTaskQueueMode"]
DescribeTaskQueueMode = enum_type_wrapper.EnumTypeWrapper(_DESCRIBETASKQUEUEMODE)
_RATELIMITSOURCE = DESCRIPTOR.enum_types_by_name["RateLimitSource"]
RateLimitSource = enum_type_wrapper.EnumTypeWrapper(_RATELIMITSOURCE)
TASK_QUEUE_KIND_UNSPECIFIED = 0
TASK_QUEUE_KIND_NORMAL = 1
TASK_QUEUE_KIND_STICKY = 2
TASK_QUEUE_TYPE_UNSPECIFIED = 0
TASK_QUEUE_TYPE_WORKFLOW = 1
TASK_QUEUE_TYPE_ACTIVITY = 2
TASK_QUEUE_TYPE_NEXUS = 3
TASK_REACHABILITY_UNSPECIFIED = 0
TASK_REACHABILITY_NEW_WORKFLOWS = 1
TASK_REACHABILITY_EXISTING_WORKFLOWS = 2
TASK_REACHABILITY_OPEN_WORKFLOWS = 3
TASK_REACHABILITY_CLOSED_WORKFLOWS = 4
BUILD_ID_TASK_REACHABILITY_UNSPECIFIED = 0
BUILD_ID_TASK_REACHABILITY_REACHABLE = 1
BUILD_ID_TASK_REACHABILITY_CLOSED_WORKFLOWS_ONLY = 2
BUILD_ID_TASK_REACHABILITY_UNREACHABLE = 3
DESCRIBE_TASK_QUEUE_MODE_UNSPECIFIED = 0
DESCRIBE_TASK_QUEUE_MODE_ENHANCED = 1
RATE_LIMIT_SOURCE_UNSPECIFIED = 0
RATE_LIMIT_SOURCE_API = 1
RATE_LIMIT_SOURCE_WORKER = 2
RATE_LIMIT_SOURCE_SYSTEM = 3


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\016TaskQueueProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _TASKQUEUEKIND._serialized_start = 65
    _TASKQUEUEKIND._serialized_end = 169
    _TASKQUEUETYPE._serialized_start = 172
    _TASKQUEUETYPE._serialized_end = 307
    _TASKREACHABILITY._serialized_start = 310
    _TASKREACHABILITY._serialized_end = 520
    _BUILDIDTASKREACHABILITY._serialized_start = 523
    _BUILDIDTASKREACHABILITY._serialized_end = 732
    _DESCRIBETASKQUEUEMODE._serialized_start = 734
    _DESCRIBETASKQUEUEMODE._serialized_end = 838
    _RATELIMITSOURCE._serialized_start = 841
    _RATELIMITSOURCE._serialized_end = 980
# @@protoc_insertion_point(module_scope)
