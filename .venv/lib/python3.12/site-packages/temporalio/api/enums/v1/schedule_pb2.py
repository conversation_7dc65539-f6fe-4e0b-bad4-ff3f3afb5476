# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/schedule.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n$temporal/api/enums/v1/schedule.proto\x12\x15temporal.api.enums.v1*\xb0\x02\n\x15ScheduleOverlapPolicy\x12'\n#SCHEDULE_OVERLAP_POLICY_UNSPECIFIED\x10\x00\x12 \n\x1cSCHEDULE_OVERLAP_POLICY_SKIP\x10\x01\x12&\n\"SCHEDULE_OVERLAP_POLICY_BUFFER_ONE\x10\x02\x12&\n\"SCHEDULE_OVERLAP_POLICY_BUFFER_ALL\x10\x03\x12(\n$SCHEDULE_OVERLAP_POLICY_CANCEL_OTHER\x10\x04\x12+\n'SCHEDULE_OVERLAP_POLICY_TERMINATE_OTHER\x10\x05\x12%\n!SCHEDULE_OVERLAP_POLICY_ALLOW_ALL\x10\x06\x42\x85\x01\n\x18io.temporal.api.enums.v1B\rScheduleProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3"
)

_SCHEDULEOVERLAPPOLICY = DESCRIPTOR.enum_types_by_name["ScheduleOverlapPolicy"]
ScheduleOverlapPolicy = enum_type_wrapper.EnumTypeWrapper(_SCHEDULEOVERLAPPOLICY)
SCHEDULE_OVERLAP_POLICY_UNSPECIFIED = 0
SCHEDULE_OVERLAP_POLICY_SKIP = 1
SCHEDULE_OVERLAP_POLICY_BUFFER_ONE = 2
SCHEDULE_OVERLAP_POLICY_BUFFER_ALL = 3
SCHEDULE_OVERLAP_POLICY_CANCEL_OTHER = 4
SCHEDULE_OVERLAP_POLICY_TERMINATE_OTHER = 5
SCHEDULE_OVERLAP_POLICY_ALLOW_ALL = 6


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\rScheduleProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _SCHEDULEOVERLAPPOLICY._serialized_start = 64
    _SCHEDULEOVERLAPPOLICY._serialized_end = 368
# @@protoc_insertion_point(module_scope)
