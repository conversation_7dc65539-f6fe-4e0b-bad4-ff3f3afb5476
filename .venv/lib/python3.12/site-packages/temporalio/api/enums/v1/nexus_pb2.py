# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: temporal/api/enums/v1/nexus.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import enum_type_wrapper

# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(
    b"\n!temporal/api/enums/v1/nexus.proto\x12\x15temporal.api.enums.v1*\xbc\x01\n\x1eNexusHandlerErrorRetryBehavior\x12\x32\n.NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_UNSPECIFIED\x10\x00\x12\x30\n,NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_RETRYABLE\x10\x01\x12\x34\n0NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_NON_RETRYABLE\x10\x02\x42\x82\x01\n\x18io.temporal.api.enums.v1B\nNexusProtoP\x01Z!go.temporal.io/api/enums/v1;enums\xaa\x02\x17Temporalio.Api.Enums.V1\xea\x02\x1aTemporalio::Api::Enums::V1b\x06proto3"
)

_NEXUSHANDLERERRORRETRYBEHAVIOR = DESCRIPTOR.enum_types_by_name[
    "NexusHandlerErrorRetryBehavior"
]
NexusHandlerErrorRetryBehavior = enum_type_wrapper.EnumTypeWrapper(
    _NEXUSHANDLERERRORRETRYBEHAVIOR
)
NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_UNSPECIFIED = 0
NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_RETRYABLE = 1
NEXUS_HANDLER_ERROR_RETRY_BEHAVIOR_NON_RETRYABLE = 2


if _descriptor._USE_C_DESCRIPTORS == False:
    DESCRIPTOR._options = None
    DESCRIPTOR._serialized_options = b"\n\030io.temporal.api.enums.v1B\nNexusProtoP\001Z!go.temporal.io/api/enums/v1;enums\252\002\027Temporalio.Api.Enums.V1\352\002\032Temporalio::Api::Enums::V1"
    _NEXUSHANDLERERRORRETRYBEHAVIOR._serialized_start = 61
    _NEXUSHANDLERERRORRETRYBEHAVIOR._serialized_end = 249
# @@protoc_insertion_point(module_scope)
