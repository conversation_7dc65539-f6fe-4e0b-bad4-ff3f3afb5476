"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import sys
import typing

import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _WorkflowIdReusePolicy:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _WorkflowIdReusePolicyEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _WorkflowIdReusePolicy.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    WORKFLOW_ID_REUSE_POLICY_UNSPECIFIED: _WorkflowIdReusePolicy.ValueType  # 0
    WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE: _WorkflowIdReusePolicy.ValueType  # 1
    """Allow starting a workflow execution using the same workflow id."""
    WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY: (
        _WorkflowIdReusePolicy.ValueType
    )  # 2
    """Allow starting a workflow execution using the same workflow id, only when the last
    execution's final state is one of [terminated, cancelled, timed out, failed].
    """
    WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE: _WorkflowIdReusePolicy.ValueType  # 3
    """Do not permit re-use of the workflow id for this workflow. Future start workflow requests
    could potentially change the policy, allowing re-use of the workflow id.
    """
    WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING: _WorkflowIdReusePolicy.ValueType  # 4
    """This option belongs in WorkflowIdConflictPolicy but is here for backwards compatibility.
    If specified, it acts like ALLOW_DUPLICATE, but also the WorkflowId*Conflict*Policy on
    the request is treated as WORKFLOW_ID_CONFLICT_POLICY_TERMINATE_EXISTING.
    If no running workflow, then the behavior is the same as ALLOW_DUPLICATE.
    """

class WorkflowIdReusePolicy(
    _WorkflowIdReusePolicy, metaclass=_WorkflowIdReusePolicyEnumTypeWrapper
):
    """Defines whether to allow re-using a workflow id from a previously *closed* workflow.
    If the request is denied, the server returns a `WorkflowExecutionAlreadyStartedFailure` error.

    See `WorkflowIdConflictPolicy` for handling workflow id duplication with a *running* workflow.
    """

WORKFLOW_ID_REUSE_POLICY_UNSPECIFIED: WorkflowIdReusePolicy.ValueType  # 0
WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE: WorkflowIdReusePolicy.ValueType  # 1
"""Allow starting a workflow execution using the same workflow id."""
WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY: (
    WorkflowIdReusePolicy.ValueType
)  # 2
"""Allow starting a workflow execution using the same workflow id, only when the last
execution's final state is one of [terminated, cancelled, timed out, failed].
"""
WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE: WorkflowIdReusePolicy.ValueType  # 3
"""Do not permit re-use of the workflow id for this workflow. Future start workflow requests
could potentially change the policy, allowing re-use of the workflow id.
"""
WORKFLOW_ID_REUSE_POLICY_TERMINATE_IF_RUNNING: WorkflowIdReusePolicy.ValueType  # 4
"""This option belongs in WorkflowIdConflictPolicy but is here for backwards compatibility.
If specified, it acts like ALLOW_DUPLICATE, but also the WorkflowId*Conflict*Policy on
the request is treated as WORKFLOW_ID_CONFLICT_POLICY_TERMINATE_EXISTING.
If no running workflow, then the behavior is the same as ALLOW_DUPLICATE.
"""
global___WorkflowIdReusePolicy = WorkflowIdReusePolicy

class _WorkflowIdConflictPolicy:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _WorkflowIdConflictPolicyEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _WorkflowIdConflictPolicy.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    WORKFLOW_ID_CONFLICT_POLICY_UNSPECIFIED: _WorkflowIdConflictPolicy.ValueType  # 0
    WORKFLOW_ID_CONFLICT_POLICY_FAIL: _WorkflowIdConflictPolicy.ValueType  # 1
    """Don't start a new workflow; instead return `WorkflowExecutionAlreadyStartedFailure`."""
    WORKFLOW_ID_CONFLICT_POLICY_USE_EXISTING: _WorkflowIdConflictPolicy.ValueType  # 2
    """Don't start a new workflow; instead return a workflow handle for the running workflow."""
    WORKFLOW_ID_CONFLICT_POLICY_TERMINATE_EXISTING: (
        _WorkflowIdConflictPolicy.ValueType
    )  # 3
    """Terminate the running workflow before starting a new one."""

class WorkflowIdConflictPolicy(
    _WorkflowIdConflictPolicy, metaclass=_WorkflowIdConflictPolicyEnumTypeWrapper
):
    """Defines what to do when trying to start a workflow with the same workflow id as a *running* workflow.
    Note that it is *never* valid to have two actively running instances of the same workflow id.

    See `WorkflowIdReusePolicy` for handling workflow id duplication with a *closed* workflow.
    """

WORKFLOW_ID_CONFLICT_POLICY_UNSPECIFIED: WorkflowIdConflictPolicy.ValueType  # 0
WORKFLOW_ID_CONFLICT_POLICY_FAIL: WorkflowIdConflictPolicy.ValueType  # 1
"""Don't start a new workflow; instead return `WorkflowExecutionAlreadyStartedFailure`."""
WORKFLOW_ID_CONFLICT_POLICY_USE_EXISTING: WorkflowIdConflictPolicy.ValueType  # 2
"""Don't start a new workflow; instead return a workflow handle for the running workflow."""
WORKFLOW_ID_CONFLICT_POLICY_TERMINATE_EXISTING: WorkflowIdConflictPolicy.ValueType  # 3
"""Terminate the running workflow before starting a new one."""
global___WorkflowIdConflictPolicy = WorkflowIdConflictPolicy

class _ParentClosePolicy:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ParentClosePolicyEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _ParentClosePolicy.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PARENT_CLOSE_POLICY_UNSPECIFIED: _ParentClosePolicy.ValueType  # 0
    PARENT_CLOSE_POLICY_TERMINATE: _ParentClosePolicy.ValueType  # 1
    """The child workflow will also terminate"""
    PARENT_CLOSE_POLICY_ABANDON: _ParentClosePolicy.ValueType  # 2
    """The child workflow will do nothing"""
    PARENT_CLOSE_POLICY_REQUEST_CANCEL: _ParentClosePolicy.ValueType  # 3
    """Cancellation will be requested of the child workflow"""

class ParentClosePolicy(
    _ParentClosePolicy, metaclass=_ParentClosePolicyEnumTypeWrapper
):
    """Defines how child workflows will react to their parent completing"""

PARENT_CLOSE_POLICY_UNSPECIFIED: ParentClosePolicy.ValueType  # 0
PARENT_CLOSE_POLICY_TERMINATE: ParentClosePolicy.ValueType  # 1
"""The child workflow will also terminate"""
PARENT_CLOSE_POLICY_ABANDON: ParentClosePolicy.ValueType  # 2
"""The child workflow will do nothing"""
PARENT_CLOSE_POLICY_REQUEST_CANCEL: ParentClosePolicy.ValueType  # 3
"""Cancellation will be requested of the child workflow"""
global___ParentClosePolicy = ParentClosePolicy

class _ContinueAsNewInitiator:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _ContinueAsNewInitiatorEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _ContinueAsNewInitiator.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CONTINUE_AS_NEW_INITIATOR_UNSPECIFIED: _ContinueAsNewInitiator.ValueType  # 0
    CONTINUE_AS_NEW_INITIATOR_WORKFLOW: _ContinueAsNewInitiator.ValueType  # 1
    """The workflow itself requested to continue as new"""
    CONTINUE_AS_NEW_INITIATOR_RETRY: _ContinueAsNewInitiator.ValueType  # 2
    """The workflow continued as new because it is retrying"""
    CONTINUE_AS_NEW_INITIATOR_CRON_SCHEDULE: _ContinueAsNewInitiator.ValueType  # 3
    """The workflow continued as new because cron has triggered a new execution"""

class ContinueAsNewInitiator(
    _ContinueAsNewInitiator, metaclass=_ContinueAsNewInitiatorEnumTypeWrapper
): ...

CONTINUE_AS_NEW_INITIATOR_UNSPECIFIED: ContinueAsNewInitiator.ValueType  # 0
CONTINUE_AS_NEW_INITIATOR_WORKFLOW: ContinueAsNewInitiator.ValueType  # 1
"""The workflow itself requested to continue as new"""
CONTINUE_AS_NEW_INITIATOR_RETRY: ContinueAsNewInitiator.ValueType  # 2
"""The workflow continued as new because it is retrying"""
CONTINUE_AS_NEW_INITIATOR_CRON_SCHEDULE: ContinueAsNewInitiator.ValueType  # 3
"""The workflow continued as new because cron has triggered a new execution"""
global___ContinueAsNewInitiator = ContinueAsNewInitiator

class _WorkflowExecutionStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _WorkflowExecutionStatusEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _WorkflowExecutionStatus.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    WORKFLOW_EXECUTION_STATUS_UNSPECIFIED: _WorkflowExecutionStatus.ValueType  # 0
    WORKFLOW_EXECUTION_STATUS_RUNNING: _WorkflowExecutionStatus.ValueType  # 1
    """Value 1 is hardcoded in SQL persistence."""
    WORKFLOW_EXECUTION_STATUS_COMPLETED: _WorkflowExecutionStatus.ValueType  # 2
    WORKFLOW_EXECUTION_STATUS_FAILED: _WorkflowExecutionStatus.ValueType  # 3
    WORKFLOW_EXECUTION_STATUS_CANCELED: _WorkflowExecutionStatus.ValueType  # 4
    WORKFLOW_EXECUTION_STATUS_TERMINATED: _WorkflowExecutionStatus.ValueType  # 5
    WORKFLOW_EXECUTION_STATUS_CONTINUED_AS_NEW: _WorkflowExecutionStatus.ValueType  # 6
    WORKFLOW_EXECUTION_STATUS_TIMED_OUT: _WorkflowExecutionStatus.ValueType  # 7

class WorkflowExecutionStatus(
    _WorkflowExecutionStatus, metaclass=_WorkflowExecutionStatusEnumTypeWrapper
):
    """(-- api-linter: core::0216::synonyms=disabled
    aip.dev/not-precedent: There is WorkflowExecutionState already in another package. --)
    """

WORKFLOW_EXECUTION_STATUS_UNSPECIFIED: WorkflowExecutionStatus.ValueType  # 0
WORKFLOW_EXECUTION_STATUS_RUNNING: WorkflowExecutionStatus.ValueType  # 1
"""Value 1 is hardcoded in SQL persistence."""
WORKFLOW_EXECUTION_STATUS_COMPLETED: WorkflowExecutionStatus.ValueType  # 2
WORKFLOW_EXECUTION_STATUS_FAILED: WorkflowExecutionStatus.ValueType  # 3
WORKFLOW_EXECUTION_STATUS_CANCELED: WorkflowExecutionStatus.ValueType  # 4
WORKFLOW_EXECUTION_STATUS_TERMINATED: WorkflowExecutionStatus.ValueType  # 5
WORKFLOW_EXECUTION_STATUS_CONTINUED_AS_NEW: WorkflowExecutionStatus.ValueType  # 6
WORKFLOW_EXECUTION_STATUS_TIMED_OUT: WorkflowExecutionStatus.ValueType  # 7
global___WorkflowExecutionStatus = WorkflowExecutionStatus

class _PendingActivityState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _PendingActivityStateEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _PendingActivityState.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PENDING_ACTIVITY_STATE_UNSPECIFIED: _PendingActivityState.ValueType  # 0
    PENDING_ACTIVITY_STATE_SCHEDULED: _PendingActivityState.ValueType  # 1
    PENDING_ACTIVITY_STATE_STARTED: _PendingActivityState.ValueType  # 2
    PENDING_ACTIVITY_STATE_CANCEL_REQUESTED: _PendingActivityState.ValueType  # 3
    PENDING_ACTIVITY_STATE_PAUSED: _PendingActivityState.ValueType  # 4
    """PAUSED means activity is paused on the server, and is not running in the worker"""
    PENDING_ACTIVITY_STATE_PAUSE_REQUESTED: _PendingActivityState.ValueType  # 5
    """PAUSE_REQUESTED means activity is currently running on the worker, but paused on the server"""

class PendingActivityState(
    _PendingActivityState, metaclass=_PendingActivityStateEnumTypeWrapper
): ...

PENDING_ACTIVITY_STATE_UNSPECIFIED: PendingActivityState.ValueType  # 0
PENDING_ACTIVITY_STATE_SCHEDULED: PendingActivityState.ValueType  # 1
PENDING_ACTIVITY_STATE_STARTED: PendingActivityState.ValueType  # 2
PENDING_ACTIVITY_STATE_CANCEL_REQUESTED: PendingActivityState.ValueType  # 3
PENDING_ACTIVITY_STATE_PAUSED: PendingActivityState.ValueType  # 4
"""PAUSED means activity is paused on the server, and is not running in the worker"""
PENDING_ACTIVITY_STATE_PAUSE_REQUESTED: PendingActivityState.ValueType  # 5
"""PAUSE_REQUESTED means activity is currently running on the worker, but paused on the server"""
global___PendingActivityState = PendingActivityState

class _PendingWorkflowTaskState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _PendingWorkflowTaskStateEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _PendingWorkflowTaskState.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    PENDING_WORKFLOW_TASK_STATE_UNSPECIFIED: _PendingWorkflowTaskState.ValueType  # 0
    PENDING_WORKFLOW_TASK_STATE_SCHEDULED: _PendingWorkflowTaskState.ValueType  # 1
    PENDING_WORKFLOW_TASK_STATE_STARTED: _PendingWorkflowTaskState.ValueType  # 2

class PendingWorkflowTaskState(
    _PendingWorkflowTaskState, metaclass=_PendingWorkflowTaskStateEnumTypeWrapper
): ...

PENDING_WORKFLOW_TASK_STATE_UNSPECIFIED: PendingWorkflowTaskState.ValueType  # 0
PENDING_WORKFLOW_TASK_STATE_SCHEDULED: PendingWorkflowTaskState.ValueType  # 1
PENDING_WORKFLOW_TASK_STATE_STARTED: PendingWorkflowTaskState.ValueType  # 2
global___PendingWorkflowTaskState = PendingWorkflowTaskState

class _HistoryEventFilterType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _HistoryEventFilterTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _HistoryEventFilterType.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    HISTORY_EVENT_FILTER_TYPE_UNSPECIFIED: _HistoryEventFilterType.ValueType  # 0
    HISTORY_EVENT_FILTER_TYPE_ALL_EVENT: _HistoryEventFilterType.ValueType  # 1
    HISTORY_EVENT_FILTER_TYPE_CLOSE_EVENT: _HistoryEventFilterType.ValueType  # 2

class HistoryEventFilterType(
    _HistoryEventFilterType, metaclass=_HistoryEventFilterTypeEnumTypeWrapper
): ...

HISTORY_EVENT_FILTER_TYPE_UNSPECIFIED: HistoryEventFilterType.ValueType  # 0
HISTORY_EVENT_FILTER_TYPE_ALL_EVENT: HistoryEventFilterType.ValueType  # 1
HISTORY_EVENT_FILTER_TYPE_CLOSE_EVENT: HistoryEventFilterType.ValueType  # 2
global___HistoryEventFilterType = HistoryEventFilterType

class _RetryState:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _RetryStateEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_RetryState.ValueType],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    RETRY_STATE_UNSPECIFIED: _RetryState.ValueType  # 0
    RETRY_STATE_IN_PROGRESS: _RetryState.ValueType  # 1
    RETRY_STATE_NON_RETRYABLE_FAILURE: _RetryState.ValueType  # 2
    RETRY_STATE_TIMEOUT: _RetryState.ValueType  # 3
    RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED: _RetryState.ValueType  # 4
    RETRY_STATE_RETRY_POLICY_NOT_SET: _RetryState.ValueType  # 5
    RETRY_STATE_INTERNAL_SERVER_ERROR: _RetryState.ValueType  # 6
    RETRY_STATE_CANCEL_REQUESTED: _RetryState.ValueType  # 7

class RetryState(_RetryState, metaclass=_RetryStateEnumTypeWrapper): ...

RETRY_STATE_UNSPECIFIED: RetryState.ValueType  # 0
RETRY_STATE_IN_PROGRESS: RetryState.ValueType  # 1
RETRY_STATE_NON_RETRYABLE_FAILURE: RetryState.ValueType  # 2
RETRY_STATE_TIMEOUT: RetryState.ValueType  # 3
RETRY_STATE_MAXIMUM_ATTEMPTS_REACHED: RetryState.ValueType  # 4
RETRY_STATE_RETRY_POLICY_NOT_SET: RetryState.ValueType  # 5
RETRY_STATE_INTERNAL_SERVER_ERROR: RetryState.ValueType  # 6
RETRY_STATE_CANCEL_REQUESTED: RetryState.ValueType  # 7
global___RetryState = RetryState

class _TimeoutType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _TimeoutTypeEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_TimeoutType.ValueType],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    TIMEOUT_TYPE_UNSPECIFIED: _TimeoutType.ValueType  # 0
    TIMEOUT_TYPE_START_TO_CLOSE: _TimeoutType.ValueType  # 1
    TIMEOUT_TYPE_SCHEDULE_TO_START: _TimeoutType.ValueType  # 2
    TIMEOUT_TYPE_SCHEDULE_TO_CLOSE: _TimeoutType.ValueType  # 3
    TIMEOUT_TYPE_HEARTBEAT: _TimeoutType.ValueType  # 4

class TimeoutType(_TimeoutType, metaclass=_TimeoutTypeEnumTypeWrapper): ...

TIMEOUT_TYPE_UNSPECIFIED: TimeoutType.ValueType  # 0
TIMEOUT_TYPE_START_TO_CLOSE: TimeoutType.ValueType  # 1
TIMEOUT_TYPE_SCHEDULE_TO_START: TimeoutType.ValueType  # 2
TIMEOUT_TYPE_SCHEDULE_TO_CLOSE: TimeoutType.ValueType  # 3
TIMEOUT_TYPE_HEARTBEAT: TimeoutType.ValueType  # 4
global___TimeoutType = TimeoutType

class _VersioningBehavior:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _VersioningBehaviorEnumTypeWrapper(
    google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[
        _VersioningBehavior.ValueType
    ],
    builtins.type,
):  # noqa: F821
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    VERSIONING_BEHAVIOR_UNSPECIFIED: _VersioningBehavior.ValueType  # 0
    """Workflow execution does not have a Versioning Behavior and is called Unversioned. This is the
    legacy behavior. An Unversioned workflow's task can go to any Unversioned worker (see
    `WorkerVersioningMode`.)
    User needs to use Patching to keep the new code compatible with prior versions when dealing
    with Unversioned workflows.
    """
    VERSIONING_BEHAVIOR_PINNED: _VersioningBehavior.ValueType  # 1
    """Workflow will start on the Current Deployment Version of its Task Queue, and then
    will be pinned to that same Deployment Version until completion (the Version that
    this Workflow is pinned to is specified in `versioning_info.version`).
    This behavior eliminates most of compatibility concerns users face when changing their code.
    Patching is not needed when pinned workflows code change.
    Can be overridden explicitly via `UpdateWorkflowExecutionOptions` API to move the
    execution to another Deployment Version.
    Activities of `PINNED` workflows are sent to the same Deployment Version. Exception to this
    would be when the activity Task Queue workers are not present in the workflow's Deployment
    Version, in which case the activity will be sent to the Current Deployment Version of its own
    task queue.
    """
    VERSIONING_BEHAVIOR_AUTO_UPGRADE: _VersioningBehavior.ValueType  # 2
    """Workflow will automatically move to the Current Deployment Version of its Task Queue when the
    next workflow task is dispatched.
    AutoUpgrade behavior is suitable for long-running workflows as it allows them to move to the
    latest Deployment Version, but the user still needs to use Patching to keep the new code
    compatible with prior versions for changed workflow types.
    Activities of `AUTO_UPGRADE` workflows are sent to the Deployment Version of the workflow
    execution (as specified in versioning_info.version based on the last completed
    workflow task). Exception to this would be when the activity Task Queue workers are not
    present in the workflow's Deployment Version, in which case, the activity will be sent to a
    different Deployment Version according to the Current Deployment Version of its own task
    queue.
    Workflows stuck on a backlogged activity will still auto-upgrade if the Current Deployment
    Version of their Task Queue changes, without having to wait for the backlogged activity to
    complete on the old Version.
    """

class VersioningBehavior(
    _VersioningBehavior, metaclass=_VersioningBehaviorEnumTypeWrapper
):
    """Versioning Behavior specifies if and how a workflow execution moves between Worker Deployment
    Versions. The Versioning Behavior of a workflow execution is typically specified by the worker
    who completes the first task of the execution, but is also overridable manually for new and
    existing workflows (see VersioningOverride).
    Experimental. Worker Deployments are experimental and might significantly change in the future.
    """

VERSIONING_BEHAVIOR_UNSPECIFIED: VersioningBehavior.ValueType  # 0
"""Workflow execution does not have a Versioning Behavior and is called Unversioned. This is the
legacy behavior. An Unversioned workflow's task can go to any Unversioned worker (see
`WorkerVersioningMode`.)
User needs to use Patching to keep the new code compatible with prior versions when dealing
with Unversioned workflows.
"""
VERSIONING_BEHAVIOR_PINNED: VersioningBehavior.ValueType  # 1
"""Workflow will start on the Current Deployment Version of its Task Queue, and then
will be pinned to that same Deployment Version until completion (the Version that
this Workflow is pinned to is specified in `versioning_info.version`).
This behavior eliminates most of compatibility concerns users face when changing their code.
Patching is not needed when pinned workflows code change.
Can be overridden explicitly via `UpdateWorkflowExecutionOptions` API to move the
execution to another Deployment Version.
Activities of `PINNED` workflows are sent to the same Deployment Version. Exception to this
would be when the activity Task Queue workers are not present in the workflow's Deployment
Version, in which case the activity will be sent to the Current Deployment Version of its own
task queue.
"""
VERSIONING_BEHAVIOR_AUTO_UPGRADE: VersioningBehavior.ValueType  # 2
"""Workflow will automatically move to the Current Deployment Version of its Task Queue when the
next workflow task is dispatched.
AutoUpgrade behavior is suitable for long-running workflows as it allows them to move to the
latest Deployment Version, but the user still needs to use Patching to keep the new code
compatible with prior versions for changed workflow types.
Activities of `AUTO_UPGRADE` workflows are sent to the Deployment Version of the workflow
execution (as specified in versioning_info.version based on the last completed
workflow task). Exception to this would be when the activity Task Queue workers are not
present in the workflow's Deployment Version, in which case, the activity will be sent to a
different Deployment Version according to the Current Deployment Version of its own task
queue.
Workflows stuck on a backlogged activity will still auto-upgrade if the Current Deployment
Version of their Task Queue changes, without having to wait for the backlogged activity to
complete on the old Version.
"""
global___VersioningBehavior = VersioningBehavior
