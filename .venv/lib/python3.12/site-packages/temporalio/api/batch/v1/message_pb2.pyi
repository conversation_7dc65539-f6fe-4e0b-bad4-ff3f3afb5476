"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import sys

import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.field_mask_pb2
import google.protobuf.internal.containers
import google.protobuf.message
import google.protobuf.timestamp_pb2

import temporalio.api.activity.v1.message_pb2
import temporalio.api.common.v1.message_pb2
import temporalio.api.enums.v1.batch_operation_pb2
import temporalio.api.enums.v1.reset_pb2
import temporalio.api.rules.v1.message_pb2
import temporalio.api.workflow.v1.message_pb2

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class BatchOperationInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    JOB_ID_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    START_TIME_FIELD_NUMBER: builtins.int
    CLOSE_TIME_FIELD_NUMBER: builtins.int
    job_id: builtins.str
    """Batch job ID"""
    state: temporalio.api.enums.v1.batch_operation_pb2.BatchOperationState.ValueType
    """Batch operation state"""
    @property
    def start_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """Batch operation start time"""
    @property
    def close_time(self) -> google.protobuf.timestamp_pb2.Timestamp:
        """Batch operation close time"""
    def __init__(
        self,
        *,
        job_id: builtins.str = ...,
        state: temporalio.api.enums.v1.batch_operation_pb2.BatchOperationState.ValueType = ...,
        start_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
        close_time: google.protobuf.timestamp_pb2.Timestamp | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "close_time", b"close_time", "start_time", b"start_time"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "close_time",
            b"close_time",
            "job_id",
            b"job_id",
            "start_time",
            b"start_time",
            "state",
            b"state",
        ],
    ) -> None: ...

global___BatchOperationInfo = BatchOperationInfo

class BatchOperationTermination(google.protobuf.message.Message):
    """BatchOperationTermination sends terminate requests to batch workflows.
    Keep the parameter in sync with temporalio.api.workflowservice.v1.TerminateWorkflowExecutionRequest.
    Ignore first_execution_run_id because this is used for single workflow operation.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DETAILS_FIELD_NUMBER: builtins.int
    IDENTITY_FIELD_NUMBER: builtins.int
    @property
    def details(self) -> temporalio.api.common.v1.message_pb2.Payloads:
        """Serialized value(s) to provide to the termination event"""
    identity: builtins.str
    """The identity of the worker/client"""
    def __init__(
        self,
        *,
        details: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
        identity: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["details", b"details"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "details", b"details", "identity", b"identity"
        ],
    ) -> None: ...

global___BatchOperationTermination = BatchOperationTermination

class BatchOperationSignal(google.protobuf.message.Message):
    """BatchOperationSignal sends signals to batch workflows.
    Keep the parameter in sync with temporalio.api.workflowservice.v1.SignalWorkflowExecutionRequest.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SIGNAL_FIELD_NUMBER: builtins.int
    INPUT_FIELD_NUMBER: builtins.int
    HEADER_FIELD_NUMBER: builtins.int
    IDENTITY_FIELD_NUMBER: builtins.int
    signal: builtins.str
    """The workflow author-defined name of the signal to send to the workflow"""
    @property
    def input(self) -> temporalio.api.common.v1.message_pb2.Payloads:
        """Serialized value(s) to provide with the signal"""
    @property
    def header(self) -> temporalio.api.common.v1.message_pb2.Header:
        """Headers that are passed with the signal to the processing workflow.
        These can include things like auth or tracing tokens.
        """
    identity: builtins.str
    """The identity of the worker/client"""
    def __init__(
        self,
        *,
        signal: builtins.str = ...,
        input: temporalio.api.common.v1.message_pb2.Payloads | None = ...,
        header: temporalio.api.common.v1.message_pb2.Header | None = ...,
        identity: builtins.str = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal["header", b"header", "input", b"input"],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "header",
            b"header",
            "identity",
            b"identity",
            "input",
            b"input",
            "signal",
            b"signal",
        ],
    ) -> None: ...

global___BatchOperationSignal = BatchOperationSignal

class BatchOperationCancellation(google.protobuf.message.Message):
    """BatchOperationCancellation sends cancel requests to batch workflows.
    Keep the parameter in sync with temporalio.api.workflowservice.v1.RequestCancelWorkflowExecutionRequest.
    Ignore first_execution_run_id because this is used for single workflow operation.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client"""
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["identity", b"identity"]
    ) -> None: ...

global___BatchOperationCancellation = BatchOperationCancellation

class BatchOperationDeletion(google.protobuf.message.Message):
    """BatchOperationDeletion sends deletion requests to batch workflows.
    Keep the parameter in sync with temporalio.api.workflowservice.v1.DeleteWorkflowExecutionRequest.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client"""
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["identity", b"identity"]
    ) -> None: ...

global___BatchOperationDeletion = BatchOperationDeletion

class BatchOperationReset(google.protobuf.message.Message):
    """BatchOperationReset sends reset requests to batch workflows.
    Keep the parameter in sync with temporalio.api.workflowservice.v1.ResetWorkflowExecutionRequest.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    OPTIONS_FIELD_NUMBER: builtins.int
    RESET_TYPE_FIELD_NUMBER: builtins.int
    RESET_REAPPLY_TYPE_FIELD_NUMBER: builtins.int
    POST_RESET_OPERATIONS_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client."""
    @property
    def options(self) -> temporalio.api.common.v1.message_pb2.ResetOptions:
        """Describes what to reset to and how. If set, `reset_type` and `reset_reapply_type` are ignored."""
    reset_type: temporalio.api.enums.v1.reset_pb2.ResetType.ValueType
    """Deprecated. Use `options`."""
    reset_reapply_type: temporalio.api.enums.v1.reset_pb2.ResetReapplyType.ValueType
    """Deprecated. Use `options`."""
    @property
    def post_reset_operations(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        temporalio.api.workflow.v1.message_pb2.PostResetOperation
    ]:
        """Operations to perform after the workflow has been reset. These operations will be applied
        to the *new* run of the workflow execution in the order they are provided.
        All operations are applied to the workflow before the first new workflow task is generated
        """
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
        options: temporalio.api.common.v1.message_pb2.ResetOptions | None = ...,
        reset_type: temporalio.api.enums.v1.reset_pb2.ResetType.ValueType = ...,
        reset_reapply_type: temporalio.api.enums.v1.reset_pb2.ResetReapplyType.ValueType = ...,
        post_reset_operations: collections.abc.Iterable[
            temporalio.api.workflow.v1.message_pb2.PostResetOperation
        ]
        | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["options", b"options"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "identity",
            b"identity",
            "options",
            b"options",
            "post_reset_operations",
            b"post_reset_operations",
            "reset_reapply_type",
            b"reset_reapply_type",
            "reset_type",
            b"reset_type",
        ],
    ) -> None: ...

global___BatchOperationReset = BatchOperationReset

class BatchOperationUpdateWorkflowExecutionOptions(google.protobuf.message.Message):
    """BatchOperationUpdateWorkflowExecutionOptions sends UpdateWorkflowExecutionOptions requests to batch workflows.
    Keep the parameters in sync with temporalio.api.workflowservice.v1.UpdateWorkflowExecutionOptionsRequest.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    WORKFLOW_EXECUTION_OPTIONS_FIELD_NUMBER: builtins.int
    UPDATE_MASK_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client."""
    @property
    def workflow_execution_options(
        self,
    ) -> temporalio.api.workflow.v1.message_pb2.WorkflowExecutionOptions:
        """Update Workflow options that were originally specified via StartWorkflowExecution. Partial updates are accepted and controlled by update_mask."""
    @property
    def update_mask(self) -> google.protobuf.field_mask_pb2.FieldMask:
        """Controls which fields from `workflow_execution_options` will be applied.
        To unset a field, set it to null and use the update mask to indicate that it should be mutated.
        """
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
        workflow_execution_options: temporalio.api.workflow.v1.message_pb2.WorkflowExecutionOptions
        | None = ...,
        update_mask: google.protobuf.field_mask_pb2.FieldMask | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "update_mask",
            b"update_mask",
            "workflow_execution_options",
            b"workflow_execution_options",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "identity",
            b"identity",
            "update_mask",
            b"update_mask",
            "workflow_execution_options",
            b"workflow_execution_options",
        ],
    ) -> None: ...

global___BatchOperationUpdateWorkflowExecutionOptions = (
    BatchOperationUpdateWorkflowExecutionOptions
)

class BatchOperationUnpauseActivities(google.protobuf.message.Message):
    """BatchOperationUnpauseActivities sends unpause requests to batch workflows."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    MATCH_ALL_FIELD_NUMBER: builtins.int
    RESET_ATTEMPTS_FIELD_NUMBER: builtins.int
    RESET_HEARTBEAT_FIELD_NUMBER: builtins.int
    JITTER_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client."""
    type: builtins.str
    match_all: builtins.bool
    reset_attempts: builtins.bool
    """Setting this flag will also reset the number of attempts."""
    reset_heartbeat: builtins.bool
    """Setting this flag will also reset the heartbeat details."""
    @property
    def jitter(self) -> google.protobuf.duration_pb2.Duration:
        """If set, the activity will start at a random time within the specified jitter
        duration, introducing variability to the start time.
        """
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
        type: builtins.str = ...,
        match_all: builtins.bool = ...,
        reset_attempts: builtins.bool = ...,
        reset_heartbeat: builtins.bool = ...,
        jitter: google.protobuf.duration_pb2.Duration | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "activity",
            b"activity",
            "jitter",
            b"jitter",
            "match_all",
            b"match_all",
            "type",
            b"type",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "activity",
            b"activity",
            "identity",
            b"identity",
            "jitter",
            b"jitter",
            "match_all",
            b"match_all",
            "reset_attempts",
            b"reset_attempts",
            "reset_heartbeat",
            b"reset_heartbeat",
            "type",
            b"type",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["activity", b"activity"]
    ) -> typing_extensions.Literal["type", "match_all"] | None: ...

global___BatchOperationUnpauseActivities = BatchOperationUnpauseActivities

class BatchOperationTriggerWorkflowRule(google.protobuf.message.Message):
    """BatchOperationTriggerWorkflowRule sends TriggerWorkflowRule requests to batch workflows."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client."""
    id: builtins.str
    """ID of existing rule."""
    @property
    def spec(self) -> temporalio.api.rules.v1.message_pb2.WorkflowRuleSpec:
        """Rule specification to be applied to the workflow without creating a new rule."""
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
        id: builtins.str = ...,
        spec: temporalio.api.rules.v1.message_pb2.WorkflowRuleSpec | None = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "id", b"id", "rule", b"rule", "spec", b"spec"
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "id", b"id", "identity", b"identity", "rule", b"rule", "spec", b"spec"
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["rule", b"rule"]
    ) -> typing_extensions.Literal["id", "spec"] | None: ...

global___BatchOperationTriggerWorkflowRule = BatchOperationTriggerWorkflowRule

class BatchOperationResetActivities(google.protobuf.message.Message):
    """BatchOperationResetActivities sends activity reset requests in a batch.
    NOTE: keep in sync with temporalio.api.workflowservice.v1.ResetActivityRequest
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    MATCH_ALL_FIELD_NUMBER: builtins.int
    RESET_ATTEMPTS_FIELD_NUMBER: builtins.int
    RESET_HEARTBEAT_FIELD_NUMBER: builtins.int
    KEEP_PAUSED_FIELD_NUMBER: builtins.int
    JITTER_FIELD_NUMBER: builtins.int
    RESTORE_ORIGINAL_OPTIONS_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client."""
    type: builtins.str
    match_all: builtins.bool
    reset_attempts: builtins.bool
    """Setting this flag will also reset the number of attempts."""
    reset_heartbeat: builtins.bool
    """Setting this flag will also reset the heartbeat details."""
    keep_paused: builtins.bool
    """If activity is paused, it will remain paused after reset"""
    @property
    def jitter(self) -> google.protobuf.duration_pb2.Duration:
        """If set, the activity will start at a random time within the specified jitter
        duration, introducing variability to the start time.
        """
    restore_original_options: builtins.bool
    """If set, the activity options will be restored to the defaults.
    Default options are then options activity was created with.
    They are part of the first ActivityTaskScheduled event.
    """
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
        type: builtins.str = ...,
        match_all: builtins.bool = ...,
        reset_attempts: builtins.bool = ...,
        reset_heartbeat: builtins.bool = ...,
        keep_paused: builtins.bool = ...,
        jitter: google.protobuf.duration_pb2.Duration | None = ...,
        restore_original_options: builtins.bool = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "activity",
            b"activity",
            "jitter",
            b"jitter",
            "match_all",
            b"match_all",
            "type",
            b"type",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "activity",
            b"activity",
            "identity",
            b"identity",
            "jitter",
            b"jitter",
            "keep_paused",
            b"keep_paused",
            "match_all",
            b"match_all",
            "reset_attempts",
            b"reset_attempts",
            "reset_heartbeat",
            b"reset_heartbeat",
            "restore_original_options",
            b"restore_original_options",
            "type",
            b"type",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["activity", b"activity"]
    ) -> typing_extensions.Literal["type", "match_all"] | None: ...

global___BatchOperationResetActivities = BatchOperationResetActivities

class BatchOperationUpdateActivityOptions(google.protobuf.message.Message):
    """BatchOperationUpdateActivityOptions sends an update-activity-options requests in a batch.
    NOTE: keep in sync with temporalio.api.workflowservice.v1.UpdateActivityRequest
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDENTITY_FIELD_NUMBER: builtins.int
    TYPE_FIELD_NUMBER: builtins.int
    MATCH_ALL_FIELD_NUMBER: builtins.int
    ACTIVITY_OPTIONS_FIELD_NUMBER: builtins.int
    UPDATE_MASK_FIELD_NUMBER: builtins.int
    RESTORE_ORIGINAL_FIELD_NUMBER: builtins.int
    identity: builtins.str
    """The identity of the worker/client."""
    type: builtins.str
    match_all: builtins.bool
    @property
    def activity_options(
        self,
    ) -> temporalio.api.activity.v1.message_pb2.ActivityOptions:
        """Update Activity options. Partial updates are accepted and controlled by update_mask."""
    @property
    def update_mask(self) -> google.protobuf.field_mask_pb2.FieldMask:
        """Controls which fields from `activity_options` will be applied"""
    restore_original: builtins.bool
    """If set, the activity options will be restored to the default.
    Default options are then options activity was created with.
    They are part of the first ActivityTaskScheduled event.
    This flag cannot be combined with any other option; if you supply
    restore_original together with other options, the request will be rejected.
    """
    def __init__(
        self,
        *,
        identity: builtins.str = ...,
        type: builtins.str = ...,
        match_all: builtins.bool = ...,
        activity_options: temporalio.api.activity.v1.message_pb2.ActivityOptions
        | None = ...,
        update_mask: google.protobuf.field_mask_pb2.FieldMask | None = ...,
        restore_original: builtins.bool = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "activity",
            b"activity",
            "activity_options",
            b"activity_options",
            "match_all",
            b"match_all",
            "type",
            b"type",
            "update_mask",
            b"update_mask",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "activity",
            b"activity",
            "activity_options",
            b"activity_options",
            "identity",
            b"identity",
            "match_all",
            b"match_all",
            "restore_original",
            b"restore_original",
            "type",
            b"type",
            "update_mask",
            b"update_mask",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["activity", b"activity"]
    ) -> typing_extensions.Literal["type", "match_all"] | None: ...

global___BatchOperationUpdateActivityOptions = BatchOperationUpdateActivityOptions
