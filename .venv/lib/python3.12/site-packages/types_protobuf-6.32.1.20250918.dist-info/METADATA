Metadata-Version: 2.4
Name: types-protobuf
Version: 6.32.1.20250918
Summary: Typing stubs for protobuf
License-Expression: Apache-2.0
Project-URL: Homepage, https://github.com/python/typeshed
Project-URL: GitHub, https://github.com/python/typeshed
Project-URL: Changes, https://github.com/typeshed-internal/stub_uploader/blob/main/data/changelogs/protobuf.md
Project-URL: Issue tracker, https://github.com/python/typeshed/issues
Project-URL: Chat, https://gitter.im/python/typing
Classifier: Programming Language :: Python :: 3
Classifier: Typing :: Stubs Only
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Dynamic: license-file

## Typing stubs for protobuf

This is a [type stub package](https://typing.python.org/en/latest/tutorials/external_libraries.html)
for the [`protobuf`](https://github.com/protocolbuffers/protobuf) package. It can be used by type checkers
to check code that uses `protobuf`. This version of
`types-protobuf` aims to provide accurate annotations for
`protobuf~=6.32.1`.

Partially generated using [mypy-protobuf==3.6.0](https://github.com/nipunn1313/mypy-protobuf/tree/v3.6.0) and libprotoc 31.1 on [protobuf v32.1](https://github.com/protocolbuffers/protobuf/releases/tag/v32.1) (python `protobuf==6.32.1`).

This stub package is marked as [partial](https://typing.python.org/en/latest/spec/distributing.html#partial-stub-packages).
If you find that annotations are missing, feel free to contribute and help complete them.


This package is part of the [typeshed project](https://github.com/python/typeshed).
All fixes for types and metadata should be contributed there.
See [the README](https://github.com/python/typeshed/blob/main/README.md)
for more details. The source for this package can be found in the
[`stubs/protobuf`](https://github.com/python/typeshed/tree/main/stubs/protobuf)
directory.

This package was tested with the following type checkers:
* [mypy](https://github.com/python/mypy/) 1.18.1
* [pyright](https://github.com/microsoft/pyright) 1.1.405

It was generated from typeshed commit
[`add4e85823fe0a7e45c51d6d4f3ef46041593bc8`](https://github.com/python/typeshed/commit/add4e85823fe0a7e45c51d6d4f3ef46041593bc8).
