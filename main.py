import asyncio
from dotenv import load_dotenv

# Import all agent modules to register their tools
from agents.master_agent import agent as master_agent_module
from agents.poet_agent import agent as poet_agent_module
from agents.workspace_agent import agent as workspace_agent_module

# Import other agent modules here as you create them

from shared.mcp_server import main_mcp

load_dotenv()


async def setup():
    """Setup function to initialize any async components if needed."""
    pass


if __name__ == "__main__":
    asyncio.run(setup())
    main_mcp.run()
