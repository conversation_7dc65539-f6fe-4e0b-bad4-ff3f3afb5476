import asyncio
from fastmcp import FastMCP
from dotenv import load_dotenv

from agents.master_agent.agent import master_agent
from agents.poet_agent.agent import poet_agent

load_dotenv()

main_mcp = FastMCP(name="TagRabbit MCP Server")


async def run_master_agent(prompt: str) -> str:
    """Run the master agent with the given prompt and return the response."""
    result = await master_agent.run(prompt)
    return result.output


async def run_poet_agent(prompt: str) -> str:
    """Run the poet agent with the given prompt and return the response."""
    result = await poet_agent.run(prompt)
    return result.output


async def setup():
    # Register agent functions as MCP tools
    main_mcp.tool(run_master_agent)
    main_mcp.tool(run_poet_agent)


if __name__ == "__main__":
    asyncio.run(setup())
    main_mcp.run()
