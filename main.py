import asyncio
from dotenv import load_dotenv

# Auto-discover and import all agent modules
import os
import importlib


def auto_import_agents():
    """Automatically import all agent modules to register their tools."""
    agents_dir = "agents"
    for agent_folder in os.listdir(agents_dir):
        agent_path = os.path.join(agents_dir, agent_folder)
        if os.path.isdir(agent_path) and not agent_folder.startswith("__"):
            try:
                # Import the agent module to trigger tool registration
                importlib.import_module(f"agents.{agent_folder}.agent")
                print(f"✓ Loaded agent: {agent_folder}")
            except ImportError as e:
                print(f"⚠ Failed to load agent {agent_folder}: {e}")


# Auto-import all agents
auto_import_agents()

from shared.mcp_server import main_mcp

load_dotenv()


async def setup():
    """Setup function to initialize any async components if needed."""
    pass


if __name__ == "__main__":
    asyncio.run(setup())
    main_mcp.run()
