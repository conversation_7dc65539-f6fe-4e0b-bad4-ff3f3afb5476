import asyncio
from fastmcp import FastMCP
from dotenv import load_dotenv

from agents.master_agent.agent import master_agent
from agents.poet_agent.agent import poet_agent

load_dotenv()

main_mcp = FastMCP(name="TagRabbit MCP Server")


async def setup():
    await main_mcp.import_server(master_agent, prefix="master")
    # await main_mcp.import_server(poet_agent, prefix="poet")


if __name__ == "__main__":
    asyncio.run(setup())
    main_mcp.run()
